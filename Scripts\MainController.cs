// MainController.cs
using Godot;
using System; // Required for System.Math.Sqrt, or use Godot.Mathf.Sqrt
using System.Collections.Generic;

public partial class MainController : Control
{
    // --- UI Element References ---
    [Export] public OptionButton WireTypeDropdown { get; set; }
    [Export] public OptionButton AngleDropdown { get; set; }
    [Export] public HSlider AngleSlider { get; set; }
    [Export] public Label AngleValueLabel { get; set; }
    // Add reference for the SpinBox - assign this in the editor or use GetNode
    [Export] public SpinBox QuantitySpinBox { get; set; } // << NEW: Export for easy assignment
    [Export] public Label BendRadiusLabel { get; set; }
    [Export] public Label ArcLengthLabel { get; set; }
    [Export] public Label TotalLengthLabel { get; set; }
    [Export] public VisualizationDrawer VisualizationAreaNode { get; set; }

    [ExportGroup("Units")]
    [Export] public CheckButton UnitSelector { get; set; }
    [Export] public bool DisplayInMillimeters { get; set; } = false;

    private const float MmToInches = 0.0393701f;

    // --- Internal State for Wire Properties ---
    public Dictionary<string, WireData> WireTypes { get; private set; } = new Dictionary<string, WireData>();
    public WireData CurrentWireData { get; private set; }
    public float CurrentBendAngleDegrees { get; private set; } = 0.0f;
    private int _currentQuantityOfWires = 1; // Internal state, driven by SpinBox

    public float BendRadius { get; private set; } = 0.0f;
    public float ArcLength { get; private set; } = 0.0f;
    public float StraightSegmentLength { get; private set; } = 50.0f;


    private float CalculateEffectiveBundleDiameter(float singleWireDiameterMm, int quantity)
    {
        if (quantity <= 0) return singleWireDiameterMm; // Default to single if invalid
        if (quantity == 1) return singleWireDiameterMm;
        if (quantity == 2) return singleWireDiameterMm * 2.0f;
        if (quantity == 3) return singleWireDiameterMm * 2.155f;
        if (quantity == 4) return singleWireDiameterMm * 2.414f;
        return singleWireDiameterMm * Mathf.Sqrt(quantity) * 1.15f; // Placeholder for >4
    }

    public override void _Ready()
    {
        GD.Print("--- MainController _Ready() START ---");

        // Validate exported nodes
        if (WireTypeDropdown == null) GD.PrintErr("WireTypeDropdown not assigned!");
        if (AngleSlider == null) GD.PrintErr("AngleSlider not assigned!");
        if (AngleValueLabel == null) GD.PrintErr("AngleValueLabel not assigned!");
        if (QuantitySpinBox == null) GD.PrintErr("QuantitySpinBox not assigned!"); // << NEW VALIDATION
        if (BendRadiusLabel == null) GD.PrintErr("BendRadiusLabel not assigned!");
        // ... rest of validations ...

        // Populate Wire Types Dropdown
        WireTypes.Add("Copper_Solid_12AWG", new WireData("Copper Solid 12AWG", 2.05f, 6.0f));
        WireTypes.Add("Copper_Stranded_12AWG", new WireData("Copper Stranded 12AWG", 2.32f, 3.0f));
        WireTypes.Add("Coaxial_RG6", new WireData("Coaxial RG6", 7.0f, 10.0f));
        // ... populate WireTypeDropdown ...
        if (WireTypeDropdown != null)
        {
            int idCounter = 0;
            WireTypeDropdown.Clear();
            foreach (var wireEntry in WireTypes)
            {
                WireTypeDropdown.AddItem(wireEntry.Value.DisplayName, idCounter++);
                WireTypeDropdown.SetItemMetadata(idCounter - 1, wireEntry.Key);
            }
        }

        // Set Units to inches
        if (UnitSelector != null)
        {
            UnitSelector.Pressed += OnUnitSelectorPressed;
        }
        
        // Configure Angle Slider
        if (AngleSlider != null)
        {
            AngleSlider.MinValue = 0;
            AngleSlider.MaxValue = 360;
            AngleSlider.Value = CurrentBendAngleDegrees;
            AngleSlider.ValueChanged += OnAngleSliderValueChanged;
        }
        if (AngleValueLabel != null) AngleValueLabel.Text = $"{CurrentBendAngleDegrees:F1}°";

        // Configure Quantity SpinBox
        if (QuantitySpinBox != null)
        {
            QuantitySpinBox.MinValue = 1;
            QuantitySpinBox.MaxValue = 100; // Adjust as needed
            QuantitySpinBox.Step = 1;
            QuantitySpinBox.Value = _currentQuantityOfWires; // Set initial value
            QuantitySpinBox.ValueChanged += OnQuantitySpinBoxValueChanged;
        }

        // Populate Angle Options Dropdown
        if (AngleDropdown != null)
        {
            AngleDropdown.AddItem("0°", 0);
            AngleDropdown.AddItem("90", 1);
            AngleDropdown.AddItem("180", 2);
            AngleDropdown.AddItem("270", 3);
            AngleDropdown.AddItem("360", 4);
            AngleDropdown.ItemSelected += OnAngleDropdownItemSelected;
        }

        // Connect other signals
        WireTypeDropdown?.Connect(OptionButton.SignalName.ItemSelected, Callable.From<long>(OnWireTypeDropdownItemSelected));

        // Initial selection and calculation
        if (WireTypeDropdown != null && WireTypeDropdown.GetItemCount() > 0)
        {
            WireTypeDropdown.Select(0);
            OnWireTypeDropdownItemSelected(0); // This will trigger UpdateCalculationsAndVisualization
        }
        else
        {
            UpdateCalculationsAndVisualization(); // Call even if dropdown is empty to set defaults/NAs
        }
        AngleDropdown.Select(1); // Default to 90°
        TweenAngleSlider(90.0f);
        GD.Print("--- MainController _Ready() END ---");
    }

    private void OnUnitSelectorPressed()
    {
        DisplayInMillimeters = !DisplayInMillimeters;
        UpdateCalculationsAndVisualization();
    }


    private void OnQuantitySpinBoxValueChanged(double value)
    {
        _currentQuantityOfWires = (int)value;
        GD.Print($"Quantity of wires changed to: {_currentQuantityOfWires}");
        UpdateCalculationsAndVisualization();
    }

    private void OnWireTypeDropdownItemSelected(long index)
    {
        // ... (existing logic to set CurrentWireData based on dropdown index and metadata) ...
        if (WireTypeDropdown == null || index < 0 || index >= WireTypeDropdown.GetItemCount())
        { CurrentWireData = null; }
        else
        {
            var metadata = WireTypeDropdown.GetItemMetadata((int)index);
            if (metadata.VariantType != Variant.Type.Nil)
            {
                string selectedKey = metadata.AsString();
                if (WireTypes.TryGetValue(selectedKey, out WireData selectedWire)) { CurrentWireData = selectedWire; }
                else { CurrentWireData = null; GD.PrintErr($"Wire type key '{selectedKey}' NOT FOUND!"); }
            }
            else { CurrentWireData = null; GD.PrintErr($"Metadata for index {index} is NIL."); }
        }
        UpdateCalculationsAndVisualization();
    }

    private void OnAngleSliderValueChanged(double value)
    {
        CurrentBendAngleDegrees = (float)value;
        if (AngleValueLabel != null) AngleValueLabel.Text = $"{CurrentBendAngleDegrees:F1}°";
        UpdateCalculationsAndVisualization();
    }

    // public override void _Notification(int what)
    // {
    //     if (what == NotificationPropertyListChanged)
    //     {
    //         // This primarily handles changes to DisplayInMillimeters from the editor
    //         UpdateCalculationsAndVisualization();
    //         VisualizationAreaNode?.QueueRedraw();
    //     }
    // }

    public void UpdateCalculationsAndVisualization()
    {
        if (CurrentWireData == null)
        {
            if (BendRadiusLabel != null) BendRadiusLabel.Text = "Min Bend Radius: N/A";
            if (ArcLengthLabel != null) ArcLengthLabel.Text = "Arc Length: N/A";
            if (TotalLengthLabel != null) TotalLengthLabel.Text = "Est. Total Length: N/A";
            if (AngleValueLabel != null) AngleValueLabel.Text = $"{CurrentBendAngleDegrees:F1}°";
            VisualizationAreaNode?.QueueRedraw();
            return;
        }

        float singleWireDiameterMm = CurrentWireData.DiameterMm;
        float baseMultiplier = CurrentWireData.MinBendRadiusMultiplier;

        // Use the _currentQuantityOfWires from the SpinBox's value for calculations
        float effectiveDiameterMm = CalculateEffectiveBundleDiameter(singleWireDiameterMm, _currentQuantityOfWires);

        BendRadius = effectiveDiameterMm * baseMultiplier;

        float bendAngleRadians = Mathf.DegToRad(CurrentBendAngleDegrees);
        float centerlineRadiusMm = BendRadius + (effectiveDiameterMm / 2.0f); // Using effective diameter for centerline of bundle
        ArcLength = centerlineRadiusMm * bendAngleRadians;
        float totalLengthMm = 2 * StraightSegmentLength + ArcLength;

        string unitSuffix = DisplayInMillimeters ? "mm" : "in";
        float displayBendRadius = DisplayInMillimeters ? BendRadius : BendRadius * MmToInches;
        float displayArcLength = DisplayInMillimeters ? ArcLength : ArcLength * MmToInches;
        float displayTotalLength = DisplayInMillimeters ? totalLengthMm : totalLengthMm * MmToInches;
        string formatSpecifier = DisplayInMillimeters ? "F1" : "F2";

        if (BendRadiusLabel != null) BendRadiusLabel.Text = $"Min Bend Radius: {displayBendRadius.ToString(formatSpecifier)} {unitSuffix}";
        if (ArcLengthLabel != null) ArcLengthLabel.Text = $"Arc Length: {displayArcLength.ToString(formatSpecifier)} {unitSuffix}";
        if (TotalLengthLabel != null) TotalLengthLabel.Text = $"Est. Total Length: {displayTotalLength.ToString(formatSpecifier)} {unitSuffix}";
        if (AngleValueLabel != null) AngleValueLabel.Text = $"{CurrentBendAngleDegrees:F1}°";

        VisualizationAreaNode?.QueueRedraw();
    }
    public void TweenAngleSlider(double value)
    {
        Tween tween = GetTree().CreateTween();
        tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(AngleSlider, "value", value, 1.0f);
    }
    public void OnAngleDropdownItemSelected(long index)
    {
        TweenAngleSlider(AngleDropdown.GetItemText((int)index).ToFloat());
    }
}