[gd_resource type="Theme" load_steps=196 format=3 uid="uid://dll8ohandi7wd"]

[ext_resource type="FontFile" uid="uid://dg6qs6ursn3qb" path="res://Fonts/Roboto-Regular.ttf" id="1_t81gj"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8h7o8"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_axjwv"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.261, 0.280914, 0.305804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_j2dqu"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.181922, 0.198184, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_py3gh"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.212878, 0.226466, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4akv6"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.177348, 0.195643, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_a3ra2"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.125333, 0.140079, 0.158509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bae0e"]
content_margin_left = 16.0
content_margin_top = 4.0
content_margin_right = 0.0
content_margin_bottom = 4.0
bg_color = Color(0.109333, 0.122196, 0.138274, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h41ta"]
content_margin_left = 8.0
content_margin_top = 4.8
content_margin_right = 8.0
content_margin_bottom = 4.8
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_62430"]
content_margin_left = 8.0
content_margin_top = 4.8
content_margin_right = 8.0
content_margin_bottom = 4.8
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qwr2j"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_s36ws"]
content_margin_left = 8.0
content_margin_top = 4.8
content_margin_right = 8.0
content_margin_bottom = 4.8
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xr200"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rlqk2"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_p123m"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xpm2m"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.389365, 0.34622, 0.725894, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_cop21"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.258824, 0.552941, 0.819608, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_16egd"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_pqmua"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_thbbb"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.313726, 0.270588, 0.611765, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mi2ky"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7i7xs"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.264943, 0.225292, 0.52875, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ketba"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_dgi60"]
load_path = "res://.godot/imported/Checked.svg-a862258fda55dd948a424cac20f1bee5.ctex"

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_6gmeo"]
load_path = "res://.godot/imported/Checked.svg-a862258fda55dd948a424cac20f1bee5.ctex"

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_416v2"]
load_path = "res://.godot/imported/Unchecked.svg-6da592ca76b3b98bba267fcf713451b3.ctex"

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_j57lj"]
load_path = "res://.godot/imported/Unchecked.svg-6da592ca76b3b98bba267fcf713451b3.ctex"

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7bwqt"]
content_margin_left = 6.0
content_margin_top = 2.0
content_margin_right = 6.0
content_margin_bottom = 2.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_igchq"]
content_margin_left = 6.0
content_margin_top = 2.0
content_margin_right = 6.0
content_margin_bottom = 2.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xeavm"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_jx4fa"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4jxgv"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.218426, 0.235091, 0.255921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bybf4"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f18fv"]
content_margin_left = 8.0
content_margin_top = 4.0
content_margin_right = 8.0
content_margin_bottom = 4.0
bg_color = Color(0.121333, 0.135608, 0.153451, 1)
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uvybr"]
content_margin_left = 8.0
content_margin_top = 4.0
content_margin_right = 8.0
content_margin_bottom = 4.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bx5ef"]
content_margin_left = 8.0
content_margin_top = 8.0
content_margin_right = 8.0
content_margin_bottom = 8.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_jyimv"]
content_margin_left = 0.0
content_margin_top = 4.8
content_margin_right = 0.0
content_margin_bottom = 4.8
bg_color = Color(0.181922, 0.198184, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0btva"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f813g"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ehp7a"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xiqgb"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bhoq8"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_alvbl"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tq6p4"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1gyfd"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tjtrd"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 1, 0.07)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_udjpf"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0, 0, 0, 0.3)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gi41i"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_left = 1.0
expand_margin_top = 1.0
expand_margin_right = 1.0
expand_margin_bottom = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_pns07"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
draw_center = false
border_color = Color(1, 1, 1, 0)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_left = 1.0
expand_margin_top = 1.0
expand_margin_right = 1.0
expand_margin_bottom = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6u3id"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.231938, 0.259226, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_skfcb"]
content_margin_left = 4.0
content_margin_top = 2.0
content_margin_right = 4.0
content_margin_bottom = 2.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_j3vt4"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_cbqot"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_d05hq"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4nvfe"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mo6sh"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hnffj"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3ks2x"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_322bf"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6ewcs"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_saaci"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_83c1s"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8534w"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_s4y7g"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4k2pq"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_n1we3"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k608l"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t6ewx"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ly5ky"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_keqdw"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_16a2h"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_io18e"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8jmtj"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.372549, 0.392157, 0.447059, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fv472"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_p2e3x"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_simb5"]
content_margin_left = 0.0
content_margin_top = 3.0
content_margin_right = 0.0
content_margin_bottom = 3.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_a6xlp"]
content_margin_left = 0.0
content_margin_top = 3.0
content_margin_right = 0.0
content_margin_bottom = 3.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxLine" id="StyleBoxLine_t81gj"]
color = Color(0.235294, 0.243137, 0.266667, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fo6ar"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_llc74"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_v1ymy"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.258824, 0.552941, 0.819608, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fqm5y"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.258824, 0.552941, 0.819608, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6olrd"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_heaif"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7hsnn"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dc18i"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_aig3n"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(1, 1, 1, 0.04)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_isqru"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(1, 1, 1, 0.04)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_o6xom"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7qcq2"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dlenu"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bc7m6"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gn6a3"]
content_margin_left = 8.0
content_margin_top = 8.0
content_margin_right = 8.0
content_margin_bottom = 8.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8ybbi"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tovaa"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xog7s"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hy7cb"]
content_margin_left = 8.0
content_margin_top = 4.0
content_margin_right = 8.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hvqbs"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.0853331, 0.0953728, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ol8it"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.376471, 0.392157, 0.45098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rlll1"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0, 0, 0, 0.1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2hdxc"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_se1tx"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_awhqk"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nr4ow"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4wuyu"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_43nco"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ap180"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t7xk7"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k3a2o"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8pwtm"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_203nj"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2t2bq"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4gflx"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qjtb1"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t2wau"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_cowdl"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_iubjl"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_l1pct"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_epmmg"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_sh1ct"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1jc4l"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nvkje"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uqbv0"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nmd2v"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tmxys"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kov8m"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_b4xoj"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_naf5h"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.258824, 0.552941, 0.819608, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fenk3"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_skjlv"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t81gj"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ri60x"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ntq8f"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_q6jc4"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.12549, 0.388235, 0.627451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bya4i"]
content_margin_left = 20.0
content_margin_top = 5.0
content_margin_right = 20.0
content_margin_bottom = 5.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.176471, 0.188235, 0.227451, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_edpyh"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mm2kh"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxLine" id="StyleBoxLine_eqy1m"]
color = Color(0.161157, 0.175563, 0.193568, 1)
grow_begin = -6.0
grow_end = -6.0

[sub_resource type="StyleBoxLine" id="StyleBoxLine_5cp46"]
color = Color(0.161157, 0.175563, 0.193568, 1)
grow_begin = -6.0
grow_end = -6.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6mptn"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)

[sub_resource type="StyleBoxLine" id="StyleBoxLine_kuhp8"]
color = Color(0.161157, 0.175563, 0.193568, 1)
grow_begin = -6.0
grow_end = -6.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_j4u1l"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)
shadow_color = Color(0, 0, 0, 0.3)
shadow_size = 3

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4t0me"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_top = 2.0
expand_margin_bottom = 2.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gxq67"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.313726, 0.270588, 0.611765, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_top = 2.0
expand_margin_bottom = 2.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_67d5t"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3bj05"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_cjeeo"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8fnkg"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_msm3m"]
size = Vector2(0, 0)

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_ywywb"]
size = Vector2(0, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hsu6r"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_width_top = 2
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_jtgbk"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_wfc2l"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_width_top = 2
border_color = Color(0.25, 0.356509, 0.5, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_adfkm"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(1, 1, 1, 0)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_aryg6"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6gmeo"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mdhoi"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k2vmu"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_color = Color(0.393123, 0.379022, 0.527673, 0.302)
corner_radius_top_left = 5
corner_radius_top_right = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_o4c02"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(1, 1, 1, 0)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bxwk3"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_b6isj"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.119333, 0.133373, 0.150921, 1)
border_width_top = 2
border_color = Color(0.25, 0.356509, 0.5, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_50f2d"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.0853331, 0.0953728, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dmkpt"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
draw_center = false
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.376471, 0.392157, 0.45098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_lwpy6"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0, 0, 0, 0.1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8d6dx"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_7ep8k"]
load_path = "res://.godot/imported/Checked.svg-a862258fda55dd948a424cac20f1bee5.ctex"

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_y0mww"]
load_path = "res://.godot/imported/Checked.svg-a862258fda55dd948a424cac20f1bee5.ctex"

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_1pmld"]
load_path = "res://.godot/imported/Unchecked.svg-6da592ca76b3b98bba267fcf713451b3.ctex"

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_dfj4h"]
load_path = "res://.godot/imported/Unchecked.svg-6da592ca76b3b98bba267fcf713451b3.ctex"

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3aajy"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.495302, 0.713709, 0.953261, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_chqpk"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.18, 0.465, 0.75, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fr3i6"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.819608, 0.823529, 0.831373, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_yp5mp"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.819608, 0.823529, 0.831373, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dd8mw"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.816929, 0.832385, 0.851792, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ooxar"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.18, 0.465, 0.75, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_yvcck"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_urr5y"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ijw2i"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.18, 0.465, 0.75, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h4dk7"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.18, 0.465, 0.75, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ttlfy"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.18, 0.465, 0.75, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_65rqr"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_yoieu"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0ooug"]
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_huw2i"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.18, 0.465, 0.75, 0.0941176)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_wsci6"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ir86x"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uj0px"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rl5n6"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oe2r0"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_prrf5"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.372549, 0.392157, 0.447059, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_r6j3k"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k5y8r"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bnn6v"]
content_margin_left = 3.0
content_margin_top = 0.0
content_margin_right = 3.0
content_margin_bottom = 0.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_eqhkr"]
content_margin_left = 3.0
content_margin_top = 0.0
content_margin_right = 3.0
content_margin_bottom = 0.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dgi60"]
content_margin_left = 10.0
content_margin_top = 28.0
content_margin_right = 10.0
content_margin_bottom = 8.0
bg_color = Color(0.194625, 0.206071, 0.257484, 1)
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3
corner_detail = 5
expand_margin_left = 8.0
expand_margin_top = 32.0
expand_margin_right = 8.0
expand_margin_bottom = 6.0
shadow_color = Color(0, 0, 0, 0.184314)
shadow_size = 8
shadow_offset = Vector2(3.775, 4.925)

[resource]
default_font = ExtResource("1_t81gj")
default_font_size = 16
AcceptDialog/styles/panel = SubResource("StyleBoxFlat_8h7o8")
AnimationBezierTrackEdit/colors/focus_color = Color(1, 1, 1, 0)
AnimationBezierTrackEdit/colors/h_line_color = Color(1, 1, 1, 0.12)
AnimationBezierTrackEdit/colors/track_focus_color = Color(1, 1, 1, 0.1)
AnimationBezierTrackEdit/colors/v_line_color = Color(1, 1, 1, 0)
AnimationTimelineEdit/colors/font_primary_color = Color(1, 1, 1, 0.7)
AnimationTimelineEdit/colors/font_secondary_color = Color(1, 1, 1, 0.4)
AnimationTimelineEdit/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTimelineEdit/colors/v_line_primary_color = Color(1, 1, 1, 0.4)
AnimationTimelineEdit/colors/v_line_secondary_color = Color(1, 1, 1, 0.08)
AnimationTimelineEdit/constants/text_primary_margin = 3
AnimationTimelineEdit/constants/text_secondary_margin = 2
AnimationTimelineEdit/constants/v_line_primary_margin = 4
AnimationTimelineEdit/constants/v_line_primary_width = 2
AnimationTimelineEdit/constants/v_line_secondary_margin = 6
AnimationTimelineEdit/constants/v_line_secondary_width = 1
AnimationTimelineEdit/styles/time_available = SubResource("StyleBoxFlat_axjwv")
AnimationTimelineEdit/styles/time_unavailable = SubResource("StyleBoxFlat_j2dqu")
AnimationTrackEdit/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTrackEdit/constants/h_separation = 6
AnimationTrackEdit/styles/focus = SubResource("StyleBoxFlat_py3gh")
AnimationTrackEdit/styles/hover = SubResource("StyleBoxFlat_4akv6")
AnimationTrackEdit/styles/odd = SubResource("StyleBoxFlat_a3ra2")
AnimationTrackEditGroup/colors/bg_color = Color(0.125333, 0.140079, 0.158509, 1)
AnimationTrackEditGroup/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTrackEditGroup/colors/v_line_color = Color(1, 1, 1, 0)
AnimationTrackEditGroup/constants/h_separation = 8
AnimationTrackEditGroup/styles/header = SubResource("StyleBoxFlat_bae0e")
BottomPanelButton/styles/hover = SubResource("StyleBoxFlat_h41ta")
BottomPanelButton/styles/hover_pressed = SubResource("StyleBoxFlat_62430")
BottomPanelButton/styles/normal = SubResource("StyleBoxFlat_qwr2j")
BottomPanelButton/styles/pressed = SubResource("StyleBoxFlat_s36ws")
Button/colors/font_color = Color(1, 1, 1, 0.7)
Button/colors/font_disabled_color = Color(1, 1, 1, 0.3)
Button/colors/font_focus_color = Color(1, 1, 1, 1)
Button/colors/font_hover_color = Color(1, 1, 1, 1)
Button/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
Button/colors/font_pressed_color = Color(1, 1, 1, 1)
Button/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
Button/colors/icon_normal_color = Color(1, 1, 1, 0.7)
Button/constants/outline_size = 0
Button/styles/disabled = SubResource("StyleBoxFlat_xr200")
Button/styles/disabled_mirrored = SubResource("StyleBoxFlat_rlqk2")
Button/styles/focus = SubResource("StyleBoxFlat_p123m")
Button/styles/hover = SubResource("StyleBoxFlat_xpm2m")
Button/styles/hover_mirrored = SubResource("StyleBoxFlat_cop21")
Button/styles/hover_pressed = SubResource("StyleBoxFlat_16egd")
Button/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_pqmua")
Button/styles/normal = SubResource("StyleBoxFlat_thbbb")
Button/styles/normal_mirrored = SubResource("StyleBoxFlat_mi2ky")
Button/styles/pressed = SubResource("StyleBoxFlat_7i7xs")
Button/styles/pressed_mirrored = SubResource("StyleBoxFlat_ketba")
CheckBox/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckBox/colors/font_pressed_color = Color(1, 1, 1, 0.7)
CheckBox/icons/checked = SubResource("CompressedTexture2D_dgi60")
CheckBox/icons/checked_disabled = SubResource("CompressedTexture2D_6gmeo")
CheckBox/icons/unchecked = SubResource("CompressedTexture2D_416v2")
CheckBox/icons/unchecked_disabled = SubResource("CompressedTexture2D_j57lj")
CheckBox/styles/normal = SubResource("StyleBoxFlat_7bwqt")
CheckBox/styles/normal_mirrored = SubResource("StyleBoxFlat_igchq")
CheckButton/colors/font_focus_color = Color(1, 1, 1, 0.7)
CheckButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckButton/colors/font_pressed_color = Color(1, 1, 1, 0.7)
Editor/colors/background = Color(0.109333, 0.122196, 0.138274, 1)
Editor/colors/box_selection_fill_color = Color(1, 1, 1, 0.12)
Editor/colors/box_selection_stroke_color = Color(1, 1, 1, 0.4)
Editor/colors/dark_color_2 = Color(0, 0, 0, 0.3)
Editor/colors/dark_color_3 = Color(0.109333, 0.122196, 0.138274, 1)
Editor/colors/forward_plus_color = Color(0.54902, 0.752941, 0.392157, 1)
Editor/colors/gl_compatibility_color = Color(0.447059, 0.698039, 0.890196, 1)
Editor/colors/mobile_color = Color(0.862745, 0.482353, 0.584314, 1)
Editor/colors/prop_subsection = Color(1, 1, 1, 0)
Editor/colors/property_color_w = Color(1, 1, 1, 0.8)
Editor/colors/property_color_x = Color(0.882353, 0.384314, 0.466667, 1)
Editor/colors/property_color_y = Color(0.764706, 0.937255, 0.396078, 1)
Editor/colors/property_color_z = Color(0.415686, 0.670588, 0.964706, 1)
Editor/colors/warning_color = Color(0.831373, 0.780392, 0.623529, 1)
Editor/constants/top_bar_separation = 4
Editor/constants/window_border_margin = 4
EditorAbout/styles/panel = SubResource("StyleBoxFlat_xeavm")
EditorAudioBus/styles/focus = SubResource("StyleBoxFlat_jx4fa")
EditorAudioBus/styles/master = SubResource("StyleBoxFlat_4jxgv")
EditorAudioBus/styles/normal = SubResource("StyleBoxFlat_bybf4")
EditorHelpBitContent/styles/normal = SubResource("StyleBoxFlat_f18fv")
EditorHelpBitTitle/styles/normal = SubResource("StyleBoxFlat_uvybr")
EditorInspector/constants/v_separation = 3
EditorInspector/styles/panel = SubResource("StyleBoxFlat_bx5ef")
EditorInspectorCategory/styles/bg = SubResource("StyleBoxFlat_jyimv")
EditorInspectorSection/constants/h_separation = 4
EditorLogFilterButton/styles/hover = SubResource("StyleBoxFlat_0btva")
EditorLogFilterButton/styles/normal = SubResource("StyleBoxFlat_f813g")
EditorLogFilterButton/styles/pressed = SubResource("StyleBoxFlat_ehp7a")
EditorProperty/colors/property_color = Color(1, 1, 1, 0.6)
EditorProperty/colors/warning_color = Color(0.831373, 0.780392, 0.623529, 1)
EditorProperty/styles/bg = SubResource("StyleBoxFlat_xiqgb")
EditorProperty/styles/bg_selected = SubResource("StyleBoxFlat_bhoq8")
EditorProperty/styles/child_bg = SubResource("StyleBoxFlat_alvbl")
EditorSettingsDialog/styles/panel = SubResource("StyleBoxFlat_tq6p4")
EditorSpinSlider/styles/label_bg = SubResource("StyleBoxFlat_1gyfd")
EditorStyles/colors/movie_writer_icon_hover = Color(1, 1, 1, 0.8)
EditorStyles/colors/movie_writer_icon_hover_pressed = Color(1, 1, 1, 0.8)
EditorStyles/colors/movie_writer_icon_normal = Color(1, 1, 1, 0.7)
EditorStyles/colors/movie_writer_icon_pressed = Color(1, 1, 1, 0.941176)
EditorStyles/styles/FocusViewport = SubResource("StyleBoxFlat_tjtrd")
EditorStyles/styles/Information3dViewport = SubResource("StyleBoxFlat_udjpf")
EditorStyles/styles/LaunchPadMovieMode = SubResource("StyleBoxFlat_gi41i")
EditorStyles/styles/LaunchPadNormal = SubResource("StyleBoxFlat_pns07")
EditorStyles/styles/MovieWriterButtonPressed = SubResource("StyleBoxFlat_6u3id")
EditorValidationPanel/styles/panel = SubResource("StyleBoxFlat_skfcb")
FlatButton/colors/font_color = Color(1, 1, 1, 0.7)
FlatButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
FlatButton/colors/font_focus_color = Color(1, 1, 1, 1)
FlatButton/colors/font_hover_color = Color(1, 1, 1, 1)
FlatButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
FlatButton/colors/font_pressed_color = Color(1, 1, 1, 1)
FlatButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
FlatButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
FlatButton/styles/disabled = SubResource("StyleBoxFlat_j3vt4")
FlatButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_cbqot")
FlatButton/styles/hover = SubResource("StyleBoxFlat_d05hq")
FlatButton/styles/hover_mirrored = SubResource("StyleBoxFlat_4nvfe")
FlatButton/styles/hover_pressed = SubResource("StyleBoxFlat_mo6sh")
FlatButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_hnffj")
FlatButton/styles/normal = SubResource("StyleBoxFlat_3ks2x")
FlatButton/styles/normal_mirrored = SubResource("StyleBoxFlat_322bf")
FlatButton/styles/pressed = SubResource("StyleBoxFlat_6ewcs")
FlatButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_saaci")
FlatMenuButton/colors/font_color = Color(1, 1, 1, 0.7)
FlatMenuButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
FlatMenuButton/colors/font_focus_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_hover_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_pressed_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
FlatMenuButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
FlatMenuButton/styles/disabled = SubResource("StyleBoxFlat_83c1s")
FlatMenuButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_8534w")
FlatMenuButton/styles/focus = SubResource("StyleBoxFlat_s4y7g")
FlatMenuButton/styles/hover = SubResource("StyleBoxFlat_4k2pq")
FlatMenuButton/styles/hover_mirrored = SubResource("StyleBoxFlat_n1we3")
FlatMenuButton/styles/hover_pressed = SubResource("StyleBoxFlat_k608l")
FlatMenuButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_t6ewx")
FlatMenuButton/styles/normal = SubResource("StyleBoxFlat_ly5ky")
FlatMenuButton/styles/normal_mirrored = SubResource("StyleBoxFlat_keqdw")
FlatMenuButton/styles/pressed = SubResource("StyleBoxFlat_16a2h")
FlatMenuButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_io18e")
GraphStateMachine/colors/focus_color = Color(1, 1, 1, 0)
HBoxContainer/constants/separation = 2
HScrollBar/styles/grabber = SubResource("StyleBoxFlat_8jmtj")
HScrollBar/styles/grabber_highlight = SubResource("StyleBoxFlat_fv472")
HScrollBar/styles/grabber_pressed = SubResource("StyleBoxFlat_p2e3x")
HScrollBar/styles/scroll = SubResource("StyleBoxFlat_simb5")
HScrollBar/styles/scroll_focus = SubResource("StyleBoxFlat_a6xlp")
HSeparator/styles/separator = SubResource("StyleBoxLine_t81gj")
HSplitContainer/constants/autohide = 1
HSplitContainer/constants/minimum_grab_thickness = 6
HSplitContainer/constants/separation = 2
InspectorActionButton/constants/h_separation = 8
InspectorActionButton/styles/disabled = SubResource("StyleBoxFlat_fo6ar")
InspectorActionButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_llc74")
InspectorActionButton/styles/hover = SubResource("StyleBoxFlat_v1ymy")
InspectorActionButton/styles/hover_mirrored = SubResource("StyleBoxFlat_fqm5y")
InspectorActionButton/styles/normal = SubResource("StyleBoxFlat_6olrd")
InspectorActionButton/styles/normal_mirrored = SubResource("StyleBoxFlat_heaif")
InspectorActionButton/styles/pressed = SubResource("StyleBoxFlat_7hsnn")
InspectorActionButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_dc18i")
ItemList/colors/guide_color = Color(1, 1, 1, 0)
ItemList/constants/v_separation = 6
ItemList/styles/cursor = SubResource("StyleBoxFlat_aig3n")
ItemList/styles/cursor_unfocused = SubResource("StyleBoxFlat_isqru")
ItemList/styles/focus = SubResource("StyleBoxFlat_o6xom")
ItemList/styles/hovered = SubResource("StyleBoxFlat_7qcq2")
ItemList/styles/hovered_selected = SubResource("StyleBoxFlat_dlenu")
ItemList/styles/hovered_selected_focus = SubResource("StyleBoxFlat_bc7m6")
ItemList/styles/panel = SubResource("StyleBoxFlat_gn6a3")
ItemList/styles/selected = SubResource("StyleBoxFlat_8ybbi")
ItemList/styles/selected_focus = SubResource("StyleBoxFlat_tovaa")
ItemListSecondary/styles/panel = SubResource("StyleBoxFlat_xog7s")
Label/colors/font_color = Color(1, 1, 1, 0.7)
Label/styles/normal = SubResource("StyleBoxFlat_hy7cb")
LineEdit/colors/font_placeholder_color = Color(1, 1, 1, 0.4)
LineEdit/styles/focus = SubResource("StyleBoxFlat_hvqbs")
LineEdit/styles/normal = SubResource("StyleBoxFlat_ol8it")
LineEdit/styles/read_only = SubResource("StyleBoxFlat_rlll1")
MainMenuBar/styles/hover = SubResource("StyleBoxFlat_2hdxc")
MainMenuBar/styles/hover_pressed = SubResource("StyleBoxFlat_se1tx")
MainMenuBar/styles/normal = SubResource("StyleBoxFlat_awhqk")
MainMenuBar/styles/pressed = SubResource("StyleBoxFlat_nr4ow")
MainScreenButton/styles/hover = SubResource("StyleBoxFlat_4wuyu")
MainScreenButton/styles/hover_mirrored = SubResource("StyleBoxFlat_43nco")
MainScreenButton/styles/hover_pressed = SubResource("StyleBoxFlat_ap180")
MainScreenButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_t7xk7")
MainScreenButton/styles/normal = SubResource("StyleBoxFlat_k3a2o")
MainScreenButton/styles/normal_mirrored = SubResource("StyleBoxFlat_8pwtm")
MainScreenButton/styles/pressed = SubResource("StyleBoxFlat_203nj")
MainScreenButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_2t2bq")
MenuButton/styles/disabled = SubResource("StyleBoxFlat_4gflx")
MenuButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_qjtb1")
MenuButton/styles/focus = SubResource("StyleBoxFlat_t2wau")
MenuButton/styles/hover = SubResource("StyleBoxFlat_cowdl")
MenuButton/styles/hover_mirrored = SubResource("StyleBoxFlat_iubjl")
MenuButton/styles/hover_pressed = SubResource("StyleBoxFlat_l1pct")
MenuButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_epmmg")
MenuButton/styles/normal = SubResource("StyleBoxFlat_sh1ct")
MenuButton/styles/normal_mirrored = SubResource("StyleBoxFlat_1jc4l")
MenuButton/styles/pressed = SubResource("StyleBoxFlat_nvkje")
MenuButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_uqbv0")
OptionButton/colors/font_color = Color(1, 1, 1, 0.7)
OptionButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
OptionButton/colors/font_focus_color = Color(1, 1, 1, 1)
OptionButton/colors/font_hover_color = Color(1, 1, 1, 1)
OptionButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
OptionButton/colors/font_pressed_color = Color(1, 1, 1, 1)
OptionButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
OptionButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
OptionButton/constants/arrow_margin = 14
OptionButton/styles/disabled = SubResource("StyleBoxFlat_nmd2v")
OptionButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_tmxys")
OptionButton/styles/focus = SubResource("StyleBoxFlat_kov8m")
OptionButton/styles/hover = SubResource("StyleBoxFlat_b4xoj")
OptionButton/styles/hover_mirrored = SubResource("StyleBoxFlat_naf5h")
OptionButton/styles/hover_pressed = SubResource("StyleBoxFlat_fenk3")
OptionButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_skjlv")
OptionButton/styles/normal = SubResource("StyleBoxFlat_t81gj")
OptionButton/styles/normal_mirrored = SubResource("StyleBoxFlat_ri60x")
OptionButton/styles/pressed = SubResource("StyleBoxFlat_ntq8f")
OptionButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_q6jc4")
PanelContainer/styles/panel = SubResource("StyleBoxFlat_bya4i")
PopupDialog/styles/panel = SubResource("StyleBoxFlat_edpyh")
PopupMenu/constants/item_start_padding = 9
PopupMenu/constants/v_separation = 7
PopupMenu/styles/hover = SubResource("StyleBoxFlat_mm2kh")
PopupMenu/styles/labeled_separator_left = SubResource("StyleBoxLine_eqy1m")
PopupMenu/styles/labeled_separator_right = SubResource("StyleBoxLine_5cp46")
PopupMenu/styles/panel = SubResource("StyleBoxFlat_6mptn")
PopupMenu/styles/separator = SubResource("StyleBoxLine_kuhp8")
PopupPanel/styles/panel = SubResource("StyleBoxFlat_j4u1l")
ProgressBar/styles/background = SubResource("StyleBoxFlat_4t0me")
ProgressBar/styles/fill = SubResource("StyleBoxFlat_gxq67")
ProjectSettingsEditor/styles/panel = SubResource("StyleBoxFlat_67d5t")
RichTextLabel/styles/normal = SubResource("StyleBoxFlat_3bj05")
ScrollContainer/styles/focus = SubResource("StyleBoxFlat_cjeeo")
ScrollContainer/styles/panel = SubResource("StyleBoxFlat_8fnkg")
SplitContainer/constants/minimum_grab_thickness = 6
SplitContainer/constants/separation = 3
SplitContainer/icons/h_grabber = SubResource("PlaceholderTexture2D_msm3m")
SplitContainer/icons/v_grabber = SubResource("PlaceholderTexture2D_ywywb")
TabBar/styles/tab_focus = SubResource("StyleBoxFlat_hsu6r")
TabBar/styles/tab_hovered = SubResource("StyleBoxFlat_jtgbk")
TabBar/styles/tab_selected = SubResource("StyleBoxFlat_wfc2l")
TabBar/styles/tab_unselected = SubResource("StyleBoxFlat_adfkm")
TabContainer/styles/panel = SubResource("StyleBoxFlat_aryg6")
TabContainer/styles/tab_focus = SubResource("StyleBoxFlat_6gmeo")
TabContainer/styles/tab_hovered = SubResource("StyleBoxFlat_mdhoi")
TabContainer/styles/tab_selected = SubResource("StyleBoxFlat_k2vmu")
TabContainer/styles/tab_unselected = SubResource("StyleBoxFlat_o4c02")
TabContainer/styles/tabbar_background = SubResource("StyleBoxFlat_bxwk3")
TabContainerOdd/styles/tab_selected = SubResource("StyleBoxFlat_b6isj")
TextEdit/styles/focus = SubResource("StyleBoxFlat_50f2d")
TextEdit/styles/normal = SubResource("StyleBoxFlat_dmkpt")
TextEdit/styles/read_only = SubResource("StyleBoxFlat_lwpy6")
TooltipPanel/styles/panel = SubResource("StyleBoxFlat_8d6dx")
Tree/colors/drop_position_color = Color(1, 1, 1, 0.4)
Tree/colors/font_color = Color(1, 1, 1, 0.7)
Tree/colors/guide_color = Color(1, 1, 1, 0)
Tree/colors/parent_hl_line_color = Color(1, 1, 1, 0.1)
Tree/constants/button_margin = 15
Tree/constants/children_hl_line_width = 0
Tree/constants/draw_guides = 0
Tree/constants/draw_relationship_lines = 1
Tree/constants/h_separation = 4
Tree/constants/inner_item_margin_left = 15
Tree/constants/inner_item_margin_right = 4
Tree/constants/parent_hl_line_width = 1
Tree/constants/relationship_line_width = 0
Tree/constants/v_separation = 10
Tree/font_sizes/title_button_font_size = 14
Tree/icons/checked = SubResource("CompressedTexture2D_7ep8k")
Tree/icons/checked_disabled = SubResource("CompressedTexture2D_y0mww")
Tree/icons/unchecked = SubResource("CompressedTexture2D_1pmld")
Tree/icons/unchecked_disabled = SubResource("CompressedTexture2D_dfj4h")
Tree/styles/button_hover = SubResource("StyleBoxFlat_3aajy")
Tree/styles/button_pressed = SubResource("StyleBoxFlat_chqpk")
Tree/styles/cursor = SubResource("StyleBoxFlat_fr3i6")
Tree/styles/cursor_unfocused = SubResource("StyleBoxFlat_yp5mp")
Tree/styles/custom_button = SubResource("StyleBoxFlat_dd8mw")
Tree/styles/custom_button_hover = SubResource("StyleBoxFlat_ooxar")
Tree/styles/custom_button_pressed = SubResource("StyleBoxFlat_yvcck")
Tree/styles/focus = SubResource("StyleBoxFlat_urr5y")
Tree/styles/hover = SubResource("StyleBoxFlat_ijw2i")
Tree/styles/hovered = SubResource("StyleBoxFlat_h4dk7")
Tree/styles/hovered_dimmed = SubResource("StyleBoxFlat_ttlfy")
Tree/styles/hovered_selected = SubResource("StyleBoxFlat_65rqr")
Tree/styles/hovered_selected_focus = SubResource("StyleBoxFlat_yoieu")
Tree/styles/panel = SubResource("StyleBoxFlat_0ooug")
Tree/styles/selected = SubResource("StyleBoxFlat_huw2i")
Tree/styles/selected_focus = SubResource("StyleBoxFlat_wsci6")
Tree/styles/title_button_hover = SubResource("StyleBoxFlat_ir86x")
Tree/styles/title_button_normal = SubResource("StyleBoxFlat_uj0px")
Tree/styles/title_button_pressed = SubResource("StyleBoxFlat_rl5n6")
TreeSecondary/styles/panel = SubResource("StyleBoxFlat_oe2r0")
VBoxContainer/constants/separation = 0
VScrollBar/styles/grabber = SubResource("StyleBoxFlat_prrf5")
VScrollBar/styles/grabber_highlight = SubResource("StyleBoxFlat_r6j3k")
VScrollBar/styles/grabber_pressed = SubResource("StyleBoxFlat_k5y8r")
VScrollBar/styles/scroll = SubResource("StyleBoxFlat_bnn6v")
VScrollBar/styles/scroll_focus = SubResource("StyleBoxFlat_eqhkr")
VSplitContainer/constants/autohide = 1
VSplitContainer/constants/minimum_grab_thickness = 6
VSplitContainer/constants/separation = 2
Window/styles/embedded_border = SubResource("StyleBoxFlat_dgi60")
