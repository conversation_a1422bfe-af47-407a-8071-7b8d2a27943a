[gd_resource type="Theme" load_steps=66 format=3 uid="uid://bcibt73qths3g"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kv65r"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_711s5"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.261, 0.280914, 0.305804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k6l6x"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.181922, 0.198184, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_01xn8"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.212878, 0.226466, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mqmd2"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.177348, 0.195643, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h5xys"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.125333, 0.140079, 0.158509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kq03c"]
content_margin_left = 16.0
content_margin_top = 4.0
content_margin_right = 0.0
content_margin_bottom = 4.0
bg_color = Color(0.109333, 0.122196, 0.138274, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hll2p"]
content_margin_left = 8.0
content_margin_top = 4.8
content_margin_right = 8.0
content_margin_bottom = 4.8
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dcfsl"]
content_margin_left = 8.0
content_margin_top = 4.8
content_margin_right = 8.0
content_margin_bottom = 4.8
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uiad3"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_asl5a"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_a3moc"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oe7pa"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_57ytr"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.29293, 0.31528, 0.343215, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_simoa"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.202687, 0.220805, 0.243451, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1sxxe"]
content_margin_left = 6.0
content_margin_top = 2.0
content_margin_right = 6.0
content_margin_bottom = 2.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_sh018"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7b6lo"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_vswa4"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.218426, 0.235091, 0.255921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gqd2v"]
content_margin_left = 8.0
content_margin_top = 4.0
content_margin_right = 8.0
content_margin_bottom = 4.0
bg_color = Color(0.121333, 0.135608, 0.153451, 1)
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dxwid"]
content_margin_left = 8.0
content_margin_top = 4.0
content_margin_right = 8.0
content_margin_bottom = 4.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6u0gn"]
content_margin_left = 8.0
content_margin_top = 8.0
content_margin_right = 8.0
content_margin_bottom = 8.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_x2a5j"]
content_margin_left = 0.0
content_margin_top = 4.8
content_margin_right = 0.0
content_margin_bottom = 4.8
bg_color = Color(0.181922, 0.198184, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qakf4"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_je4t5"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bx0ey"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1ni2p"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_msxit"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 1, 0.07)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_i82na"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0, 0, 0, 0.3)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_egrk0"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_left = 1.0
expand_margin_top = 1.0
expand_margin_right = 1.0
expand_margin_bottom = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dkrsl"]
content_margin_left = 6.0
content_margin_top = 4.0
content_margin_right = 6.0
content_margin_bottom = 4.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
draw_center = false
border_color = Color(1, 1, 1, 0)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_left = 1.0
expand_margin_top = 1.0
expand_margin_right = 1.0
expand_margin_bottom = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_44k6j"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.231938, 0.259226, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_swfvm"]
content_margin_left = 4.0
content_margin_top = 2.0
content_margin_right = 4.0
content_margin_bottom = 2.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ctoo2"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.256496, 0.272869, 0.293333, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rux83"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.463615, 0.4877, 0.517804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kxbic"]
content_margin_left = 0.0
content_margin_top = 3.0
content_margin_right = 0.0
content_margin_bottom = 3.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5vyo7"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(1, 1, 1, 0.04)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2rk15"]
content_margin_left = 8.0
content_margin_top = 8.0
content_margin_right = 8.0
content_margin_bottom = 8.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_r1jc0"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6xhs0"]
content_margin_left = 8.0
content_margin_top = 4.0
content_margin_right = 8.0
content_margin_bottom = 4.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_v6yi0"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.0853331, 0.0953728, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_re7yf"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_58bqw"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0, 0, 0, 0.1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxLine" id="StyleBoxLine_jrqur"]
color = Color(0.161157, 0.175563, 0.193568, 1)
grow_begin = -6.0
grow_end = -6.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8rqeh"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k2084"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)
shadow_color = Color(0, 0, 0, 0.3)
shadow_size = 3

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qygfl"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.0875919, 0.0966276, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_top = 2.0
expand_margin_bottom = 2.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_26erj"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.223452, 0.243426, 0.268392, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_top = 2.0
expand_margin_bottom = 2.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2xsss"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_n4x4x"]
size = Vector2(0, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1c2dg"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_width_top = 2
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4h2fv"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1flmk"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_width_top = 2
border_color = Color(0.25, 0.356509, 0.5, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7qa65"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(1, 1, 1, 0)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_m5ykw"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_lmcqk"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f028s"]
content_margin_left = 14.0
content_margin_top = 8.0
content_margin_right = 14.0
content_margin_bottom = 6.0
bg_color = Color(0.119333, 0.133373, 0.150921, 1)
border_width_top = 2
border_color = Color(0.25, 0.356509, 0.5, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kjdif"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8kufp"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oss8c"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(1, 1, 1, 0.04)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_46qep"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ytoxk"]
content_margin_left = 8.0
content_margin_top = 8.0
content_margin_right = 8.0
content_margin_bottom = 8.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_m5r8g"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.0853331, 0.0953728, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_s8xc4"]
content_margin_left = 3.0
content_margin_top = 0.0
content_margin_right = 3.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="GDScript" id="GDScript_hhmc0"]
script/source = "@tool
extends Theme

var base_color : Color
var contrast : float
var scale : float
var dark_theme : bool

func _init() -> void:
	# Editor Settings

	var settings : EditorSettings = EditorInterface.get_editor_settings()

	@warning_ignore('unsafe_cast')
	base_color = settings.get_setting('interface/theme/base_color')
	@warning_ignore('unsafe_cast')
	contrast = settings.get_setting('interface/theme/contrast')
	scale = EditorInterface.get_editor_scale()

	@warning_ignore('unsafe_cast')
	var accent_color : Color = settings.get_setting('interface/theme/accent_color')
	@warning_ignore('unsafe_cast')
	var base_spacing : int = settings.get_setting('interface/theme/base_spacing')
	@warning_ignore('unsafe_cast')
	var extra_spacing : int = settings.get_setting('interface/theme/additional_spacing')
	@warning_ignore('unsafe_cast')
	var corner_radius : int = settings.get_setting('interface/theme/corner_radius')
	@warning_ignore('unsafe_cast')
	var icon_and_font_color : int = settings.get_setting('interface/theme/icon_and_font_color')
	@warning_ignore('unsafe_cast')
	var relationship_line_opacity : float = settings.get_setting('interface/theme/relationship_line_opacity')
	@warning_ignore('unsafe_cast')
	var draw_extra_borders : bool = settings.get_setting('interface/theme/draw_extra_borders')
	@warning_ignore('unsafe_cast')
	var increase_scrollbar_touch_area : bool = settings.get_setting('interface/touchscreen/increase_scrollbar_touch_area')	

	# Globals

	base_spacing = maxi(base_spacing, 2) # Base spacing below 2 looks broken

	var base_margin : float = base_spacing
	var increased_margin : float = base_spacing + extra_spacing * 0.75
	var popup_margin : float = maxf(base_margin * 2.4, 4.0 * scale)

	dark_theme = base_color.get_luminance() < 0.5
	var dark_theme_icon_and_font : bool = dark_theme

	if icon_and_font_color != 0: # ColorMode.AUTO_COLOR
		dark_theme_icon_and_font = icon_and_font_color == 2 # ColorMode.LIGHT

	var mono_color : Color = Color.WHITE if dark_theme else Color.BLACK
	var mono_color_icon_and_font : Color = Color.WHITE if dark_theme_icon_and_font else Color.BLACK

	var extra_border_color_1 : Color = Color(1, 1, 1, 0.4) if dark_theme else Color(0, 0, 0, 0.4)
	var extra_border_color_2 : Color = Color(1, 1, 1, 0.2) if dark_theme else Color(0, 0, 0, 0.2)

	# Ensure minimum contrast with the light theme. The default
	# contrast makes it hard to see the UI elements
	if not dark_theme and contrast < 0 and contrast > -0.4:
		contrast = -0.4

	# Main stylebox that most styleboxes duplicate
	var base_sb : StyleBoxFlat = StyleBoxFlat.new()
	base_sb.bg_color = base_color
	base_sb.set_content_margin_all(base_margin * scale)
	base_sb.set_corner_radius_all(int(corner_radius * scale))

	# Non-transparent buttons will potentially blend worse with background
	# than transparent ones, however this is currently only noticeable on the Close
	# button of editor settings, and it probably shouldn't even exist
	var button_sb : StyleBoxFlat = base_sb.duplicate()
	button_sb.bg_color = _get_base_color(0.3, 0.8)
	if draw_extra_borders:
		_set_border(button_sb, extra_border_color_1, floorf(scale))
	else:
		_set_border(button_sb, _get_base_color(0.5, 0.7), 1, true)
	_set_margin(button_sb, base_margin * 2, base_margin * 1.5, base_margin * 2, base_margin * 1.5)

	var button_hover_sb : StyleBoxFlat = button_sb.duplicate()
	button_hover_sb.bg_color = _get_base_color(0.5, 0.7)
	if draw_extra_borders:
		_set_border(button_hover_sb, extra_border_color_1, floorf(scale))
	else:
		_set_border(button_hover_sb, _get_base_color(0.7, 0.7), 1, true)

	var button_pressed_sb : StyleBoxFlat = button_sb.duplicate()
	button_pressed_sb.bg_color = _get_base_color(0.7, 0.7)
	if draw_extra_borders:
		_set_border(button_pressed_sb, extra_border_color_1, floorf(scale))
	else:
		_set_border(button_pressed_sb, _get_base_color(0.9, 0.7), 1, true)

	var button_disabled_sb : StyleBoxFlat = button_sb.duplicate()
	button_disabled_sb.set_border_width_all(0)
	button_disabled_sb.bg_color = _get_base_color(0.2, 0.7)
	if draw_extra_borders:
		_set_border(button_disabled_sb, extra_border_color_2 * Color(1, 1, 1, 0.5), floorf(scale))

	var flat_button_hover_sb : StyleBoxFlat = base_sb.duplicate()
	_set_margin(flat_button_hover_sb, base_margin * 1.5, base_margin, base_margin * 1.5, base_margin)
	flat_button_hover_sb.bg_color = _get_base_color(0.3, 0.7)
	if draw_extra_borders:
		_set_border(flat_button_hover_sb, extra_border_color_1, floorf(scale))

	var flat_button_pressed_sb : StyleBoxFlat = flat_button_hover_sb.duplicate()
	flat_button_pressed_sb.bg_color = _get_base_color(0.5, 0.7)

	var base_empty_sb : StyleBoxFlat = base_sb.duplicate()
	base_empty_sb.draw_center = false
	base_empty_sb.set_content_margin_all(0)

	var base_empty_wide_sb : StyleBoxFlat = base_sb.duplicate()
	base_empty_wide_sb.draw_center = false
	# Ensure minimum margin for wide flat buttons otherwise the topbar looks broken
	var base_empty_wide_margin : float = maxf(base_margin, 3.0)
	_set_margin(base_empty_wide_sb, base_empty_wide_margin * 1.5, base_empty_wide_margin, base_empty_wide_margin * 1.5, base_empty_wide_margin)

	# Animation editor

	set_color('focus_color', 'AnimationBezierTrackEdit', Color.TRANSPARENT)
	set_color('h_line_color', 'AnimationBezierTrackEdit', mono_color * Color(1, 1, 1, 0.12))
	set_color('track_focus_color', 'AnimationBezierTrackEdit', mono_color * Color(1, 1, 1, 0.1))
	set_color('v_line_color', 'AnimationBezierTrackEdit', Color.TRANSPARENT)

	set_color('font_primary_color', 'AnimationTimelineEdit', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('font_secondary_color', 'AnimationTimelineEdit', mono_color_icon_and_font * Color(1, 1, 1, 0.4))
	set_color('h_line_color', 'AnimationTimelineEdit', Color.TRANSPARENT)
	set_color('v_line_primary_color', 'AnimationTimelineEdit', mono_color * Color(1, 1, 1, 0.4))
	set_color('v_line_secondary_color', 'AnimationTimelineEdit', mono_color * Color(1, 1, 1, 0.08))

	set_constant('text_primary_margin', 'AnimationTimelineEdit', int(base_margin * 0.75 * scale))
	set_constant('text_secondary_margin', 'AnimationTimelineEdit', int(base_margin * 0.5 * scale))
	set_constant('v_line_primary_margin', 'AnimationTimelineEdit', int(base_margin * scale))
	set_constant('v_line_primary_width', 'AnimationTimelineEdit', int(ceilf(2 * scale)))
	set_constant('v_line_secondary_margin', 'AnimationTimelineEdit', int(base_margin * 1.5 * scale))
	set_constant('v_line_secondary_width', 'AnimationTimelineEdit', int(ceilf(scale)))

	var sb : StyleBoxFlat = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.55, 0.7)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_1, floorf(scale))
	set_stylebox('time_available', 'AnimationTimelineEdit', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.2, 0.8)
	set_stylebox('time_unavailable', 'AnimationTimelineEdit', sb)

	set_color('h_line_color', 'AnimationTrackEdit', Color.TRANSPARENT)
	set_constant('h_separation', 'AnimationTrackEdit', int(base_margin * 1.5 * scale))

	sb = base_sb.duplicate()
	sb.draw_center = false
	_set_border(sb, _get_base_color(0.3, 0.6), 2)
	_set_margin(sb, base_margin * 1.5, base_margin, base_margin * 1.5, base_margin)
	set_stylebox('focus', 'AnimationTrackEdit', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.2, 0.9)
	set_stylebox('hover', 'AnimationTrackEdit', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.2)
	set_stylebox('odd', 'AnimationTrackEdit', sb)

	set_color('bg_color', 'AnimationTrackEditGroup', _get_base_color(-0.2))
	set_color('h_line_color', 'AnimationTrackEditGroup', Color.TRANSPARENT)
	set_color('v_line_color', 'AnimationTrackEditGroup', Color.TRANSPARENT)
	set_constant('h_separation', 'AnimationTrackEditGroup', int(base_margin * 2 * scale))

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.6)
	_set_margin(sb, base_margin * 4, base_margin, 0, base_margin)
	set_stylebox('header', 'AnimationTrackEditGroup', sb)

	# Bottom panel

	# Use bigger margin for buttons in bottom panel to make them easier to press
	var empty_sb : StyleBoxFlat = base_empty_sb.duplicate()
	_set_margin(empty_sb, base_margin * 2, base_margin * 1.2, base_margin * 2, base_margin * 1.2)
	set_stylebox('normal', 'BottomPanelButton', base_empty_wide_sb)

	sb = flat_button_hover_sb.duplicate()
	_set_margin(sb, base_margin * 2, base_margin * 1.2, base_margin * 2, base_margin * 1.2)
	set_stylebox('hover', 'BottomPanelButton', sb)
	set_stylebox('pressed', 'BottomPanelButton', sb)

	sb = flat_button_pressed_sb.duplicate()
	_set_margin(sb, base_margin * 2, base_margin * 1.2, base_margin * 2, base_margin * 1.2)
	set_stylebox('hover_pressed', 'BottomPanelButton', sb)

	# Audio Bus

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.2, 0.7)
	set_stylebox('normal', 'EditorAudioBus', sb)
	set_stylebox('focus', 'EditorAudioBus', sb)
	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.35, 0.7)
	set_stylebox('master', 'EditorAudioBus', sb)

	# Button

	set_color('font_color', 'Button', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('font_disabled_color', 'Button', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('font_focus_color', 'Button', mono_color_icon_and_font)
	set_color('font_hover_color', 'Button', mono_color_icon_and_font)
	set_color('font_hover_pressed_color', 'Button', mono_color_icon_and_font)
	set_color('font_pressed_color', 'Button', mono_color_icon_and_font)
	set_color('icon_disabled_color', 'Button', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('icon_normal_color', 'Button', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_constant('outline_size', 'Button', 0)
	set_stylebox('disabled', 'Button', button_disabled_sb)
	set_stylebox('disabled_mirrored', 'Button', button_disabled_sb)
	set_stylebox('focus', 'Button', base_empty_sb)
	set_stylebox('hover', 'Button', button_hover_sb)
	set_stylebox('hover_mirrored', 'Button', button_hover_sb)
	set_stylebox('hover_pressed', 'Button', button_pressed_sb)
	set_stylebox('hover_pressed_mirrored', 'Button', button_pressed_sb)
	set_stylebox('normal', 'Button', button_sb)
	set_stylebox('normal_mirrored', 'Button', button_sb)
	set_stylebox('pressed', 'Button', button_pressed_sb)
	set_stylebox('pressed_mirrored', 'Button', button_pressed_sb)

	# Checkbox

	set_color('font_hover_pressed_color', 'CheckBox', mono_color_icon_and_font)
	set_color('font_pressed_color', 'CheckBox', mono_color_icon_and_font * Color(1, 1, 1, 0.7))

	sb = base_sb.duplicate()
	sb.draw_center = false
	_set_margin(sb, base_margin * 1.5, base_margin * 0.5, base_margin * 1.5, base_margin * 0.5)
	set_stylebox('normal', 'CheckBox', sb)
	set_stylebox('normal_mirrored', 'CheckBox', sb)

	# CheckButton

	set_color('font_focus_color', 'CheckButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('font_hover_pressed_color', 'CheckButton', mono_color_icon_and_font)
	set_color('font_pressed_color', 'CheckButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))

	# Editor

	set_color('background', 'Editor', _get_base_color(-0.6))
	set_color('box_selection_fill_color', 'Editor', mono_color * Color(1, 1, 1, 0.12))
	set_color('box_selection_stroke_color', 'Editor', mono_color * Color(1, 1, 1, 0.4))
	# Ruler in 2D view:
	set_color('dark_color_2', 'Editor', Color(0, 0, 0, 0.3) if dark_theme else Color(1, 1, 1, 0.3))
	# Shortcut tree cell background:
	set_color('dark_color_3', 'Editor', _get_base_color(-0.6))

	set_color('forward_plus_color', 'Editor', Color(0.54902, 0.752941, 0.392157))
	set_color('gl_compatibility_color', 'Editor', Color(0.447059, 0.698039, 0.890196))
	set_color('mobile_color', 'Editor', Color(0.862745, 0.482353, 0.584314))
	set_color('property_color_w', 'Editor', mono_color_icon_and_font * Color(1, 1, 1, 0.8))
	set_color('property_color_x', 'Editor', Color('#E16277') if dark_theme_icon_and_font else Color('#670A18'))
	set_color('property_color_y', 'Editor', Color('#C3EF65') if dark_theme_icon_and_font else Color('#455E10'))
	set_color('property_color_z', 'Editor', Color('#6AABF6') if dark_theme_icon_and_font else Color('#143862'))
	set_color('warning_color', 'Editor', Color('#D4C79F') if dark_theme_icon_and_font else Color('#D47D03'))

	set_color('prop_subsection', 'Editor', Color.TRANSPARENT)
	set_constant('top_bar_separation', 'Editor', int(base_margin * scale))
	set_constant('window_border_margin', 'Editor', int(base_margin * scale))

	# EditorHelpBit

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.3 if dark_theme else -0.7)
	_set_margin(sb, base_margin * 2, base_margin, base_margin * 2, base_margin)
	sb.set_corner_radius_all(0)
	sb.corner_radius_bottom_right = int(corner_radius * scale)
	sb.corner_radius_bottom_left = int(corner_radius * scale)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('normal', 'EditorHelpBitContent', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.55 if dark_theme else -0.9) # Same as secondary tree
	_set_margin(sb, base_margin * 2, base_margin, base_margin * 2, base_margin)
	sb.set_corner_radius_all(0)
	sb.corner_radius_top_right = int(corner_radius * scale)
	sb.corner_radius_top_left = int(corner_radius * scale)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('normal', 'EditorHelpBitTitle', sb)

	# EditorInspector

	empty_sb = base_empty_sb.duplicate()
	empty_sb.set_content_margin_all(base_margin * 2 * scale)
	set_stylebox('panel', 'EditorInspector', empty_sb)
	set_constant('v_separation', 'EditorInspector', int(base_margin * 0.85 * scale))
	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.2, 0.8)
	_set_margin(sb, 0, base_margin * 1.2, 0, base_margin * 1.2)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('bg', 'EditorInspectorCategory', sb)
	set_constant('h_separation', 'EditorInspectorSection', int(base_margin * scale))

	# EditorLogFilterButton

	# Hover and pressed styles are swapped for toggle buttons on purpose
	set_stylebox('hover', 'EditorLogFilterButton', flat_button_pressed_sb)
	set_stylebox('pressed', 'EditorLogFilterButton', flat_button_hover_sb)
	set_stylebox('normal', 'EditorLogFilterButton', base_empty_sb)


	# EditorProperty

	set_color('property_color', 'EditorProperty', mono_color_icon_and_font * Color(1, 1, 1, 0.6))
	set_color('warning_color', 'EditorProperty', get_color('warning_color', 'Editor'))
	set_stylebox('bg', 'EditorProperty', base_empty_sb)
	set_stylebox('bg_selected', 'EditorProperty', base_empty_sb)
	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.8) # Same as LineEdit normal
	sb.set_content_margin_all(base_margin * scale)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('child_bg', 'EditorProperty', sb)

	# EditorSpinSlider

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.8)
	sb.set_content_margin_all(base_margin * scale)
	set_stylebox('label_bg', 'EditorSpinSlider', sb)

	# Viewport

	sb = base_sb.duplicate()
	sb.draw_center = false
	sb.set_corner_radius_all(0)
	_set_border(sb, mono_color * Color(1, 1, 1, 0.07), 2, false)
	set_stylebox('FocusViewport', 'EditorStyles', sb)

	sb = base_sb.duplicate()
	sb.bg_color = Color(0, 0, 0, 0.3) if dark_theme else Color(1, 1, 1, 0.3)
	_set_margin(sb, base_margin * 2, base_margin * 1.5, base_margin * 2, base_margin * 1.5)
	set_stylebox('Information3dViewport', 'EditorStyles', sb)

	# LaunchPad

	set_color('movie_writer_icon_hover', 'EditorStyles', Color(1, 1, 1, 0.8))
	set_color('movie_writer_icon_hover_pressed', 'EditorStyles', Color(1, 1, 1, 0.8))
	set_color('movie_writer_icon_normal', 'EditorStyles', Color(1, 1, 1, 0.7))
	set_color('movie_writer_icon_pressed', 'EditorStyles', Color(1, 1, 1, 0.941176))

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.5)
	_set_margin(sb, base_margin * 1.5, base_margin, base_margin * 1.5, base_margin)
	sb.set_expand_margin_all(scale)
	sb.set_border_width_all(0)
	set_stylebox('LaunchPadMovieMode', 'EditorStyles', sb)

	sb = sb.duplicate()
	sb.draw_center = false
	sb.border_color = Color.TRANSPARENT
	set_stylebox('LaunchPadNormal', 'EditorStyles', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.5)
	sb.set_content_margin_all(0)
	set_stylebox('MovieWriterButtonPressed', 'EditorStyles', sb)

	# EditorValidationPanel

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.5)
	_set_margin(sb, base_margin, base_margin * 0.5, base_margin, base_margin * 0.5)
	set_stylebox('panel', 'EditorValidationPanel', sb)

	# FlatButton

	set_color('font_color', 'FlatButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('font_disabled_color', 'FlatButton', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('font_focus_color', 'FlatButton', mono_color_icon_and_font)
	set_color('font_hover_color', 'FlatButton', mono_color_icon_and_font)
	set_color('font_hover_pressed_color', 'FlatButton', mono_color_icon_and_font)
	set_color('font_pressed_color', 'FlatButton', mono_color_icon_and_font)
	set_color('icon_disabled_color', 'FlatButton', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('icon_normal_color', 'FlatButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))

	set_stylebox('disabled', 'FlatButton', base_empty_wide_sb)
	set_stylebox('disabled_mirrored', 'FlatButton', base_empty_wide_sb)
	set_stylebox('normal', 'FlatButton', base_empty_wide_sb)
	set_stylebox('normal_mirrored', 'FlatButton', base_empty_wide_sb)
	set_stylebox('hover', 'FlatButton', flat_button_hover_sb)
	set_stylebox('hover_mirrored', 'FlatButton', flat_button_hover_sb)
	set_stylebox('hover_pressed', 'FlatButton', flat_button_pressed_sb)
	set_stylebox('hover_pressed_mirrored', 'FlatButton', flat_button_pressed_sb)
	set_stylebox('pressed', 'FlatButton', flat_button_pressed_sb)
	set_stylebox('pressed_mirrored', 'FlatButton', flat_button_pressed_sb)

	# FlatMenuButton

	set_color('font_color', 'FlatMenuButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('font_disabled_color', 'FlatMenuButton', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('font_focus_color', 'FlatMenuButton', mono_color_icon_and_font)
	set_color('font_hover_color', 'FlatMenuButton', mono_color_icon_and_font)
	set_color('font_hover_pressed_color', 'FlatMenuButton', mono_color_icon_and_font)
	set_color('font_pressed_color', 'FlatMenuButton', mono_color_icon_and_font)
	set_color('icon_disabled_color', 'FlatMenuButton', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('icon_normal_color', 'FlatMenuButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))

	set_stylebox('disabled', 'FlatMenuButton', base_empty_wide_sb)
	set_stylebox('disabled_mirrored', 'FlatMenuButton', base_empty_wide_sb)
	set_stylebox('focus', 'FlatMenuButton', base_empty_wide_sb)
	set_stylebox('normal', 'FlatMenuButton', base_empty_wide_sb)
	set_stylebox('normal_mirrored', 'FlatMenuButton', base_empty_wide_sb)
	set_stylebox('hover', 'FlatMenuButton', flat_button_hover_sb)
	set_stylebox('hover_mirrored', 'FlatMenuButton', flat_button_hover_sb)
	set_stylebox('hover_pressed', 'FlatMenuButton', flat_button_pressed_sb)
	set_stylebox('hover_pressed_mirrored', 'FlatMenuButton', flat_button_pressed_sb)
	set_stylebox('pressed', 'FlatMenuButton', flat_button_pressed_sb)
	set_stylebox('pressed_mirrored', 'FlatMenuButton', flat_button_pressed_sb)

	# GraphStateMachine

	set_color('focus_color', 'GraphStateMachine', Color.TRANSPARENT)

	# Box Containers

	set_constant('separation', 'HBoxContainer', int(2 * scale))
	set_constant('separation', 'VBoxContainer', int(2 * scale))

	# Split Containers

	set_constant('autohide', 'HSplitContainer', 1)
	set_constant('minimum_grab_thickness', 'HSplitContainer', int(base_margin * 1.5 * scale))
	set_constant('separation', 'HSplitContainer', int(ceilf(2 * scale)))

	set_constant('autohide', 'VSplitContainer', 1)
	set_constant('minimum_grab_thickness', 'VSplitContainer', int(base_margin * 1.5 * scale))
	set_constant('separation', 'VSplitContainer', int(ceilf(2 * scale)))

	# InspectorActionButton

	set_constant('h_separation', 'InspectorActionButton', int(base_margin * 2 * scale))
	set_stylebox('disabled', 'InspectorActionButton', button_disabled_sb)
	set_stylebox('disabled_mirrored', 'InspectorActionButton', button_disabled_sb)
	set_stylebox('normal', 'InspectorActionButton', button_sb)
	set_stylebox('normal_mirrored', 'InspectorActionButton', button_sb)
	set_stylebox('hover', 'InspectorActionButton', button_hover_sb)
	set_stylebox('hover_mirrored', 'InspectorActionButton', button_hover_sb)
	set_stylebox('pressed', 'InspectorActionButton', button_pressed_sb)
	set_stylebox('pressed_mirrored', 'InspectorActionButton', button_pressed_sb)

	# ItemList

	set_color('guide_color', 'ItemList', Color.TRANSPARENT)
	set_constant('v_separation', 'ItemList', int(base_margin * 1.5 * scale))

	sb = base_sb.duplicate()
	sb.bg_color = mono_color * Color(1, 1, 1, 0.04)
	set_stylebox('cursor', 'ItemList', sb)
	set_stylebox('cursor_unfocused', 'ItemList', sb)
	set_stylebox('focus', 'ItemList', base_empty_sb)

	set_stylebox('hovered', 'ItemList', flat_button_hover_sb)
	set_stylebox('selected', 'ItemList', flat_button_hover_sb)
	set_stylebox('selected_focus', 'ItemList', flat_button_hover_sb)
	set_stylebox('hovered_selected', 'ItemList', flat_button_hover_sb)
	set_stylebox('hovered_selected_focus', 'ItemList', flat_button_hover_sb)

	sb = base_sb.duplicate()
	sb.set_content_margin_all(base_margin * 2 * scale)
	set_stylebox('panel', 'ItemList', sb)

	# Label

	set_color('font_color', 'Label', mono_color_icon_and_font * Color(1, 1, 1, 0.7))

	empty_sb = base_empty_sb.duplicate()
	# Keeping vertical margin low otherwise quick open looks bad
	_set_margin(empty_sb, base_margin * 2, base_margin, base_margin * 2, base_margin)
	set_stylebox('normal', 'Label', empty_sb)

	# LineEdit and TextEdit

	set_color('font_placeholder_color', 'LineEdit', mono_color_icon_and_font * Color(1, 1, 1, 0.4))

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-1.2 if dark_theme else -2.0)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_1, floorf(scale))
	_set_margin(sb, base_margin * 2, base_margin * 0.75, base_margin * 2, base_margin * 0.75)
	set_stylebox('focus', 'LineEdit', sb)
	set_stylebox('focus', 'TextEdit', sb)

	sb = sb.duplicate()
	sb.bg_color = _get_base_color(-0.8 if dark_theme else -1.6)
	set_stylebox('normal', 'LineEdit', sb)
	set_stylebox('normal', 'TextEdit', sb)

	# Using transparent background for readonly otherwise it looks bad in the master audio bus
	sb = sb.duplicate()
	sb.bg_color = Color(0, 0, 0, 0.1) if dark_theme else Color(1, 1, 1, 0.1)
	set_stylebox('read_only', 'LineEdit', sb)
	set_stylebox('read_only', 'TextEdit', sb)

	# MainMenuBar

	set_stylebox('normal', 'MainMenuBar', base_empty_wide_sb)
	set_stylebox('hover', 'MainMenuBar', flat_button_hover_sb)
	set_stylebox('hover_pressed', 'MainMenuBar', flat_button_pressed_sb)
	set_stylebox('pressed', 'MainMenuBar', flat_button_pressed_sb)

	# MainScreenButton

	set_stylebox('normal', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('normal_mirrored', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('hover', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('hover_mirrored', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('hover_pressed', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('hover_pressed_mirrored', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('pressed', 'MainScreenButton', base_empty_wide_sb)
	set_stylebox('pressed_mirrored', 'MainScreenButton', base_empty_wide_sb)

	# MenuButton

	set_stylebox('disabled', 'MenuButton', base_empty_wide_sb)
	set_stylebox('disabled_mirrored', 'MenuButton', base_empty_wide_sb)
	set_stylebox('focus', 'MenuButton', base_empty_wide_sb)
	set_stylebox('normal', 'MenuButton', base_empty_wide_sb)
	set_stylebox('normal_mirrored', 'MenuButton', base_empty_wide_sb)
	set_stylebox('pressed', 'MenuButton', flat_button_pressed_sb)
	set_stylebox('pressed_mirrored', 'MenuButton', flat_button_pressed_sb)
	set_stylebox('hover', 'MenuButton', flat_button_hover_sb)
	set_stylebox('hover_mirrored', 'MenuButton', flat_button_hover_sb)
	set_stylebox('hover_pressed', 'MenuButton', flat_button_hover_sb)
	set_stylebox('hover_pressed_mirrored', 'MenuButton', flat_button_hover_sb)

	# OptionButton

	set_constant('arrow_margin', 'OptionButton', int(base_margin * 3.5))

	set_color('font_color', 'OptionButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('font_disabled_color', 'OptionButton', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('font_focus_color', 'OptionButton', mono_color_icon_and_font)
	set_color('font_hover_color', 'OptionButton', mono_color_icon_and_font)
	set_color('font_hover_pressed_color', 'OptionButton', mono_color_icon_and_font)
	set_color('font_pressed_color', 'OptionButton', mono_color_icon_and_font)
	set_color('icon_disabled_color', 'OptionButton', mono_color_icon_and_font * Color(1, 1, 1, 0.3))
	set_color('icon_normal_color', 'OptionButton', mono_color_icon_and_font * Color(1, 1, 1, 0.7))

	set_stylebox('disabled', 'OptionButton', button_disabled_sb)
	set_stylebox('disabled_mirrored', 'OptionButton', button_disabled_sb)
	set_stylebox('focus', 'OptionButton', base_empty_sb)
	set_stylebox('normal', 'OptionButton', button_sb)
	set_stylebox('normal_mirrored', 'OptionButton', button_sb)
	set_stylebox('pressed', 'OptionButton', button_pressed_sb)
	set_stylebox('pressed_mirrored', 'OptionButton', button_pressed_sb)
	set_stylebox('hover', 'OptionButton', button_hover_sb)
	set_stylebox('hover_mirrored', 'OptionButton', button_hover_sb)
	set_stylebox('hover_pressed', 'OptionButton', button_pressed_sb)
	set_stylebox('hover_pressed_mirrored', 'OptionButton', button_pressed_sb)

	# Popups

	set_constant('item_start_padding', 'PopupMenu', int(popup_margin * scale))
	set_constant('v_separation', 'PopupMenu', int(base_margin * 1.75 * scale))

	set_stylebox('hover', 'PopupMenu', flat_button_hover_sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.8, 0.9)
	sb.set_content_margin_all(int(popup_margin * scale))
	sb.set_corner_radius_all(0)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('panel', 'PopupMenu', sb)

	var line_sb : StyleBoxLine = StyleBoxLine.new()
	line_sb.color = _get_base_color(0.1, 0.8)
	line_sb.grow_begin = base_margin * -1.5 * scale
	line_sb.grow_end = base_margin * -1.5 * scale
	line_sb.thickness = int(ceilf(scale))
	set_stylebox('labeled_separator_left', 'PopupMenu', line_sb)
	set_stylebox('labeled_separator_right', 'PopupMenu', line_sb)
	set_stylebox('separator', 'PopupMenu', line_sb)

	set_stylebox('panel', 'PanelContainer', base_empty_wide_sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.8, 0.9)
	sb.shadow_color = Color(0, 0, 0, 0.3)
	sb.shadow_size = int(base_margin * 0.75 * scale)
	sb.set_content_margin_all(int(popup_margin * scale))
	sb.set_corner_radius_all(0)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('panel', 'PopupPanel', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.8, 0.9)
	sb.set_content_margin_all(0)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('panel', 'TooltipPanel', sb)

	sb = base_sb.duplicate()
	sb.set_content_margin_all(int(popup_margin * scale))
	sb.set_corner_radius_all(0)
	set_stylebox('panel', 'PopupDialog', sb)
	set_stylebox('panel', 'AcceptDialog', sb)

	sb = sb.duplicate()
	sb.bg_color = _get_base_color(-1)
	set_stylebox('panel', 'EditorSettingsDialog', sb)
	set_stylebox('panel', 'ProjectSettingsEditor', sb)
	set_stylebox('panel', 'EditorAbout', sb)

	# ProgressBar

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-1.2, 0.9)
	sb.expand_margin_top = base_margin * 0.5 * scale
	sb.expand_margin_bottom = base_margin * 0.5 * scale
	sb.set_content_margin_all(base_margin * scale)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('background', 'ProgressBar', sb)

	sb = sb.duplicate()
	sb.bg_color = _get_base_color(0.4, 0.8)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_1, floorf(scale))
	set_stylebox('fill', 'ProgressBar', sb)

	# RichTextLabel

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.5)
	sb.set_content_margin_all(base_margin * 1.5 * scale)
	set_stylebox('normal', 'RichTextLabel', sb)

	# ScrollContainer

	set_stylebox('panel', 'ScrollContainer', base_empty_sb)
	set_stylebox('focus', 'ScrollContainer', base_empty_sb)

	# ScrollBar

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(0.5, 0.6)
	_set_border(sb, _get_base_color(), floorf(scale))

	set_stylebox('grabber', 'HScrollBar', sb)
	set_stylebox('grabber', 'VScrollBar', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(1.4, 0.5)

	set_stylebox('grabber_highlight', 'HScrollBar', sb)
	set_stylebox('grabber_highlight', 'VScrollBar', sb)
	set_stylebox('grabber_pressed', 'HScrollBar', sb)
	set_stylebox('grabber_pressed', 'VScrollBar', sb)

	var margin : int = 10 if increase_scrollbar_touch_area else 3

	empty_sb = base_empty_sb.duplicate()
	_set_margin(empty_sb, 0, margin * scale, 0, margin * scale)

	set_stylebox('scroll', 'HScrollBar', empty_sb)
	set_stylebox('scroll_focus', 'HScrollBar', empty_sb)

	empty_sb = empty_sb.duplicate()
	_set_margin(empty_sb, margin * scale, 0, margin * scale, 0)

	set_stylebox('scroll', 'VScrollBar', empty_sb)
	set_stylebox('scroll_focus', 'VScrollBar', empty_sb)

	# SplitContainer

	set_constant('minimum_grab_thickness', 'SplitContainer', int(base_margin * 1.5 * scale))
	set_constant('separation', 'SplitContainer', int(base_margin * 0.75 * scale))

	var empty_texture : PlaceholderTexture2D = PlaceholderTexture2D.new()
	empty_texture.size = Vector2(0, 0)
	set_icon('h_grabber', 'SplitContainer', empty_texture)
	set_icon('v_grabber', 'SplitContainer', empty_texture)

	# TabContainer

	sb = base_sb.duplicate()
	_set_margin(sb, base_margin * 3.5, base_margin * 2, base_margin * 3.5, base_margin * 1.5)
	sb.set_corner_radius_all(0)
	sb.border_width_top = int(2 * scale)
	var col : Color = accent_color
	col.v = 0.5
	col.s = 0.5
	sb.border_color = col
	set_stylebox('tab_selected', 'TabBar', sb)
	set_stylebox('tab_selected', 'TabContainer', sb)

	sb = sb.duplicate()
	sb.bg_color = _get_base_color(-0.35)
	set_stylebox('tab_selected', 'TabContainerOdd', sb)

	sb = sb.duplicate()
	sb.bg_color = base_color
	sb.border_color = accent_color
	set_stylebox('tab_focus', 'TabBar', sb)
	set_stylebox('tab_focus', 'TabContainer', sb)

	sb = sb.duplicate()
	sb.bg_color = Color.TRANSPARENT
	sb.set_border_width_all(0)
	set_stylebox('tab_unselected', 'TabBar', sb)
	set_stylebox('tab_unselected', 'TabContainer', sb)

	sb = sb.duplicate()
	sb.bg_color = _get_base_color(-0.5)
	set_stylebox('tab_hovered', 'TabBar', sb)
	set_stylebox('tab_hovered', 'TabContainer', sb)

	sb = base_sb.duplicate()
	sb.set_content_margin_all(increased_margin * 1.5 * scale)
	sb.set_corner_radius_all(0)
	sb.corner_radius_bottom_right = int(corner_radius * scale)
	sb.corner_radius_bottom_left = int(corner_radius * scale)
	set_stylebox('panel', 'TabContainer', sb)

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-1.0)
	sb.set_content_margin_all(0)
	sb.set_corner_radius_all(0)	
	set_stylebox('tabbar_background', 'TabContainer', sb)

	# Tree

	set_color('drop_position_color', 'Tree', mono_color * Color(1, 1, 1, 0.4))
	set_color('font_color', 'Tree', mono_color_icon_and_font * Color(1, 1, 1, 0.7))
	set_color('guide_color', 'Tree', Color.TRANSPARENT)
	set_color('parent_hl_line_color', 'Tree', mono_color * Color(1, 1, 1, relationship_line_opacity))
	set_constant('children_hl_line_width', 'Tree', 0)
	set_constant('draw_guides', 'Tree', 0)
	set_constant('draw_relationship_lines', 'Tree', 1)
	set_constant('inner_item_margin_left', 'Tree', int(base_margin * scale))
	set_constant('inner_item_margin_right', 'Tree', int(base_margin * scale))
	set_constant('parent_hl_line_width', 'Tree', int(ceilf(scale)))
	set_constant('relationship_line_width', 'Tree', 0)
	set_constant('v_separation', 'Tree', int(base_margin * 0.25 * scale))

	# Using empty stylebox for trees to avoid drawing unnecessary borders in docks.
	# Note that using opaque color that is the same as dock background
	# doesn't work because EditorPropertyResource is using Tree panel
	# stylebox to draw its background as well, making it look broken
	empty_sb = base_empty_sb.duplicate()
	empty_sb.set_content_margin_all(base_margin * 2 * scale)
	set_stylebox('panel', 'Tree', empty_sb)

	# Leaving focus empty for trees and scroll containers because there's no way to
	# make focus indication look not janky when only a part of a dock is highlighted
	set_stylebox('focus', 'Tree', base_empty_sb)

	# Rounded corners look a little janky in tree titles because there's no way to
	# introduce gaps between columns, however not having rounded corners looks even worse
	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-1.2 if dark_theme else -1.8)
	set_stylebox('title_button_hover', 'Tree', sb)
	set_stylebox('title_button_normal', 'Tree', sb)
	set_stylebox('title_button_pressed', 'Tree', sb)

	sb = flat_button_hover_sb.duplicate()
	sb.set_content_margin_all(0)
	set_stylebox('button_hover', 'Tree', sb)
	set_stylebox('hover', 'Tree', sb)
	set_stylebox('hovered_dimmed', 'Tree', sb)
	set_stylebox('custom_button_hover', 'Tree', sb)
	set_stylebox('hovered', 'Tree', sb)
	set_stylebox('selected', 'Tree', sb)
	set_stylebox('selected_focus', 'Tree', sb)

	sb = sb.duplicate()
	sb.bg_color = _get_base_color(0.5, 0.7)
	set_stylebox('hovered_selected', 'Tree', sb)
	set_stylebox('hovered_selected_focus', 'Tree', sb)

	set_stylebox('button_pressed', 'Tree', flat_button_pressed_sb)
	set_stylebox('custom_button_pressed', 'Tree', flat_button_pressed_sb)

	# Cursor is drawn on top of the item so it needs to be transparent
	sb = base_sb.duplicate()
	sb.bg_color = mono_color * Color(1, 1, 1, 0.04)
	set_stylebox('cursor', 'Tree', sb)
	set_stylebox('cursor_unfocused', 'Tree', sb)

	# Sidebars

	sb = base_sb.duplicate()
	sb.bg_color = _get_base_color(-0.55 if dark_theme else -0.9)
	if draw_extra_borders:
		_set_border(sb, extra_border_color_2, floorf(scale))
	set_stylebox('panel', 'TreeSecondary', sb)
	set_stylebox('panel', 'ItemListSecondary', sb)

	# HACKS
	# This section is for workarounds for unthemable UI
	# These could be fixed on the engine side in the future

	# TilesetEditor

	# Tileset editor is using Tree panel for the panel container of expanded view, while minimal theme
	# needs trees to be transparent, so it needs to have an explicitly set style for the theme
	# to still be able to support transparent Trees
	# See https://github.com/godotengine/godot/issues/99118
	(func() -> void:
		# Check to see if we're running in project manager
		if EditorInterface.get_script_editor() == null:
			return

		var tileset_editor : Control = EditorInterface.get_base_control().find_children('', 'TileSetEditor', true, false)[0]
		var expand_panel : Control = tileset_editor.get_child(3)
		var panel_sb : StyleBoxFlat = base_sb.duplicate()
		panel_sb.set_corner_radius_all(0)
		expand_panel.add_theme_stylebox_override('panel', panel_sb)
		var _error : int = tileset_editor.theme_changed.connect(func() -> void:
			expand_panel.add_theme_stylebox_override('panel', panel_sb)
		)
	).call_deferred()

# Lighten base color in dark theme, darken in light theme, clamp
func _get_base_color(brightness_offset: float = 0, saturation_multiplier: float = 1) -> Color:
	var dark : bool = dark_theme if brightness_offset >= 0 else !dark_theme
	var color : Color = Color(base_color)
	color.v = clampf(lerpf(color.v, 1 if dark else 0, absf(contrast * brightness_offset)), 0, 1)
	color.s *= saturation_multiplier
	return color

# Shorthand content margin setter
func _set_margin(sb: StyleBox, left: float, top: float, right: float = left, bottom: float = top) -> void:
	sb.content_margin_left = left * scale
	sb.content_margin_top = top * scale
	sb.content_margin_right = right * scale
	sb.content_margin_bottom = bottom * scale

# Shorthand border setter
func _set_border(sb: StyleBoxFlat, color: Color, width: float = 1, blend: bool = false) -> void:
	sb.border_color = color
	sb.border_blend = blend
	sb.set_border_width_all(int(ceilf(width * scale)))
"

[resource]
AcceptDialog/styles/panel = SubResource("StyleBoxFlat_kv65r")
AnimationBezierTrackEdit/colors/focus_color = Color(1, 1, 1, 0)
AnimationBezierTrackEdit/colors/h_line_color = Color(1, 1, 1, 0.12)
AnimationBezierTrackEdit/colors/track_focus_color = Color(1, 1, 1, 0.1)
AnimationBezierTrackEdit/colors/v_line_color = Color(1, 1, 1, 0)
AnimationTimelineEdit/colors/font_primary_color = Color(1, 1, 1, 0.7)
AnimationTimelineEdit/colors/font_secondary_color = Color(1, 1, 1, 0.4)
AnimationTimelineEdit/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTimelineEdit/colors/v_line_primary_color = Color(1, 1, 1, 0.4)
AnimationTimelineEdit/colors/v_line_secondary_color = Color(1, 1, 1, 0.08)
AnimationTimelineEdit/constants/text_primary_margin = 3
AnimationTimelineEdit/constants/text_secondary_margin = 2
AnimationTimelineEdit/constants/v_line_primary_margin = 4
AnimationTimelineEdit/constants/v_line_primary_width = 2
AnimationTimelineEdit/constants/v_line_secondary_margin = 6
AnimationTimelineEdit/constants/v_line_secondary_width = 1
AnimationTimelineEdit/styles/time_available = SubResource("StyleBoxFlat_711s5")
AnimationTimelineEdit/styles/time_unavailable = SubResource("StyleBoxFlat_k6l6x")
AnimationTrackEdit/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTrackEdit/constants/h_separation = 6
AnimationTrackEdit/styles/focus = SubResource("StyleBoxFlat_01xn8")
AnimationTrackEdit/styles/hover = SubResource("StyleBoxFlat_mqmd2")
AnimationTrackEdit/styles/odd = SubResource("StyleBoxFlat_h5xys")
AnimationTrackEditGroup/colors/bg_color = Color(0.125333, 0.140079, 0.158509, 1)
AnimationTrackEditGroup/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTrackEditGroup/colors/v_line_color = Color(1, 1, 1, 0)
AnimationTrackEditGroup/constants/h_separation = 8
AnimationTrackEditGroup/styles/header = SubResource("StyleBoxFlat_kq03c")
BottomPanelButton/styles/hover = SubResource("StyleBoxFlat_hll2p")
BottomPanelButton/styles/hover_pressed = SubResource("StyleBoxFlat_dcfsl")
BottomPanelButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
BottomPanelButton/styles/pressed = SubResource("StyleBoxFlat_hll2p")
Button/colors/font_color = Color(1, 1, 1, 0.7)
Button/colors/font_disabled_color = Color(1, 1, 1, 0.3)
Button/colors/font_focus_color = Color(1, 1, 1, 1)
Button/colors/font_hover_color = Color(1, 1, 1, 1)
Button/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
Button/colors/font_pressed_color = Color(1, 1, 1, 1)
Button/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
Button/colors/icon_normal_color = Color(1, 1, 1, 0.7)
Button/constants/outline_size = 0
Button/styles/disabled = SubResource("StyleBoxFlat_asl5a")
Button/styles/disabled_mirrored = SubResource("StyleBoxFlat_asl5a")
Button/styles/focus = SubResource("StyleBoxFlat_a3moc")
Button/styles/hover = SubResource("StyleBoxFlat_oe7pa")
Button/styles/hover_mirrored = SubResource("StyleBoxFlat_oe7pa")
Button/styles/hover_pressed = SubResource("StyleBoxFlat_57ytr")
Button/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_57ytr")
Button/styles/normal = SubResource("StyleBoxFlat_simoa")
Button/styles/normal_mirrored = SubResource("StyleBoxFlat_simoa")
Button/styles/pressed = SubResource("StyleBoxFlat_57ytr")
Button/styles/pressed_mirrored = SubResource("StyleBoxFlat_57ytr")
CheckBox/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckBox/colors/font_pressed_color = Color(1, 1, 1, 0.7)
CheckBox/styles/normal = SubResource("StyleBoxFlat_1sxxe")
CheckBox/styles/normal_mirrored = SubResource("StyleBoxFlat_1sxxe")
CheckButton/colors/font_focus_color = Color(1, 1, 1, 0.7)
CheckButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckButton/colors/font_pressed_color = Color(1, 1, 1, 0.7)
Editor/colors/background = Color(0.109333, 0.122196, 0.138274, 1)
Editor/colors/box_selection_fill_color = Color(1, 1, 1, 0.12)
Editor/colors/box_selection_stroke_color = Color(1, 1, 1, 0.4)
Editor/colors/dark_color_2 = Color(0, 0, 0, 0.3)
Editor/colors/dark_color_3 = Color(0.109333, 0.122196, 0.138274, 1)
Editor/colors/forward_plus_color = Color(0.54902, 0.752941, 0.392157, 1)
Editor/colors/gl_compatibility_color = Color(0.447059, 0.698039, 0.890196, 1)
Editor/colors/mobile_color = Color(0.862745, 0.482353, 0.584314, 1)
Editor/colors/prop_subsection = Color(1, 1, 1, 0)
Editor/colors/property_color_w = Color(1, 1, 1, 0.8)
Editor/colors/property_color_x = Color(0.882353, 0.384314, 0.466667, 1)
Editor/colors/property_color_y = Color(0.764706, 0.937255, 0.396078, 1)
Editor/colors/property_color_z = Color(0.415686, 0.670588, 0.964706, 1)
Editor/colors/warning_color = Color(0.831373, 0.780392, 0.623529, 1)
Editor/constants/top_bar_separation = 4
Editor/constants/window_border_margin = 4
EditorAbout/styles/panel = SubResource("StyleBoxFlat_sh018")
EditorAudioBus/styles/focus = SubResource("StyleBoxFlat_7b6lo")
EditorAudioBus/styles/master = SubResource("StyleBoxFlat_vswa4")
EditorAudioBus/styles/normal = SubResource("StyleBoxFlat_7b6lo")
EditorHelpBitContent/styles/normal = SubResource("StyleBoxFlat_gqd2v")
EditorHelpBitTitle/styles/normal = SubResource("StyleBoxFlat_dxwid")
EditorInspector/constants/v_separation = 3
EditorInspector/styles/panel = SubResource("StyleBoxFlat_6u0gn")
EditorInspectorCategory/styles/bg = SubResource("StyleBoxFlat_x2a5j")
EditorInspectorSection/constants/h_separation = 4
EditorLogFilterButton/styles/hover = SubResource("StyleBoxFlat_qakf4")
EditorLogFilterButton/styles/normal = SubResource("StyleBoxFlat_a3moc")
EditorLogFilterButton/styles/pressed = SubResource("StyleBoxFlat_je4t5")
EditorProperty/colors/property_color = Color(1, 1, 1, 0.6)
EditorProperty/colors/warning_color = Color(0.831373, 0.780392, 0.623529, 1)
EditorProperty/styles/bg = SubResource("StyleBoxFlat_a3moc")
EditorProperty/styles/bg_selected = SubResource("StyleBoxFlat_a3moc")
EditorProperty/styles/child_bg = SubResource("StyleBoxFlat_bx0ey")
EditorSettingsDialog/styles/panel = SubResource("StyleBoxFlat_sh018")
EditorSpinSlider/styles/label_bg = SubResource("StyleBoxFlat_1ni2p")
EditorStyles/colors/movie_writer_icon_hover = Color(1, 1, 1, 0.8)
EditorStyles/colors/movie_writer_icon_hover_pressed = Color(1, 1, 1, 0.8)
EditorStyles/colors/movie_writer_icon_normal = Color(1, 1, 1, 0.7)
EditorStyles/colors/movie_writer_icon_pressed = Color(1, 1, 1, 0.941176)
EditorStyles/styles/FocusViewport = SubResource("StyleBoxFlat_msxit")
EditorStyles/styles/Information3dViewport = SubResource("StyleBoxFlat_i82na")
EditorStyles/styles/LaunchPadMovieMode = SubResource("StyleBoxFlat_egrk0")
EditorStyles/styles/LaunchPadNormal = SubResource("StyleBoxFlat_dkrsl")
EditorStyles/styles/MovieWriterButtonPressed = SubResource("StyleBoxFlat_44k6j")
EditorValidationPanel/styles/panel = SubResource("StyleBoxFlat_swfvm")
FlatButton/colors/font_color = Color(1, 1, 1, 0.7)
FlatButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
FlatButton/colors/font_focus_color = Color(1, 1, 1, 1)
FlatButton/colors/font_hover_color = Color(1, 1, 1, 1)
FlatButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
FlatButton/colors/font_pressed_color = Color(1, 1, 1, 1)
FlatButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
FlatButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
FlatButton/styles/disabled = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/hover = SubResource("StyleBoxFlat_je4t5")
FlatButton/styles/hover_mirrored = SubResource("StyleBoxFlat_je4t5")
FlatButton/styles/hover_pressed = SubResource("StyleBoxFlat_qakf4")
FlatButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_qakf4")
FlatButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/pressed = SubResource("StyleBoxFlat_qakf4")
FlatButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_qakf4")
FlatMenuButton/colors/font_color = Color(1, 1, 1, 0.7)
FlatMenuButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
FlatMenuButton/colors/font_focus_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_hover_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_pressed_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
FlatMenuButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
FlatMenuButton/styles/disabled = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/focus = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/hover = SubResource("StyleBoxFlat_je4t5")
FlatMenuButton/styles/hover_mirrored = SubResource("StyleBoxFlat_je4t5")
FlatMenuButton/styles/hover_pressed = SubResource("StyleBoxFlat_qakf4")
FlatMenuButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_qakf4")
FlatMenuButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/pressed = SubResource("StyleBoxFlat_qakf4")
FlatMenuButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_qakf4")
GraphStateMachine/colors/focus_color = Color(1, 1, 1, 0)
HBoxContainer/constants/separation = 2
HScrollBar/styles/grabber = SubResource("StyleBoxFlat_ctoo2")
HScrollBar/styles/grabber_highlight = SubResource("StyleBoxFlat_rux83")
HScrollBar/styles/grabber_pressed = SubResource("StyleBoxFlat_rux83")
HScrollBar/styles/scroll = SubResource("StyleBoxFlat_kxbic")
HScrollBar/styles/scroll_focus = SubResource("StyleBoxFlat_kxbic")
HSplitContainer/constants/autohide = 1
HSplitContainer/constants/minimum_grab_thickness = 6
HSplitContainer/constants/separation = 2
InspectorActionButton/constants/h_separation = 8
InspectorActionButton/styles/disabled = SubResource("StyleBoxFlat_asl5a")
InspectorActionButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_asl5a")
InspectorActionButton/styles/hover = SubResource("StyleBoxFlat_oe7pa")
InspectorActionButton/styles/hover_mirrored = SubResource("StyleBoxFlat_oe7pa")
InspectorActionButton/styles/normal = SubResource("StyleBoxFlat_simoa")
InspectorActionButton/styles/normal_mirrored = SubResource("StyleBoxFlat_simoa")
InspectorActionButton/styles/pressed = SubResource("StyleBoxFlat_57ytr")
InspectorActionButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_57ytr")
ItemList/colors/guide_color = Color(1, 1, 1, 0)
ItemList/constants/v_separation = 6
ItemList/styles/cursor = SubResource("StyleBoxFlat_5vyo7")
ItemList/styles/cursor_unfocused = SubResource("StyleBoxFlat_5vyo7")
ItemList/styles/focus = SubResource("StyleBoxFlat_a3moc")
ItemList/styles/hovered = SubResource("StyleBoxFlat_je4t5")
ItemList/styles/hovered_selected = SubResource("StyleBoxFlat_je4t5")
ItemList/styles/hovered_selected_focus = SubResource("StyleBoxFlat_je4t5")
ItemList/styles/panel = SubResource("StyleBoxFlat_2rk15")
ItemList/styles/selected = SubResource("StyleBoxFlat_je4t5")
ItemList/styles/selected_focus = SubResource("StyleBoxFlat_je4t5")
ItemListSecondary/styles/panel = SubResource("StyleBoxFlat_r1jc0")
Label/colors/font_color = Color(1, 1, 1, 0.7)
Label/styles/normal = SubResource("StyleBoxFlat_6xhs0")
LineEdit/colors/font_placeholder_color = Color(1, 1, 1, 0.4)
LineEdit/styles/focus = SubResource("StyleBoxFlat_v6yi0")
LineEdit/styles/normal = SubResource("StyleBoxFlat_re7yf")
LineEdit/styles/read_only = SubResource("StyleBoxFlat_58bqw")
MainMenuBar/styles/hover = SubResource("StyleBoxFlat_je4t5")
MainMenuBar/styles/hover_pressed = SubResource("StyleBoxFlat_qakf4")
MainMenuBar/styles/normal = SubResource("StyleBoxFlat_uiad3")
MainMenuBar/styles/pressed = SubResource("StyleBoxFlat_qakf4")
MainScreenButton/styles/hover = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/hover_mirrored = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/hover_pressed = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/pressed = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/disabled = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/focus = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/hover = SubResource("StyleBoxFlat_je4t5")
MenuButton/styles/hover_mirrored = SubResource("StyleBoxFlat_je4t5")
MenuButton/styles/hover_pressed = SubResource("StyleBoxFlat_je4t5")
MenuButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_je4t5")
MenuButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/pressed = SubResource("StyleBoxFlat_qakf4")
MenuButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_qakf4")
OptionButton/colors/font_color = Color(1, 1, 1, 0.7)
OptionButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
OptionButton/colors/font_focus_color = Color(1, 1, 1, 1)
OptionButton/colors/font_hover_color = Color(1, 1, 1, 1)
OptionButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
OptionButton/colors/font_pressed_color = Color(1, 1, 1, 1)
OptionButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
OptionButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
OptionButton/constants/arrow_margin = 14
OptionButton/styles/disabled = SubResource("StyleBoxFlat_asl5a")
OptionButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_asl5a")
OptionButton/styles/focus = SubResource("StyleBoxFlat_a3moc")
OptionButton/styles/hover = SubResource("StyleBoxFlat_oe7pa")
OptionButton/styles/hover_mirrored = SubResource("StyleBoxFlat_oe7pa")
OptionButton/styles/hover_pressed = SubResource("StyleBoxFlat_57ytr")
OptionButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_57ytr")
OptionButton/styles/normal = SubResource("StyleBoxFlat_simoa")
OptionButton/styles/normal_mirrored = SubResource("StyleBoxFlat_simoa")
OptionButton/styles/pressed = SubResource("StyleBoxFlat_57ytr")
OptionButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_57ytr")
PanelContainer/styles/panel = SubResource("StyleBoxFlat_uiad3")
PopupDialog/styles/panel = SubResource("StyleBoxFlat_kv65r")
PopupMenu/constants/item_start_padding = 9
PopupMenu/constants/v_separation = 7
PopupMenu/styles/hover = SubResource("StyleBoxFlat_je4t5")
PopupMenu/styles/labeled_separator_left = SubResource("StyleBoxLine_jrqur")
PopupMenu/styles/labeled_separator_right = SubResource("StyleBoxLine_jrqur")
PopupMenu/styles/panel = SubResource("StyleBoxFlat_8rqeh")
PopupMenu/styles/separator = SubResource("StyleBoxLine_jrqur")
PopupPanel/styles/panel = SubResource("StyleBoxFlat_k2084")
ProgressBar/styles/background = SubResource("StyleBoxFlat_qygfl")
ProgressBar/styles/fill = SubResource("StyleBoxFlat_26erj")
ProjectSettingsEditor/styles/panel = SubResource("StyleBoxFlat_sh018")
RichTextLabel/styles/normal = SubResource("StyleBoxFlat_2xsss")
ScrollContainer/styles/focus = SubResource("StyleBoxFlat_a3moc")
ScrollContainer/styles/panel = SubResource("StyleBoxFlat_a3moc")
SplitContainer/constants/minimum_grab_thickness = 6
SplitContainer/constants/separation = 3
SplitContainer/icons/h_grabber = SubResource("PlaceholderTexture2D_n4x4x")
SplitContainer/icons/v_grabber = SubResource("PlaceholderTexture2D_n4x4x")
TabBar/styles/tab_focus = SubResource("StyleBoxFlat_1c2dg")
TabBar/styles/tab_hovered = SubResource("StyleBoxFlat_4h2fv")
TabBar/styles/tab_selected = SubResource("StyleBoxFlat_1flmk")
TabBar/styles/tab_unselected = SubResource("StyleBoxFlat_7qa65")
TabContainer/styles/panel = SubResource("StyleBoxFlat_m5ykw")
TabContainer/styles/tab_focus = SubResource("StyleBoxFlat_1c2dg")
TabContainer/styles/tab_hovered = SubResource("StyleBoxFlat_4h2fv")
TabContainer/styles/tab_selected = SubResource("StyleBoxFlat_1flmk")
TabContainer/styles/tab_unselected = SubResource("StyleBoxFlat_7qa65")
TabContainer/styles/tabbar_background = SubResource("StyleBoxFlat_lmcqk")
TabContainerOdd/styles/tab_selected = SubResource("StyleBoxFlat_f028s")
TextEdit/styles/focus = SubResource("StyleBoxFlat_v6yi0")
TextEdit/styles/normal = SubResource("StyleBoxFlat_re7yf")
TextEdit/styles/read_only = SubResource("StyleBoxFlat_58bqw")
TooltipPanel/styles/panel = SubResource("StyleBoxFlat_kjdif")
Tree/colors/drop_position_color = Color(1, 1, 1, 0.4)
Tree/colors/font_color = Color(1, 1, 1, 0.7)
Tree/colors/guide_color = Color(1, 1, 1, 0)
Tree/colors/parent_hl_line_color = Color(1, 1, 1, 0.1)
Tree/constants/children_hl_line_width = 0
Tree/constants/draw_guides = 0
Tree/constants/draw_relationship_lines = 1
Tree/constants/inner_item_margin_left = 4
Tree/constants/inner_item_margin_right = 4
Tree/constants/parent_hl_line_width = 1
Tree/constants/relationship_line_width = 0
Tree/constants/v_separation = 1
Tree/styles/button_hover = SubResource("StyleBoxFlat_8kufp")
Tree/styles/button_pressed = SubResource("StyleBoxFlat_qakf4")
Tree/styles/cursor = SubResource("StyleBoxFlat_oss8c")
Tree/styles/cursor_unfocused = SubResource("StyleBoxFlat_oss8c")
Tree/styles/custom_button_hover = SubResource("StyleBoxFlat_8kufp")
Tree/styles/custom_button_pressed = SubResource("StyleBoxFlat_qakf4")
Tree/styles/focus = SubResource("StyleBoxFlat_a3moc")
Tree/styles/hover = SubResource("StyleBoxFlat_8kufp")
Tree/styles/hovered = SubResource("StyleBoxFlat_8kufp")
Tree/styles/hovered_dimmed = SubResource("StyleBoxFlat_8kufp")
Tree/styles/hovered_selected = SubResource("StyleBoxFlat_46qep")
Tree/styles/hovered_selected_focus = SubResource("StyleBoxFlat_46qep")
Tree/styles/panel = SubResource("StyleBoxFlat_ytoxk")
Tree/styles/selected = SubResource("StyleBoxFlat_8kufp")
Tree/styles/selected_focus = SubResource("StyleBoxFlat_8kufp")
Tree/styles/title_button_hover = SubResource("StyleBoxFlat_m5r8g")
Tree/styles/title_button_normal = SubResource("StyleBoxFlat_m5r8g")
Tree/styles/title_button_pressed = SubResource("StyleBoxFlat_m5r8g")
TreeSecondary/styles/panel = SubResource("StyleBoxFlat_r1jc0")
VBoxContainer/constants/separation = 2
VScrollBar/styles/grabber = SubResource("StyleBoxFlat_ctoo2")
VScrollBar/styles/grabber_highlight = SubResource("StyleBoxFlat_rux83")
VScrollBar/styles/grabber_pressed = SubResource("StyleBoxFlat_rux83")
VScrollBar/styles/scroll = SubResource("StyleBoxFlat_s8xc4")
VScrollBar/styles/scroll_focus = SubResource("StyleBoxFlat_s8xc4")
VSplitContainer/constants/autohide = 1
VSplitContainer/constants/minimum_grab_thickness = 6
VSplitContainer/constants/separation = 2
script = SubResource("GDScript_hhmc0")
