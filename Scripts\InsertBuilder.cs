using Godot;
using Godot.Collections;
using System;
using System.Collections.Generic;
using System.Data;

public partial class InsertBuilder : Control
{
    DataTable InsertArrangement { get; set; }
    SQLHandler sqlHandler { get; set; }
    CustomSignals customSignals { get; set; }
    public int scale = 400;
    public int radiusScale = 600;
    public int count;
    public Array<Area2D> pinArray = new Array<Area2D>();
    [Export] public Camera2D camera { get; set; }

    [Export(PropertyHint.Range, "1.01,2.0,0.01")] public float zoomSpeed = 1.1f;
    [Export(PropertyHint.Range, "0.1,1.0,0.05")] public float minZoom = 0.2f;
    [Export(PropertyHint.Range, "1.0,10.0,0.5")] public float maxZoom = 5.0f;
    

    private bool isPanning = false;
    private Vector2 arrangementCenter = Vector2.Zero;
    public string partNumber;

    public override void _Ready()
    {
        SetAnchorsAndOffsetsPreset(LayoutPreset.FullRect);
        ConnectSignals();
    }

    private void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
    }

    public void SetArrangement(string PartNumber)
    {
        partNumber = PartNumber;

        InsertArrangement = sqlHandler.Query(@$"SELECT pin, xpos, ypos, Insert_Pins.gauge AS gauge, radius
        FROM parts
        JOIN part_attrib_int ON parts.[index] = part_attrib_int.part_index
        JOIN Insert_Arrgs ON part_attrib_int.value = Insert_Arrgs.[index]
        JOIN Inserts ON Inserts.[index] = Insert_Arrgs.insert_index
        JOIN Insert_Pins ON Inserts.[index] = Insert_Pins.insert_index
        JOIN Insert_StdContacts ON Insert_StdContacts.pin_index = Insert_Pins.[index]
        JOIN Insert_Contacts ON Insert_StdContacts.contact_index = Insert_Contacts.[index]
        WHERE part_attrib_int.attrib_index = 43 AND parts.PartNumber = '{PartNumber}'");
        
        count = InsertArrangement.Rows.Count;

        CalculateArrangementCenter();
        QueueRedraw();
    }

    private void CalculateArrangementCenter()
    {
        if (InsertArrangement == null || InsertArrangement.Rows.Count == 0)
        {
            arrangementCenter = Vector2.Zero;
            return;
        }

        float minX = float.MaxValue;
        float minY = float.MaxValue;
        float maxX = float.MinValue;
        float maxY = float.MinValue;

        foreach (DataRow row in InsertArrangement.Rows)
        {
            float xpos = float.Parse(row["xpos"].ToString());
            float ypos = float.Parse(row["ypos"].ToString());

            minX = Mathf.Min(minX, xpos);
            maxX = Mathf.Max(maxX, xpos);
            minY = Mathf.Min(minY, ypos);
            maxY = Mathf.Max(maxY, ypos);
        }
        
        arrangementCenter = new Vector2((minX + maxX) / 2f, (minY + maxY) / 2f);
    }

    public override void _GuiInput(InputEvent @event)
    {
        if (camera == null) return;

        if (@event is InputEventMouseButton mouseButtonEvent)
        {
            if (mouseButtonEvent.IsPressed())
            {
                if (mouseButtonEvent.ButtonIndex == MouseButton.WheelDown || mouseButtonEvent.ButtonIndex == MouseButton.WheelUp)
                {
                    Vector2 pointBeforeZoom = camera.GetCanvasTransform().AffineInverse() * mouseButtonEvent.Position;

                    Vector2 newZoom = camera.Zoom;
                    if (mouseButtonEvent.ButtonIndex == MouseButton.WheelDown)
                    {
                        newZoom /= zoomSpeed;
                    }
                    else
                    {
                        newZoom *= zoomSpeed;
                    }
                    camera.Zoom = newZoom.Clamp(new Vector2(minZoom, minZoom), new Vector2(maxZoom, maxZoom));
                    
                    Vector2 pointAfterZoom = camera.GetCanvasTransform().AffineInverse() * mouseButtonEvent.Position;
                    
                    camera.Position += pointBeforeZoom - pointAfterZoom;

                    GetViewport().SetInputAsHandled();
                    return;
                }
            }

            if (mouseButtonEvent.ButtonIndex == MouseButton.Middle)
            {
                isPanning = mouseButtonEvent.Pressed;
                Input.SetDefaultCursorShape(isPanning ? Input.CursorShape.Move : Input.CursorShape.Arrow);
                GetViewport().SetInputAsHandled();
            }
        }

        if (@event is InputEventMouseMotion mouseMotionEvent && isPanning)
        {
            camera.Position -= mouseMotionEvent.Relative;
            GetViewport().SetInputAsHandled();
        }
    }

    public override void _Draw()
    {
        if (InsertArrangement == null) return;
        
        Vector2 screenCenter = Size / 2;

        foreach (DataRow row in InsertArrangement.Rows)
        {
            float xpos = float.Parse(row["xpos"].ToString());
            float ypos = float.Parse(row["ypos"].ToString());
            float radius = float.Parse(row["radius"].ToString());
            
            float relativeX = (xpos - arrangementCenter.X) * scale;
            float relativeY = -(ypos - arrangementCenter.Y) * scale;
            
            Vector2 pinPosition = screenCenter + new Vector2(relativeX, relativeY);

            if (row["gauge"].ToString() == "QUADRAX" || row["gauge"].ToString() == "TWINAX" || row["gauge"].ToString() == "COAX")
            {
                DrawCircle(pinPosition, radius * radiusScale, new Color(1, 1, 1, 1), true, 0.5f, true);
            }
            else
            {
                DrawCircle(pinPosition, radius * radiusScale, new Color(1, 1, 1, 1), false, 0.5f, true);
            }

            // --- TEXT CENTERING LOGIC ---
            var font = GD.Load<Font>("res://Fonts/Roboto_SemiCondensed-Regular.ttf");
            int fontSize = 10;
            string pinLabel = row["pin"].ToString();

            // Step 1: Calculate the size of the text label
            Vector2 labelSize = font.GetStringSize(pinLabel, HorizontalAlignment.Left, -1, fontSize);

            // Step 2: Calculate the position for the top-left corner of the text to center it
            Vector2 labelPosition = pinPosition - labelSize / 2f;

            // Step 3: Draw the string at the new calculated position
            if (row["gauge"].ToString() == "QUADRAX" || row["gauge"].ToString() == "TWINAX" || row["gauge"].ToString() == "COAX")
            {
                DrawString(font, pinPosition - new Vector2(labelSize.X / 2f, -fontSize / 2f), pinLabel, HorizontalAlignment.Left, -1, fontSize, new Color(0, 0, 0, 1f));
            }
            else
            {
                DrawString(font, pinPosition - new Vector2(labelSize.X / 2f, - fontSize / 2f), pinLabel, HorizontalAlignment.Left, -1, fontSize, new Color(1, 1, 1, 1f));
            }
            
        }
    }
}