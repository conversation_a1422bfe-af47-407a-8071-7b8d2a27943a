[gd_scene load_steps=8 format=3 uid="uid://ds4qxmhlwh4ke"]

[ext_resource type="Script" uid="uid://biagtoupqhnth" path="res://Scripts/NavigationBar.cs" id="1_4atj2"]
[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_gyd2r"]
[ext_resource type="Texture2D" uid="uid://dsmqwv1ldxk6k" path="res://Icons/Back.svg" id="1_pg3vv"]
[ext_resource type="Texture2D" uid="uid://dtwetyxq2wxtr" path="res://Icons/ConnectorAssistantLogo.svg" id="2_4atj2"]
[ext_resource type="FontFile" uid="uid://cnivboaibgmc7" path="res://Fonts/Roboto_Condensed-Regular.ttf" id="3_xftw0"]
[ext_resource type="Texture2D" uid="uid://b5jamio0pccux" path="res://Icons/Home.svg" id="4_fcw3t"]
[ext_resource type="FontFile" uid="uid://dlvu30o86he7u" path="res://Fonts/Roboto-Light.ttf" id="8_xftw0"]

[node name="NavigationBar" type="PanelContainer" node_paths=PackedStringArray("BackButton", "ApplicationLogoButton", "ApplicationTitle", "HomeButton", "ApplicationNavigationContainer")]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 75.0
grow_horizontal = 2
size_flags_horizontal = 3
theme = ExtResource("1_gyd2r")
script = ExtResource("1_4atj2")
BackButton = NodePath("HBoxContainer/BackButton")
ApplicationLogoButton = NodePath("HBoxContainer/ApplicationLogoButton")
ApplicationTitle = NodePath("HBoxContainer/ApplicationTitle")
HomeButton = NodePath("HBoxContainer/ApplicationNavigation/HomeButton")
ApplicationNavigationContainer = NodePath("HBoxContainer/ApplicationNavigation")

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="BackButton" type="Button" parent="HBoxContainer"]
custom_minimum_size = Vector2(76, 0)
layout_mode = 2
size_flags_horizontal = 4
icon = ExtResource("1_pg3vv")
flat = true
icon_alignment = 1

[node name="ApplicationLogoButton" type="Button" parent="HBoxContainer"]
layout_mode = 2
icon = ExtResource("2_4atj2")
flat = true

[node name="ApplicationTitle" type="Label" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 2
theme_override_fonts/font = ExtResource("3_xftw0")
theme_override_font_sizes/font_size = 32
text = "CONNECTOR ASSISTANT"

[node name="ApplicationNavigation" type="HBoxContainer" parent="HBoxContainer"]
custom_minimum_size = Vector2(0, 75)
layout_mode = 2
size_flags_horizontal = 6

[node name="HomeButton" type="Button" parent="HBoxContainer/ApplicationNavigation"]
layout_mode = 2
icon = ExtResource("4_fcw3t")
flat = true

[node name="ApplicationTeamTitle" type="Label" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 6
theme_override_colors/font_color = Color(0.372549, 0.392157, 0.447059, 1)
theme_override_fonts/font = ExtResource("8_xftw0")
theme_override_font_sizes/font_size = 24
text = "LABOPS TEAM"
