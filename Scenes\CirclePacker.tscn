[gd_scene load_steps=4 format=3 uid="uid://d11qaem6f3lvm"]

[ext_resource type="Script" uid="uid://dumps6652l2ya" path="res://Scripts/CirclePacker.cs" id="1_ygpge"]
[ext_resource type="Script" uid="uid://bfru65h7grqdq" path="res://Scripts/CameraPanAndZoom.cs" id="2_xsu4u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ygpge"]

[node name="CirclePacker" type="Control" node_paths=PackedStringArray("Camera")]
top_level = true
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_ygpge")
CircleCount = 9
MaxRadius = 30.0
PushFactor = 0.53
TighteningForce = 0.731
Camera = NodePath("Camera2D")
_airGapLabelPath = NodePath("PanelContainer/MarginContainer/VBoxContainer/AirGapLabel")
_boundingRadiusLabelPath = NodePath("PanelContainer/MarginContainer/VBoxContainer/BoundingRadiusLabel")

[node name="Camera2D" type="Camera2D" parent="."]
anchor_mode = 0
script = ExtResource("2_xsu4u")

[node name="PanelContainer" type="PanelContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_ygpge")

[node name="MarginContainer" type="MarginContainer" parent="PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 50
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 50
theme_override_constants/margin_bottom = 15

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer/MarginContainer"]
visible = false
layout_mode = 2
size_flags_vertical = 8

[node name="BoundingRadiusLabel" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
horizontal_alignment = 1

[node name="HSlider" type="HSlider" parent="PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="AirGapLabel" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
horizontal_alignment = 1

[node name="HBoxContainer" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_constants/separation = 15

[node name="Button2" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Tighten"

[node name="Button" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "RESET"

[connection signal="value_changed" from="PanelContainer/MarginContainer/VBoxContainer/HSlider" to="." method="SetAirGap"]
[connection signal="toggled" from="PanelContainer/MarginContainer/VBoxContainer/HBoxContainer/Button2" to="." method="OnTightenButtonToggled"]
[connection signal="pressed" from="PanelContainer/MarginContainer/VBoxContainer/HBoxContainer/Button" to="." method="Reset"]
