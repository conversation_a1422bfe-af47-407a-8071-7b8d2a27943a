[gd_scene load_steps=4 format=3 uid="uid://c81c1jmq2x0ka"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_brc06"]
[ext_resource type="Script" uid="uid://c57kw2q6fbw7q" path="res://Scripts/ContactAssemblyRow.cs" id="1_pvrqr"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_pvrqr"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[node name="PanelContainer" type="PanelContainer" node_paths=PackedStringArray("CrimperLabel", "AccessoryContainer")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_brc06")
theme_override_styles/panel = SubResource("StyleBoxFlat_pvrqr")
script = ExtResource("1_pvrqr")
CrimperLabel = NodePath("ContactAssemblyRow/CrimperLabel")
AccessoryContainer = NodePath("ContactAssemblyRow/VBoxContainer")

[node name="ContactAssemblyRow" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="CrimperLabel" type="Label" parent="ContactAssemblyRow"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 1
text = "Crimper"
horizontal_alignment = 2
vertical_alignment = 1

[node name="VBoxContainer" type="VBoxContainer" parent="ContactAssemblyRow"]
layout_mode = 2
size_flags_horizontal = 3
