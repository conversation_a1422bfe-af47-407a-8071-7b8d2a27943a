[gd_scene load_steps=9 format=3 uid="uid://dux4y7u2xjgkj"]

[ext_resource type="Script" uid="uid://elv60idjpvjj" path="res://Scripts/ContactDetailsPanel.cs" id="1_l4ql5"]
[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="2_fi40t"]
[ext_resource type="Texture2D" uid="uid://c3fqb2e7wvft6" path="res://Icons/Contact Diagram Dimensions.svg" id="3_18f4v"]
[ext_resource type="Texture2D" uid="uid://dml6j7dxm4lwj" path="res://Icons/Contact Diagram_1.svg" id="4_m73xr"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_575e4"]
content_margin_left = 5.0
content_margin_top = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_shayv"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_o7cbj"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_t1prb"]

[node name="ContactDetailsPanel" type="PanelContainer" node_paths=PackedStringArray("PartNumberLine", "TypeLine", "WireRangeLine", "MatingEndLine", "WireBarrelLine", "InsertionToolLine", "RemovalToolLine", "AssemblyContainer", "ColorCode1", "ColorCode2", "ColorCode3")]
custom_minimum_size = Vector2(450, 0)
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_575e4")
script = ExtResource("1_l4ql5")
PartNumberLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/PartNumberLabel")
TypeLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer/TypeLabel")
WireRangeLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer/WireRangeLabel")
MatingEndLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer/MatingEndLabel")
WireBarrelLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer/WireBarrelLabel")
InsertionToolLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer/VBoxContainer/InsertionToolLine")
RemovalToolLine = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer2/VBoxContainer/RemovalToolLine")
AssemblyContainer = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer3/VBoxContainer/AssemblyVbox")
ColorCode1 = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer/HBoxContainer/Color1")
ColorCode2 = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer/HBoxContainer/Color2")
ColorCode3 = NodePath("ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer/HBoxContainer/Color3")

[node name="ContactsContainer" type="VBoxContainer" parent="."]
custom_minimum_size = Vector2(0, 182.37)
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 5

[node name="Label" type="Label" parent="ContactsContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
size_flags_vertical = 0
text = "Contact Details"
horizontal_alignment = 1

[node name="ContactsPanel" type="PanelContainer" parent="ContactsContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2
theme = ExtResource("2_fi40t")
theme_override_styles/panel = SubResource("StyleBoxFlat_shayv")

[node name="VBoxContainer" type="VBoxContainer" parent="ContactsContainer/ContactsPanel"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="PartNumberLabel" type="Label" parent="ContactsContainer/ContactsPanel/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 25
text = "Part Number"

[node name="HBoxContainer" type="FlowContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer"]
layout_mode = 2

[node name="TypeLabel" type="LineEdit" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_575e4")
text = "Contact Type: Pin"
alignment = 1
editable = false
expand_to_text_length = true

[node name="WireRangeLabel" type="LineEdit" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_575e4")
text = "Wire Range: 22 - 28"
alignment = 1
editable = false
expand_to_text_length = true

[node name="MatingEndLabel" type="LineEdit" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_575e4")
text = "Mating End: 16"
alignment = 1
editable = false
expand_to_text_length = true

[node name="WireBarrelLabel" type="LineEdit" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_575e4")
text = "Wire Barrel: 16"
alignment = 1
editable = false
expand_to_text_length = true

[node name="HBoxContainer2" type="HBoxContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 8
theme_override_constants/separation = 5

[node name="PanelContainer" type="PanelContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_575e4")

[node name="VBoxContainer" type="VBoxContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2
text = "Insertion Tool"
horizontal_alignment = 1

[node name="InsertionToolLine" type="LineEdit" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_o7cbj")
placeholder_text = "Part Number"
alignment = 1
editable = false

[node name="PanelContainer2" type="PanelContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_575e4")

[node name="VBoxContainer" type="VBoxContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer2"]
layout_mode = 2

[node name="Label" type="Label" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer2/VBoxContainer"]
layout_mode = 2
text = "Removal Tool"
horizontal_alignment = 1

[node name="RemovalToolLine" type="LineEdit" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer2/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_o7cbj")
placeholder_text = "Part Number"
alignment = 1
editable = false

[node name="PanelContainer3" type="PanelContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_575e4")

[node name="VBoxContainer" type="VBoxContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer3"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="Label" type="Label" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer3/VBoxContainer"]
layout_mode = 2
text = "Assembly"
horizontal_alignment = 1

[node name="AssemblyVbox" type="VBoxContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/HBoxContainer2/PanelContainer3/VBoxContainer"]
layout_mode = 2

[node name="PanelContainer2" type="PanelContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 10
theme_override_styles/panel = SubResource("StyleBoxEmpty_t1prb")

[node name="TextureRect" type="TextureRect" parent="ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer2"]
layout_mode = 2
texture = ExtResource("3_18f4v")
stretch_mode = 3

[node name="PanelContainer" type="PanelContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_t1prb")

[node name="TextureRect" type="TextureRect" parent="ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer"]
layout_mode = 2
texture = ExtResource("4_m73xr")
stretch_mode = 3

[node name="HBoxContainer" type="HBoxContainer" parent="ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer"]
custom_minimum_size = Vector2(400, 0)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_constants/separation = 5

[node name="Color1" type="ColorRect" parent="ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer/HBoxContainer"]
custom_minimum_size = Vector2(25, 40)
layout_mode = 2

[node name="Color2" type="ColorRect" parent="ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer/HBoxContainer"]
custom_minimum_size = Vector2(25, 40)
layout_mode = 2

[node name="Color3" type="ColorRect" parent="ContactsContainer/ContactsPanel/VBoxContainer/PanelContainer/HBoxContainer"]
custom_minimum_size = Vector2(25, 40)
layout_mode = 2
