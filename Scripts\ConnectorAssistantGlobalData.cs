using Godot;
using Microsoft.Data.SqlClient;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

public partial class ConnectorAssistantGlobalData : Node
{
    SQLHandler sqlHandler;
    CustomSignals customSignals;
    public ProgressBar progressBar;
    public Label loadingLabel;
    public RichTextLabel loadingText;
    public DataTable TypeTable;
    public DataTable AttributeTable;
    public DataTable PartsTable;
    public List<DataTable> data = new List<DataTable>();
    public Dictionary<string, Dictionary<string, string>> items;
    public List<string> partList = new List<string>();
    public List<string> attributeList = new List<string>();
    public AssistantSuiteInitialize initializeScene;
    public Tree ItemTree = new Tree();
    public string UserName;

    public override void _Ready()
    {
        ConnectSignals();
        UserName = System.Environment.UserName;
    }
    public void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
    }
    public async void GetAllDataAsync()
    {
        initializeScene.TweenLight();
        double progress = 0;
        progressBar.MaxValue = 6;
        loadingText.AddText("Acquiring data from Server \n");

        loadingText.AddText("Querying types of parts from Server \n");
        TypeTable = await sqlHandler.QueryAsync($"SELECT [type] FROM dbo.types");
        loadingText.AddText("Loaded " + TypeTable.Rows.Count + " types \n");
        TweenProgressBar(progress += 1.0f);


        loadingText.AddText("Querying part attributes from Server \n");
        AttributeTable = await sqlHandler.QueryAsync($"SELECT [attribute], [display_name] FROM [CableDesignTool_Play].[dbo].[attributes]");
        loadingText.AddText("Loaded " + AttributeTable.Rows.Count + " attributes \n");
        TweenProgressBar(progress += 1.0f);

        loadingText.AddText("Querying all parts from Server \n");
        PartsTable = await sqlHandler.QueryAsync($"SELECT [PartNumber], [type] FROM parts JOIN part_types ON parts.[index] = part_types.part_index JOIN types ON part_types.type_index = types.[index] ORDER BY [type]");
        loadingText.AddText("Loaded " + PartsTable.Rows.Count + " parts \n");
        TweenProgressBar(progress += 1.0f);

        partList = new List<string>();
        attributeList = new List<string>();

        foreach (DataRow row in PartsTable.Rows)
        {
            if (!partList.Contains(row["PartNumber"].ToString()))
            {
                partList.Add(row["PartNumber"].ToString());
            }
        }
        loadingText.AddText("Parsed " + partList.Count + " parts for SQL query \n");
        TweenProgressBar(progress += 1.0f);

        foreach (DataRow row in AttributeTable.Rows)
        {
            if (!attributeList.Contains(row["attribute"].ToString()))
            {
                attributeList.Add(row["attribute"].ToString());
            }
        }
        loadingText.AddText("Parsed " + attributeList.Count + " attributes for SQL query \n");
        TweenProgressBar(progress += 1.0f);

        string parts = string.Join(", ", partList);
        string attributes = string.Join(", ", attributeList);

        loadingText.AddText("Loading data tables from Server \n");
        data = sqlHandler.QueryStoredProcedureLoadEnabled(progressBar, loadingLabel, "sp_partInfo_multi",
        [
            new SqlParameter("@part_list", parts),
            new SqlParameter("@attrib_list", attributes),
        ]);
        loadingText.AddText("Loaded " + data.Count + " data tables \n");
        TweenProgressBar(progress += 1.0f);

        attributeList.Insert(0, "Part Number");
        Tree ItemTree = SetupTreeColumns(attributeList);

        TreeItem root = ItemTree.CreateItem();
        ItemTree.HideRoot = true;


        items = new Dictionary<string, Dictionary<string, string>>();
        foreach (DataTable table in data)
        {
            foreach (DataRow row in table.Rows)
            {
                if (!items.ContainsKey(row["PartNumber"].ToString()))
                {
                    items.Add(row["PartNumber"].ToString(), new Dictionary<string, string>());
                }
                if (!items[row["PartNumber"].ToString()].ContainsKey(row["attrib_row"].ToString()))
                {
                    items[row["PartNumber"].ToString()].Add(row["attrib_row"].ToString(), row["value"].ToString());
                }
            }
        }

        foreach (KeyValuePair<string, Dictionary<string, string>> item in items)
        {
            TreeItem part = ItemTree.CreateItem(root);
            part.SetText(0, item.Key);
            foreach (KeyValuePair<string, string> attribute in item.Value)
            {
                part.SetText(attribute.Key.ToInt(), attribute.Value);
            }
        }

        loadingText.AddText("Done Loading Part Data. \n");
        TweenProgressBar(progressBar.MaxValue);
        await Task.Delay(3000);
        initializeScene.OnDataLoadComplete();
    }
    public void TweenProgressBar(double value)
    {
        Tween tween = GetTree().CreateTween();
        tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(progressBar, "value", value, 1.0f);
    }
    public Tree SetupTreeColumns(List<string> attributes)
    {
        ItemTree.Clear();
        ItemTree.Columns = attributes.Count;
        ItemTree.ColumnTitlesVisible = true;
        foreach (string attribute in attributes)
        {
            ItemTree.SetColumnTitle(attributes.IndexOf(attribute), attribute);
            ItemTree.SetColumnExpand(attributes.IndexOf(attribute), true);
            ItemTree.SetColumnCustomMinimumWidth(attributes.IndexOf(attribute), 200);
        }
        return ItemTree;
    }
}
