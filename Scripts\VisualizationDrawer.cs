// VisualizationDrawer.cs
using Godot;

public partial class VisualizationDrawer : Control
{
    private MainController _mainController; // Reference to the main controller to get data
    private const float MmToInches = 0.0393701f;
    // --- Exported variables to control the visual style and fixed sizes of the drawing ---
    [ExportGroup("Visual Appearance")]
    [Export] public float VisualArcCenterlineRadiusPx { get; set; } = 60.0f; // Fixed radius for the centerline of the arc being drawn (in pixels)
    [Export] public float VisualStraightLenPx { get; set; } = 100.0f;   // Fixed length for the straight wire segments being drawn (in pixels)
    [Export] public float VisualWireThicknessPx { get; set; } = 4.0f;    // Fixed thickness for the wire being drawn (in pixels)

    [ExportGroup("Positioning & Style")]
    [Export] public Vector2 DrawingOrigin { get; set; } // Top-left starting point of the drawing within this Control node
    [Export] public Color WireColor { get; set; } = Colors.SteelBlue;          // Color of the wire
    [Export] public Color DimensionColor { get; set; } = Colors.DarkGray;     // Color of dimension lines and ticks
    [Export] public Color TextColor { get; set; } = Colors.Silver;             // Color of dimension text
    [Export] public float TextPadding { get; set; } = 5f;                      // General padding between dimension lines and text (in pixels)

    private Font _font;        // Font used for drawing text
    private int _fontSize;     // Font size used for drawing text

    public override void _Ready()
    {
        // Find the MainController node by searching up the scene tree
        Node current = GetParent();
        while (current != null)
        {
            if (current is MainController controller)
            {
                _mainController = controller;
                GD.Print("VisualizationDrawer found MainController.");
                break;
            }
            current = current.GetParent();
        }
        if (_mainController == null)
        {
            GD.PrintErr("VisualizationDrawer could NOT find MainController in its parent hierarchy!");
        }

        // Get default theme font and font size (User's updated method)
        var defaultTheme = ThemeDB.GetDefaultTheme();
        _font = defaultTheme.GetFont("font", "Label");
        _fontSize = defaultTheme.GetFontSize("font_size", "Label");

        if (_font == null)
        {
            GD.PushWarning("Default Label font not found for VisualizationDrawer. Text might not render correctly."); // User's updated warning
        }
    }

    public override void _Draw()
    {
        DrawingOrigin = Position + (Size / 2) - new Vector2(200, 150);
        // Ensure the instance is valid and necessary data is available before drawing
        if (!IsInstanceValid(this) || _mainController == null || _mainController.CurrentWireData == null)
        {
            if (IsInstanceValid(this) && _font != null) // Draw placeholder text if setup is incomplete
            {
                DrawString(_font, new Vector2(TextPadding, TextPadding + _fontSize),
                               "Select wire type and angle.", HorizontalAlignment.Left, -1, _fontSize, TextColor);
            }
            return;
        }

        // Get actual calculated data in MILLIMETERS from MainController
        float actualBendRadiusInnerMm = _mainController.BendRadius;
        float actualArcLengthMm = _mainController.ArcLength;
        float actualStraightLenMm = _mainController.StraightSegmentLength;
        float bendAngleDegrees = _mainController.CurrentBendAngleDegrees;
        float bendAngleRadians = Mathf.DegToRad(bendAngleDegrees);

        // Determine display units based on MainController's setting
        bool useMm = _mainController.DisplayInMillimeters;
        string unitSuffix = useMm ? "mm" : "in";
        float conversionFactor = useMm ? 1.0f : MmToInches;
        string formatSpecifier = useMm ? "F1" : "F2"; // Or "F3" for inches if more precision is desired

        // --- Visual Drawing Calculations (these use fixed pixel values from exports) ---
        Vector2 p0 = DrawingOrigin;
        Vector2 p1 = p0 + new Vector2(VisualStraightLenPx, 0);
        float drawnArcCenterlineRadiusPx = VisualArcCenterlineRadiusPx;
        Vector2 arcCenter = p1 + new Vector2(0, drawnArcCenterlineRadiusPx);
        float arcAngleStartRad = -Mathf.Pi / 2.0f;
        float arcAngleEndRad = arcAngleStartRad + bendAngleRadians;
        Vector2 p2 = arcCenter + new Vector2(Mathf.Cos(arcAngleEndRad), Mathf.Sin(arcAngleEndRad)) * drawnArcCenterlineRadiusPx;
        float tangentAngle = arcAngleEndRad + Mathf.Pi / 2.0f;
        Vector2 p3Direction = new Vector2(Mathf.Cos(tangentAngle), Mathf.Sin(tangentAngle));
        Vector2 p3 = p2 + p3Direction * VisualStraightLenPx;

        // --- Draw Measurements & Angles (Text will use converted units) ---
        if (_font != null)
        {
            float tickSize = 3.0f;
            // arcDimVisualOffset defined here as it's used by straight segment dim line as well
            float arcDimVisualOffset = _fontSize * 1.2f;

            // --- Angle Arc Indicator & Text ---
            // ... (existing angle indicator drawing logic, angleStr uses bendAngleDegrees directly) ...
            float angleIndicatorRadius = drawnArcCenterlineRadiusPx * 0.5f;
            if (angleIndicatorRadius > 5.0f && bendAngleRadians > Mathf.DegToRad(0.5f))
            {
                DrawArc(arcCenter, angleIndicatorRadius, arcAngleStartRad, arcAngleEndRad, 24, DimensionColor, 1.0f, true);
                string angleStr = $"{bendAngleDegrees:F0}°";
                Vector2 angleStrSize = _font.GetStringSize(angleStr, HorizontalAlignment.Left, -1, _fontSize);
                Vector2 angleTextDir = new Vector2(Mathf.Cos(arcAngleStartRad + bendAngleRadians / 2.0f), Mathf.Sin(arcAngleStartRad + bendAngleRadians / 2.0f));
                Vector2 angleTextCenterAnchor = arcCenter + angleTextDir * (angleIndicatorRadius / 2f + angleStrSize.Y / 2f + TextPadding + 5f);
                Vector2 finalAngleTextPos = angleTextCenterAnchor - angleStrSize / 2f;
                DrawString(_font, finalAngleTextPos, angleStr, HorizontalAlignment.Left, -1, _fontSize, TextColor);
            }

            // --- Radius Dimension Lines & Text ---
            if (bendAngleRadians > Mathf.DegToRad(0.5f))
            {
                // ... (drawing of the two radius lines remains the same) ...
                Vector2 startRadiusNormal = new Vector2(Mathf.Cos(arcAngleStartRad), Mathf.Sin(arcAngleStartRad));
                Vector2 radiusLineStartPointOnArc = arcCenter + startRadiusNormal * drawnArcCenterlineRadiusPx;
                DrawLine(arcCenter, radiusLineStartPointOnArc, DimensionColor, 1.0f, true);
                DrawLine(arcCenter, p2, DimensionColor, 1.0f, true);

                // Convert radius for display and format text
                float displayRadius = actualBendRadiusInnerMm * conversionFactor;
                string rText = $"Minimum Bend Radius: {displayRadius.ToString(formatSpecifier)}{unitSuffix}";
                Vector2 rTextSize = _font.GetStringSize(rText, HorizontalAlignment.Center, -1, _fontSize);
                // Using user's hardcoded position for R text anchor:
                Vector2 rTextCenterAnchor = arcCenter + new Vector2(0, 300);
                Vector2 finalRTextPos = rTextCenterAnchor - rTextSize / 2f;
                DrawString(_font, finalRTextPos, rText, HorizontalAlignment.Center, -1, _fontSize, TextColor);
            }

            // // --- Length of First Straight Segment Dimension ---
            // float segDimLineY = p0.Y - arcDimVisualOffset;
            // Vector2 seg1DimLineStart = new Vector2(p0.X, segDimLineY);
            // Vector2 seg1DimLineEnd = new Vector2(p1.X, segDimLineY);
            // DrawLine(seg1DimLineStart, seg1DimLineEnd, DimensionColor, 1.0f, true);
            // DrawLine(seg1DimLineStart + new Vector2(0, -tickSize), seg1DimLineStart + new Vector2(0, tickSize), DimensionColor, 1.0f, true);
            // DrawLine(seg1DimLineEnd + new Vector2(0, -tickSize), seg1DimLineEnd + new Vector2(0, tickSize), DimensionColor, 1.0f, true);

            // // Convert straight length for display and format text
            // float displayStraightLength = actualStraightLenMm * conversionFactor;
            // string seg1Text = $"{displayStraightLength.ToString(formatSpecifier)}{unitSuffix}";
            // Vector2 seg1TextSize = _font.GetStringSize(seg1Text, HorizontalAlignment.Left, -1, _fontSize);
            // Vector2 seg1TextCenterAnchor = (seg1DimLineStart + seg1DimLineEnd) / 2.0f + new Vector2(0, seg1TextSize.Y / 2f + TextPadding);
            // Vector2 finalSeg1TextPos = seg1TextCenterAnchor - new Vector2(seg1TextSize.X / 2f, 0);
            // DrawString(_font, finalSeg1TextPos, seg1Text, HorizontalAlignment.Left, -1, _fontSize, TextColor);

            // --- Arc Length Dimension ---
            if (drawnArcCenterlineRadiusPx > 0.01f && actualArcLengthMm > 0.01f && bendAngleRadians > Mathf.DegToRad(0.5f))
            {
                float arcDimRadiusPx = drawnArcCenterlineRadiusPx + arcDimVisualOffset;
                // ... (drawing of arc length dimension line and ticks remains the same) ...
                DrawArc(arcCenter, arcDimRadiusPx, arcAngleStartRad, arcAngleEndRad, 50, DimensionColor, 1.0f, true);
                Vector2 arcStartDirTick = new Vector2(Mathf.Cos(arcAngleStartRad), Mathf.Sin(arcAngleStartRad));
                Vector2 arcEndDirTick = new Vector2(Mathf.Cos(arcAngleEndRad), Mathf.Sin(arcAngleEndRad));
                DrawLine(arcCenter + arcStartDirTick * (arcDimRadiusPx - tickSize), arcCenter + arcStartDirTick * (arcDimRadiusPx + tickSize), DimensionColor, 1.0f, true);
                DrawLine(arcCenter + arcEndDirTick * (arcDimRadiusPx - tickSize), arcCenter + arcEndDirTick * (arcDimRadiusPx + tickSize), DimensionColor, 1.0f, true);

                // Convert arc length for display and format text
                float displayArcLength = actualArcLengthMm * conversionFactor;
                string arcLengthText = $"L: {displayArcLength.ToString(formatSpecifier)}{unitSuffix}";
                Vector2 arcTextSize = _font.GetStringSize(arcLengthText, HorizontalAlignment.Left, -1, _fontSize);
                float midArcDisplayAngle = arcAngleStartRad + bendAngleRadians / 2.0f;
                Vector2 midArcNormal = new Vector2(Mathf.Cos(midArcDisplayAngle), Mathf.Sin(midArcDisplayAngle));
                Vector2 arcLengthTextCenterAnchor = arcCenter + midArcNormal * (arcDimRadiusPx + arcTextSize.Y / 2f + TextPadding);
                Vector2 finalArcLengthTextPos = arcLengthTextCenterAnchor - arcTextSize / 2.0f;
                DrawString(_font, finalArcLengthTextPos, arcLengthText, HorizontalAlignment.Left, -1, _fontSize, TextColor);
            }
        }

        // --- DRAW WIRE LAST (So it appears on top) ---
        // ... (existing wire drawing logic) ...
        // DrawLine(p0, p1, WireColor, VisualWireThicknessPx, true);
        if (drawnArcCenterlineRadiusPx > 0.01f)
        {
            DrawArc(arcCenter, drawnArcCenterlineRadiusPx, arcAngleStartRad, arcAngleEndRad, 50, WireColor, VisualWireThicknessPx, true);
        }
        // DrawLine(p2, p3, WireColor, VisualWireThicknessPx, true);
    }
}