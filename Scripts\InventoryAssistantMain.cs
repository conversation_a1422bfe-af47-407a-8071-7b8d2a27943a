using Godot;
using System;
using System.Data;

public partial class InventoryAssistantMain : PanelContainer
{
    [Export] Tree InventoryTree { get; set; }
    public SQLHandler sqlHandler { get; set; }
    public CustomSignals customSignals { get; set; }

    public override void _Ready()
    {
        ConnectSignals();
        LoadInventory();
    }

    public void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
    }

    public async void LoadInventory()
    {
        try
        {
            DataTable InventoryTable = await sqlHandler.QueryAsync(@"
            SELECT [Part_Number] AS [Part Number], [QTY] AS Quantity, [ROOM] AS Room, [Bin] AS Bin, [ALT_P_N] AS [Alternate Part Number]
            FROM [SysTest].[dbo].[Inventory]");
            
        }
        catch (Exception e)
        {
            GD.Print(e.Message);
        }
    }
    
}
