using Godot;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

/// <summary>
/// Excel-like inventory management interface with user authentication, editable cells,
/// column resizing, filtering, and search capabilities.
/// </summary>
public partial class InventoryAssistantMain : PanelContainer
{
    #region UI Component References
    /// <summary>Tree control for displaying inventory data in a spreadsheet-like format</summary>
    [Export] public Tree InventoryTree { get; set; }

    /// <summary>Search input field for filtering inventory items</summary>
    [Export] public LineEdit SearchBar { get; set; }

    /// <summary>Button to trigger search operations</summary>
    [Export] public Button SearchButton { get; set; }

    /// <summary>Button to open advanced filter options</summary>
    [Export] public Button FilterButton { get; set; }

    /// <summary>Button to refresh data from database</summary>
    [Export] public Button RefreshButton { get; set; }

    /// <summary>Button to save changes back to database</summary>
    [Export] public Button SaveButton { get; set; }

    /// <summary>Label showing current user and edit permissions</summary>
    [Export] public Label UserStatusLabel { get; set; }

    /// <summary>Progress bar for data loading operations</summary>
    [Export] public ProgressBar LoadingProgressBar { get; set; }

    /// <summary>Label for status messages</summary>
    [Export] public Label StatusLabel { get; set; }

    /// <summary>Container for filter controls</summary>
    [Export] public VBoxContainer FilterContainer { get; set; }

    /// <summary>Window for advanced filter options</summary>
    [Export] public Window FilterWindow { get; set; }
    #endregion

    #region Dependencies
    /// <summary>Database handler for SQL operations</summary>
    public SQLHandler SqlHandler { get; private set; }

    /// <summary>Global signal handler for cross-component communication</summary>
    public CustomSignals CustomSignals { get; private set; }
    #endregion

    #region Data Management
    /// <summary>Original unfiltered inventory data from database</summary>
    private DataTable _originalInventoryData;

    /// <summary>Currently displayed filtered data</summary>
    private DataTable _filteredInventoryData;

    /// <summary>Dictionary to track modified cells for saving</summary>
    private Dictionary<string, Dictionary<string, object>> _modifiedCells = new();

    /// <summary>List of column names that can be edited</summary>
    private readonly List<string> _editableColumns = new() { "Quantity", "Room", "Bin", "Alternate Part Number" };

    /// <summary>Dictionary storing column filters</summary>
    private Dictionary<string, string> _columnFilters = new();
    #endregion

    #region User Authentication
    /// <summary>Current logged-in user</summary>
    public string CurrentUser { get; private set; } = "Guest";

    /// <summary>Whether current user has edit permissions</summary>
    public bool HasEditPermissions { get; private set; } = false;

    /// <summary>List of users with edit permissions</summary>
    private readonly HashSet<string> _approvedUsers = new()
    {
        "admin", "inventory_manager", "supervisor"
    };
    #endregion

    #region Godot Lifecycle
    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// Initializes the inventory assistant and loads data.
    /// </summary>
    public override void _Ready()
    {
        InitializeInventoryAssistant();
        ConnectSignals();
        SetupTreeColumns();
        LoadInventoryData();
    }
    #endregion

    #region Initialization
    /// <summary>
    /// Initializes the inventory assistant with default settings.
    /// </summary>
    private void InitializeInventoryAssistant()
    {
        // Initialize data structures
        _modifiedCells = new Dictionary<string, Dictionary<string, object>>();
        _columnFilters = new Dictionary<string, string>();

        // Set initial UI state
        SaveButton.Disabled = true;
        LoadingProgressBar.Visible = false;
        StatusLabel.Text = "Ready";

        // Set up user authentication (in real app, get from login system)
        SetCurrentUser("admin"); // TODO: Replace with actual user authentication
    }

    /// <summary>
    /// Connects UI element signals to their respective event handlers.
    /// </summary>
    private void ConnectSignals()
    {
        try
        {
            SqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
            CustomSignals = GetNode<CustomSignals>("/root/CustomSignals");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error connecting to global nodes: {ex.Message}");
        }

        // Connect UI signals
        SearchButton.Pressed += OnSearchButtonPressed;
        FilterButton.Pressed += OnFilterButtonPressed;
        RefreshButton.Pressed += OnRefreshButtonPressed;
        SaveButton.Pressed += OnSaveButtonPressed;
        SearchBar.TextChanged += OnSearchBarTextChanged;

        // Connect tree signals
        InventoryTree.ItemSelected += OnTreeItemSelected;
        InventoryTree.ItemEdited += OnTreeItemEdited;
        InventoryTree.ColumnTitleClicked += OnColumnTitleClicked;

        // Connect filter window signals if available
        if (FilterWindow != null)
        {
            FilterWindow.CloseRequested += OnFilterWindowCloseRequested;
        }
    }

    /// <summary>
    /// Sets up the tree columns with proper configuration for Excel-like behavior.
    /// </summary>
    private void SetupTreeColumns()
    {
        InventoryTree.Clear();
        InventoryTree.Columns = 5; // Part Number, Quantity, Room, Bin, Alternate Part Number
        InventoryTree.ColumnTitlesVisible = true;
        InventoryTree.HideRoot = true;
        InventoryTree.SelectMode = Tree.SelectModeEnum.Single;

        // Configure columns
        var columnTitles = new[] { "Part Number", "Quantity", "Room", "Bin", "Alternate Part Number" };
        var columnWidths = new[] { 200, 100, 100, 100, 200 };

        for (int i = 0; i < columnTitles.Length; i++)
        {
            InventoryTree.SetColumnTitle(i, columnTitles[i]);
            InventoryTree.SetColumnCustomMinimumWidth(i, columnWidths[i]);
            InventoryTree.SetColumnExpand(i, true);
            InventoryTree.SetColumnClipContent(i, false);
        }
    }
    #endregion

    #region User Authentication
    /// <summary>
    /// Sets the current user and updates permissions.
    /// </summary>
    /// <param name="username">Username to set as current user</param>
    public void SetCurrentUser(string username)
    {
        CurrentUser = username ?? "Guest";
        HasEditPermissions = _approvedUsers.Contains(CurrentUser.ToLower());

        UpdateUserStatusDisplay();
        UpdateEditPermissions();

        GD.Print($"User set to: {CurrentUser}, Edit permissions: {HasEditPermissions}");
    }

    /// <summary>
    /// Updates the user status display in the UI.
    /// </summary>
    private void UpdateUserStatusDisplay()
    {
        if (UserStatusLabel != null)
        {
            var permissionText = HasEditPermissions ? "Edit Enabled" : "Read Only";
            UserStatusLabel.Text = $"User: {CurrentUser} ({permissionText})";

            // Color code the status
            UserStatusLabel.Modulate = HasEditPermissions ? Colors.Green : Colors.Orange;
        }
    }

    /// <summary>
    /// Updates UI elements based on current user's edit permissions.
    /// </summary>
    private void UpdateEditPermissions()
    {
        if (SaveButton != null)
        {
            SaveButton.Disabled = !HasEditPermissions;
        }

        // Update tree editability
        if (InventoryTree != null)
        {
            // Tree editing will be controlled per-cell in the edit event handler
        }
    }
    #endregion

    #region Data Loading
    /// <summary>
    /// Loads inventory data from the database and populates the tree.
    /// </summary>
    public async void LoadInventoryData()
    {
        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Loading inventory data...";

            _originalInventoryData = await SqlHandler.QueryAsync(@"
                SELECT
                    [Part_Number] AS [Part Number],
                    [QTY] AS [Quantity],
                    [ROOM] AS [Room],
                    [Bin] AS [Bin],
                    [ALT_P_N] AS [Alternate Part Number]
                FROM [SysTest].[dbo].[Inventory]
                ORDER BY [Part_Number]");

            _filteredInventoryData = _originalInventoryData.Copy();

            await PopulateTreeWithData(_filteredInventoryData);

            StatusLabel.Text = $"Loaded {_originalInventoryData.Rows.Count} inventory items";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error loading inventory data: {ex.Message}");
            StatusLabel.Text = "Error loading data";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Populates the tree with data from a DataTable.
    /// </summary>
    /// <param name="data">DataTable containing inventory data</param>
    private async Task PopulateTreeWithData(DataTable data)
    {
        InventoryTree.Clear();
        var root = InventoryTree.CreateItem();
        InventoryTree.HideRoot = true;

        LoadingProgressBar.MaxValue = data.Rows.Count;
        LoadingProgressBar.Value = 0;

        for (int i = 0; i < data.Rows.Count; i++)
        {
            var row = data.Rows[i];
            var item = InventoryTree.CreateItem(root);

            // Populate columns
            for (int col = 0; col < data.Columns.Count; col++)
            {
                var value = row[col]?.ToString() ?? "";
                item.SetText(col, value);

                // Set cell as editable if user has permissions and column is editable
                var columnName = data.Columns[col].ColumnName;
                if (HasEditPermissions && _editableColumns.Contains(columnName))
                {
                    item.SetEditable(col, true);
                }

                // Set text alignment
                if (columnName == "Part Number")
                {
                    item.SetTextAlignment(col, HorizontalAlignment.Left);
                }
                else
                {
                    item.SetTextAlignment(col, HorizontalAlignment.Center);
                }
            }

            LoadingProgressBar.Value = i + 1;

            // Yield control periodically to prevent UI freezing
            if (i % 50 == 0)
            {
                await ToSignal(GetTree().CreateTimer(0.001), "timeout");
            }
        }
    }

    /// <summary>
    /// Shows or hides the loading state UI elements.
    /// </summary>
    /// <param name="isLoading">Whether to show loading state</param>
    private void ShowLoadingState(bool isLoading)
    {
        if (LoadingProgressBar != null)
        {
            LoadingProgressBar.Visible = isLoading;
        }

        if (RefreshButton != null)
        {
            RefreshButton.Disabled = isLoading;
        }
    }
    #endregion

    #region Event Handlers
    /// <summary>
    /// Handles search button press events.
    /// </summary>
    private void OnSearchButtonPressed()
    {
        ApplyFiltersAndSearch();
    }

    /// <summary>
    /// Handles filter button press events.
    /// </summary>
    private void OnFilterButtonPressed()
    {
        if (FilterWindow != null)
        {
            FilterWindow.Show();
        }
        else
        {
            // Create a simple filter dialog if no window is assigned
            CreateSimpleFilterDialog();
        }
    }

    /// <summary>
    /// Handles refresh button press events.
    /// </summary>
    private void OnRefreshButtonPressed()
    {
        LoadInventoryData();
        _modifiedCells.Clear();
        SaveButton.Disabled = true;
        StatusLabel.Text = "Data refreshed";
    }

    /// <summary>
    /// Handles save button press events.
    /// </summary>
    private async void OnSaveButtonPressed()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "No edit permissions";
            return;
        }

        await SaveChangesToDatabase();
    }

    /// <summary>
    /// Handles search bar text changes for real-time filtering.
    /// </summary>
    /// <param name="newText">New search text</param>
    private void OnSearchBarTextChanged(string newText)
    {
        // Implement real-time search with a small delay
        GetTree().CreateTimer(0.3).Timeout += () => ApplyFiltersAndSearch();
    }

    /// <summary>
    /// Handles tree item selection events.
    /// </summary>
    private void OnTreeItemSelected()
    {
        var selectedItem = InventoryTree.GetSelected();
        if (selectedItem != null)
        {
            var partNumber = selectedItem.GetText(0);
            StatusLabel.Text = $"Selected: {partNumber}";
        }
    }

    /// <summary>
    /// Handles tree item edit events.
    /// </summary>
    private void OnTreeItemEdited()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "Edit not allowed - insufficient permissions";
            return;
        }

        var editedItem = InventoryTree.GetEdited();
        var editedColumn = InventoryTree.GetEditedColumn();

        if (editedItem != null)
        {
            TrackCellModification(editedItem, editedColumn);
            SaveButton.Disabled = false;
            StatusLabel.Text = "Changes pending - click Save to commit";
        }
    }

    /// <summary>
    /// Handles column title click events for sorting.
    /// </summary>
    /// <param name="column">Column index that was clicked</param>
    /// <param name="mouseButtonIndex">Mouse button that was clicked</param>
    private void OnColumnTitleClicked(long column, long mouseButtonIndex)
    {
        if (mouseButtonIndex == (long)MouseButton.Left)
        {
            SortByColumn((int)column);
        }
    }

    /// <summary>
    /// Handles filter window close events.
    /// </summary>
    private void OnFilterWindowCloseRequested()
    {
        FilterWindow?.Hide();
    }
    #endregion

    #region Search and Filtering
    /// <summary>
    /// Applies current filters and search criteria to the data.
    /// </summary>
    private async void ApplyFiltersAndSearch()
    {
        if (_originalInventoryData == null) return;

        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Applying filters...";

            var filteredData = _originalInventoryData.Copy();
            filteredData.Clear();

            var searchText = SearchBar.Text.Trim().ToLower();

            foreach (DataRow row in _originalInventoryData.Rows)
            {
                bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                    row.ItemArray.Any(field => field?.ToString().ToLower().Contains(searchText) == true);

                bool matchesFilters = true;
                foreach (var filter in _columnFilters)
                {
                    if (!string.IsNullOrEmpty(filter.Value))
                    {
                        var columnValue = row[filter.Key]?.ToString().ToLower() ?? "";
                        if (!columnValue.Contains(filter.Value.ToLower()))
                        {
                            matchesFilters = false;
                            break;
                        }
                    }
                }

                if (matchesSearch && matchesFilters)
                {
                    filteredData.ImportRow(row);
                }
            }

            _filteredInventoryData = filteredData;
            await PopulateTreeWithData(_filteredInventoryData);

            StatusLabel.Text = $"Showing {_filteredInventoryData.Rows.Count} of {_originalInventoryData.Rows.Count} items";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error applying filters: {ex.Message}");
            StatusLabel.Text = "Error applying filters";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Sorts the data by the specified column.
    /// </summary>
    /// <param name="columnIndex">Index of column to sort by</param>
    private async void SortByColumn(int columnIndex)
    {
        if (_filteredInventoryData == null || columnIndex >= _filteredInventoryData.Columns.Count) return;

        try
        {
            ShowLoadingState(true);
            var columnName = _filteredInventoryData.Columns[columnIndex].ColumnName;
            StatusLabel.Text = $"Sorting by {columnName}...";

            // Create a new DataTable with the same structure
            var sortedData = _filteredInventoryData.Clone(); // Clone structure, not data

            // Get all rows and sort them
            var allRows = _filteredInventoryData.Select();
            DataRow[] sortedRows;

            // Handle numeric sorting for Quantity column
            if (columnName == "Quantity")
            {
                sortedRows = allRows.OrderBy(row =>
                {
                    var value = row[columnIndex]?.ToString();
                    return int.TryParse(value, out int numValue) ? numValue : 0;
                }).ToArray();
            }
            else
            {
                // String sorting for other columns
                sortedRows = allRows.OrderBy(row => row[columnIndex]?.ToString() ?? "").ToArray();
            }

            // Add sorted rows to new table
            foreach (var row in sortedRows)
            {
                var newRow = sortedData.NewRow();
                newRow.ItemArray = row.ItemArray;
                sortedData.Rows.Add(newRow);
            }

            _filteredInventoryData = sortedData;
            await PopulateTreeWithData(_filteredInventoryData);

            StatusLabel.Text = $"Sorted by {columnName} ({_filteredInventoryData.Rows.Count} items)";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error sorting data: {ex.Message}");
            StatusLabel.Text = "Error sorting data";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Creates a simple filter dialog for column-based filtering.
    /// </summary>
    private void CreateSimpleFilterDialog()
    {
        var dialog = new AcceptDialog();
        dialog.Title = "Column Filters";
        dialog.Size = new Vector2I(400, 300);

        var vbox = new VBoxContainer();
        dialog.AddChild(vbox);

        // Add filter controls for each column
        var columnNames = new[] { "Part Number", "Quantity", "Room", "Bin", "Alternate Part Number" };

        foreach (var columnName in columnNames)
        {
            var hbox = new HBoxContainer();
            vbox.AddChild(hbox);

            var label = new Label();
            label.Text = $"{columnName}:";
            label.CustomMinimumSize = new Vector2(120, 0);
            hbox.AddChild(label);

            var lineEdit = new LineEdit();
            lineEdit.PlaceholderText = $"Filter {columnName}...";
            lineEdit.Text = _columnFilters.GetValueOrDefault(columnName, "");
            lineEdit.TextChanged += (newText) => {
                _columnFilters[columnName] = newText;
            };
            hbox.AddChild(lineEdit);
        }

        // Add clear filters button
        var clearButton = new Button();
        clearButton.Text = "Clear All Filters";
        clearButton.Pressed += () => {
            _columnFilters.Clear();
            dialog.QueueFree();
            ApplyFiltersAndSearch();
        };
        vbox.AddChild(clearButton);

        dialog.PopupCentered();
        GetTree().Root.AddChild(dialog);

        dialog.Confirmed += () => {
            ApplyFiltersAndSearch();
        };
    }
    #endregion

    #region Data Modification
    /// <summary>
    /// Tracks modifications made to cells for later saving.
    /// </summary>
    /// <param name="item">The tree item that was modified</param>
    /// <param name="column">The column index that was modified</param>
    private void TrackCellModification(TreeItem item, int column)
    {
        try
        {
            var partNumber = item.GetText(0); // Part Number is the primary key
            var columnName = _filteredInventoryData.Columns[column].ColumnName;
            var newValue = item.GetText(column);

            if (!_modifiedCells.ContainsKey(partNumber))
            {
                _modifiedCells[partNumber] = new Dictionary<string, object>();
            }

            _modifiedCells[partNumber][columnName] = newValue;

            GD.Print($"Tracked modification: {partNumber}.{columnName} = {newValue}");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error tracking cell modification: {ex.Message}");
        }
    }

    /// <summary>
    /// Saves all tracked changes back to the database.
    /// </summary>
    private async Task SaveChangesToDatabase()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "No edit permissions";
            return;
        }

        if (_modifiedCells.Count == 0)
        {
            StatusLabel.Text = "No changes to save";
            return;
        }

        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Saving changes...";

            int savedCount = 0;
            foreach (var partModifications in _modifiedCells)
            {
                var partNumber = partModifications.Key;
                var modifications = partModifications.Value;

                await SavePartModifications(partNumber, modifications);
                savedCount++;
            }

            _modifiedCells.Clear();
            SaveButton.Disabled = true;

            StatusLabel.Text = $"Saved {savedCount} item(s) successfully";
            ShowLoadingState(false);

            // Refresh data to show updated values
            LoadInventoryData();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error saving changes: {ex.Message}");
            StatusLabel.Text = "Error saving changes";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Saves modifications for a specific part to the database.
    /// </summary>
    /// <param name="partNumber">Part number to update</param>
    /// <param name="modifications">Dictionary of column changes</param>
    private async Task SavePartModifications(string partNumber, Dictionary<string, object> modifications)
    {
        var updateParts = new List<string>();
        var parameters = new List<object> { partNumber };

        foreach (var modification in modifications)
        {
            var columnName = modification.Key;
            var value = modification.Value;

            // Map display column names to database column names
            var dbColumnName = columnName switch
            {
                "Part Number" => "Part_Number",
                "Quantity" => "QTY",
                "Room" => "ROOM",
                "Bin" => "Bin",
                "Alternate Part Number" => "ALT_P_N",
                _ => columnName
            };

            updateParts.Add($"[{dbColumnName}] = @param{parameters.Count}");
            parameters.Add(value);
        }

        if (updateParts.Count > 0)
        {
            var updateQuery = $@"
                UPDATE [SysTest].[dbo].[Inventory]
                SET {string.Join(", ", updateParts)}
                WHERE [Part_Number] = @param0";

            // For now, using a simple string-based query
            // In production, you'd want to use proper parameterized queries
            var simpleQuery = $@"
                UPDATE [SysTest].[dbo].[Inventory]
                SET {string.Join(", ", modifications.Select(m => $"[{GetDbColumnName(m.Key)}] = '{m.Value}'"))}
                WHERE [Part_Number] = '{partNumber}'";

            await SqlHandler.QueryAsync(simpleQuery);
        }
    }

    /// <summary>
    /// Maps display column names to database column names.
    /// </summary>
    /// <param name="displayName">Display column name</param>
    /// <returns>Database column name</returns>
    private static string GetDbColumnName(string displayName)
    {
        return displayName switch
        {
            "Part Number" => "Part_Number",
            "Quantity" => "QTY",
            "Room" => "ROOM",
            "Bin" => "Bin",
            "Alternate Part Number" => "ALT_P_N",
            _ => displayName
        };
    }
    #endregion

    #region Public API
    /// <summary>
    /// Public method to add a new user to the approved users list.
    /// </summary>
    /// <param name="username">Username to add</param>
    public void AddApprovedUser(string username)
    {
        if (!string.IsNullOrEmpty(username))
        {
            _approvedUsers.Add(username.ToLower());
            GD.Print($"Added approved user: {username}");
        }
    }

    /// <summary>
    /// Public method to remove a user from the approved users list.
    /// </summary>
    /// <param name="username">Username to remove</param>
    public void RemoveApprovedUser(string username)
    {
        if (!string.IsNullOrEmpty(username))
        {
            _approvedUsers.Remove(username.ToLower());
            GD.Print($"Removed approved user: {username}");
        }
    }

    /// <summary>
    /// Public method to check if a user has edit permissions.
    /// </summary>
    /// <param name="username">Username to check</param>
    /// <returns>True if user has edit permissions</returns>
    public bool CheckUserPermissions(string username)
    {
        return _approvedUsers.Contains(username?.ToLower() ?? "");
    }
    #endregion
}
