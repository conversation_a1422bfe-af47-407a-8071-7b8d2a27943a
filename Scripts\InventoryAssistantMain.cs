using Godot;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

/// <summary>
/// Excel-like inventory management interface with user authentication, editable cells,
/// column resizing, filtering, and search capabilities.
/// </summary>
public partial class InventoryAssistantMain : PanelContainer
{
    #region UI Component References
    /// <summary>Tree control for displaying inventory data in a spreadsheet-like format</summary>
    [Export] public Tree InventoryTree { get; set; }

    /// <summary>Search input field for filtering inventory items</summary>
    [Export] public LineEdit SearchBar { get; set; }

    /// <summary>Button to trigger search operations</summary>
    [Export] public Button SearchButton { get; set; }

    /// <summary>Button to open advanced filter options</summary>
    [Export] public Button FilterButton { get; set; }

    /// <summary>Button to refresh data from database</summary>
    [Export] public Button RefreshButton { get; set; }

    /// <summary>Button to save changes back to database</summary>
    [Export] public Button SaveButton { get; set; }

    /// <summary>Button to add new parts to inventory</summary>
    [Export] public Button AddPartButton { get; set; }

    /// <summary>Button to delete selected parts from inventory</summary>
    [Export] public Button DeletePartButton { get; set; }

    /// <summary>Label showing current user and edit permissions</summary>
    [Export] public Label UserStatusLabel { get; set; }

    /// <summary>Progress bar for data loading operations</summary>
    [Export] public ProgressBar LoadingProgressBar { get; set; }

    /// <summary>Label for status messages</summary>
    [Export] public Label StatusLabel { get; set; }

    /// <summary>Container for filter controls</summary>
    [Export] public VBoxContainer FilterContainer { get; set; }

    /// <summary>Window for advanced filter options</summary>
    [Export] public Window FilterWindow { get; set; }

    // Parts Request System UI Components
    /// <summary>Main tab container for switching between Inventory and Requests</summary>
    [Export] public TabContainer MainTabContainer { get; set; }

    /// <summary>Search field for finding parts to add to requests</summary>
    [Export] public LineEdit PartSearchField { get; set; }

    /// <summary>Tree showing available parts for selection</summary>
    [Export] public Tree PartSelectionTree { get; set; }

    /// <summary>Tree showing items in current request</summary>
    [Export] public Tree RequestItemsTree { get; set; }

    /// <summary>Priority selector for requests</summary>
    [Export] public OptionButton PrioritySelector { get; set; }

    /// <summary>Required date input for when parts are needed</summary>
    [Export] public LineEdit RequiredDateField { get; set; }

    /// <summary>Text area for request justification</summary>
    [Export] public TextEdit JustificationText { get; set; }

    /// <summary>Button to add selected part to request</summary>
    [Export] public Button AddToRequestButton { get; set; }

    /// <summary>Button to submit the request</summary>
    [Export] public Button SubmitRequestButton { get; set; }

    /// <summary>Tree showing all requests for management</summary>
    [Export] public Tree RequestsManagementTree { get; set; }

    /// <summary>Filter for request status</summary>
    [Export] public OptionButton RequestStatusFilter { get; set; }

    /// <summary>Filter for request priority</summary>
    [Export] public OptionButton RequestPriorityFilter { get; set; }

    /// <summary>Button to approve selected requests</summary>
    [Export] public Button ApproveRequestButton { get; set; }

    /// <summary>Button to reject selected requests</summary>
    [Export] public Button RejectRequestButton { get; set; }

    /// <summary>Panel showing detailed request information</summary>
    [Export] public RichTextLabel RequestDetailsPanel { get; set; }

    /// <summary>Label showing total estimated cost</summary>
    [Export] public Label TotalCostLabel { get; set; }

    /// <summary>Input field for requester name</summary>
    [Export] public LineEdit RequesterNameField { get; set; }

    /// <summary>Input field for department</summary>
    [Export] public LineEdit DepartmentField { get; set; }
    #endregion

    #region Dependencies
    /// <summary>Database handler for SQL operations</summary>
    public SQLHandler SqlHandler { get; private set; }

    /// <summary>Global signal handler for cross-component communication</summary>
    public CustomSignals CustomSignals { get; private set; }
    #endregion

    #region Data Management
    /// <summary>Original unfiltered inventory data from database</summary>
    private DataTable _originalInventoryData;

    /// <summary>Currently displayed filtered data</summary>
    private DataTable _filteredInventoryData;

    /// <summary>Dictionary to track modified cells for saving - using row index as key</summary>
    private Dictionary<int, Dictionary<string, object>> _modifiedCells = new();

    /// <summary>Dictionary to track original values for modified rows - for database updates</summary>
    private Dictionary<int, Dictionary<string, object>> _originalRowValues = new();

    /// <summary>List of column names that can be edited</summary>
    private readonly List<string> _editableColumns = new() { "Part Number", "Quantity", "Room", "Bin", "Alternate Part Number" };

    /// <summary>Dictionary storing column filters</summary>
    private Dictionary<string, string> _columnFilters = new();
    #endregion

    #region Parts Request Data
    /// <summary>Current request being created</summary>
    private PartRequest _currentRequest;

    /// <summary>List of items in the current request</summary>
    private List<PartRequestItem> _currentRequestItems = new();

    /// <summary>All requests data for management view</summary>
    private DataTable _allRequestsData;

    /// <summary>Filtered requests data for display</summary>
    private DataTable _filteredRequestsData;
    #endregion

    #region User Authentication
    /// <summary>Current logged-in user</summary>
    public string CurrentUser { get; private set; } = "Guest";

    /// <summary>Whether current user has edit permissions</summary>
    public bool HasEditPermissions { get; private set; } = false;

    /// <summary>List of users with edit permissions</summary>
    private readonly HashSet<string> _approvedUsers = new()
    {
        "admin", "inventory_manager", "supervisor"
    };
    #endregion

    #region Godot Lifecycle
    /// <summary>
    /// Called when the node enters the scene tree for the first time.
    /// Initializes the inventory assistant and loads data.
    /// </summary>
    public override void _Ready()
    {
        InitializeInventoryAssistant();
        ConnectSignals();
        SetupTreeColumns();
        LoadInventoryData();
    }
    #endregion

    #region Initialization
    /// <summary>
    /// Initializes the inventory assistant with default settings.
    /// </summary>
    private void InitializeInventoryAssistant()
    {
        // Initialize data structures
        _modifiedCells = new Dictionary<int, Dictionary<string, object>>();
        _originalRowValues = new Dictionary<int, Dictionary<string, object>>();
        _columnFilters = new Dictionary<string, string>();

        // Set initial UI state
        SaveButton.Disabled = true;
        LoadingProgressBar.Visible = false;
        StatusLabel.Text = "Ready";

        // Set up user authentication (in real app, get from login system)
        SetCurrentUser("admin"); // TODO: Replace with actual user authentication
    }

    /// <summary>
    /// Connects UI element signals to their respective event handlers.
    /// </summary>
    private void ConnectSignals()
    {
        try
        {
            SqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
            CustomSignals = GetNode<CustomSignals>("/root/CustomSignals");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error connecting to global nodes: {ex.Message}");
        }

        // Connect UI signals
        SearchButton.Pressed += OnSearchButtonPressed;
        FilterButton.Pressed += OnFilterButtonPressed;
        RefreshButton.Pressed += OnRefreshButtonPressed;
        SaveButton.Pressed += OnSaveButtonPressed;
        AddPartButton.Pressed += OnAddPartButtonPressed;
        DeletePartButton.Pressed += OnDeletePartButtonPressed;
        SearchBar.TextChanged += OnSearchBarTextChanged;

        // Connect Parts Request UI signals
        ConnectPartsRequestSignals();

        // Connect tree signals
        InventoryTree.ItemSelected += OnTreeItemSelected;
        InventoryTree.ItemEdited += OnTreeItemEdited;
        InventoryTree.ColumnTitleClicked += OnColumnTitleClicked;

        // Connect filter window signals if available
        if (FilterWindow != null)
        {
            FilterWindow.CloseRequested += OnFilterWindowCloseRequested;
        }
    }

    /// <summary>
    /// Sets up the tree columns with proper configuration for Excel-like behavior.
    /// </summary>
    private void SetupTreeColumns()
    {
        InventoryTree.Clear();
        InventoryTree.Columns = 5; // Part Number, Quantity, Room, Bin, Alternate Part Number
        InventoryTree.ColumnTitlesVisible = true;
        InventoryTree.HideRoot = true;
        InventoryTree.SelectMode = Tree.SelectModeEnum.Single;

        // Configure columns
        var columnTitles = new[] { "Part Number", "Quantity", "Room", "Bin", "Alternate Part Number" };
        var columnWidths = new[] { 200, 100, 100, 100, 200 };

        for (int i = 0; i < columnTitles.Length; i++)
        {
            InventoryTree.SetColumnTitle(i, columnTitles[i]);
            InventoryTree.SetColumnCustomMinimumWidth(i, columnWidths[i]);
            InventoryTree.SetColumnExpand(i, true);
            InventoryTree.SetColumnClipContent(i, false);
        }
    }

    /// <summary>
    /// Connects signals for the parts request system UI components.
    /// </summary>
    private void ConnectPartsRequestSignals()
    {
        // Only connect if components are assigned
        if (PartSearchField != null)
            PartSearchField.TextChanged += OnPartSearchFieldTextChanged;

        if (AddToRequestButton != null)
            AddToRequestButton.Pressed += OnAddToRequestButtonPressed;

        if (SubmitRequestButton != null)
            SubmitRequestButton.Pressed += OnSubmitRequestButtonPressed;

        if (PartSelectionTree != null)
            PartSelectionTree.ItemSelected += OnPartSelectionTreeItemSelected;

        if (RequestItemsTree != null)
            RequestItemsTree.ItemSelected += OnRequestItemsTreeItemSelected;

        if (RequestsManagementTree != null)
            RequestsManagementTree.ItemSelected += OnRequestsManagementTreeItemSelected;

        if (ApproveRequestButton != null)
            ApproveRequestButton.Pressed += OnApproveRequestButtonPressed;

        if (RejectRequestButton != null)
            RejectRequestButton.Pressed += OnRejectRequestButtonPressed;

        if (RequestStatusFilter != null)
            RequestStatusFilter.ItemSelected += OnRequestStatusFilterChanged;

        if (RequestPriorityFilter != null)
            RequestPriorityFilter.ItemSelected += OnRequestPriorityFilterChanged;

        if (MainTabContainer != null)
            MainTabContainer.TabChanged += OnMainTabChanged;
    }
    #endregion

    #region User Authentication
    /// <summary>
    /// Sets the current user and updates permissions.
    /// </summary>
    /// <param name="username">Username to set as current user</param>
    public void SetCurrentUser(string username)
    {
        CurrentUser = username ?? "Guest";
        HasEditPermissions = _approvedUsers.Contains(CurrentUser.ToLower());

        UpdateUserStatusDisplay();
        UpdateEditPermissions();

        GD.Print($"User set to: {CurrentUser}, Edit permissions: {HasEditPermissions}");
    }

    /// <summary>
    /// Updates the user status display in the UI.
    /// </summary>
    private void UpdateUserStatusDisplay()
    {
        if (UserStatusLabel != null)
        {
            var permissionText = HasEditPermissions ? "Edit Enabled" : "Read Only";
            UserStatusLabel.Text = $"User: {CurrentUser} ({permissionText})";

            // Color code the status
            UserStatusLabel.Modulate = HasEditPermissions ? Colors.Green : Colors.Orange;
        }
    }

    /// <summary>
    /// Updates UI elements based on current user's edit permissions.
    /// </summary>
    private void UpdateEditPermissions()
    {
        if (SaveButton != null)
        {
            SaveButton.Disabled = !HasEditPermissions;
        }

        if (AddPartButton != null)
        {
            AddPartButton.Disabled = !HasEditPermissions;
        }

        if (DeletePartButton != null)
        {
            DeletePartButton.Disabled = !HasEditPermissions;
        }

        // Update tree editability
        if (InventoryTree != null)
        {
            // Tree editing will be controlled per-cell in the edit event handler
        }
    }
    #endregion

    #region Data Loading
    /// <summary>
    /// Loads inventory data from the database and populates the tree.
    /// </summary>
    public async void LoadInventoryData()
    {
        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Loading inventory data...";

            // Clear tracking dictionaries when loading new data
            _modifiedCells.Clear();
            _originalRowValues.Clear();

            _originalInventoryData = await SqlHandler.QueryAsync(@"
                SELECT
                    [Part_Number] AS [Part Number],
                    [QTY] AS [Quantity],
                    [ROOM] AS [Room],
                    [Bin] AS [Bin],
                    [ALT_P_N] AS [Alternate Part Number]
                FROM [SysTest].[dbo].[Inventory]
                ORDER BY [Part_Number]");

            _filteredInventoryData = _originalInventoryData.Copy();

            await PopulateTreeWithData(_filteredInventoryData);

            StatusLabel.Text = $"Loaded {_originalInventoryData.Rows.Count} inventory items";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error loading inventory data: {ex.Message}");
            StatusLabel.Text = "Error loading data";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Populates the tree with data from a DataTable.
    /// </summary>
    /// <param name="data">DataTable containing inventory data</param>
    private async Task PopulateTreeWithData(DataTable data)
    {
        InventoryTree.Clear();
        var root = InventoryTree.CreateItem();
        InventoryTree.HideRoot = true;

        LoadingProgressBar.MaxValue = data.Rows.Count;
        LoadingProgressBar.Value = 0;

        for (int i = 0; i < data.Rows.Count; i++)
        {
            var row = data.Rows[i];
            var item = InventoryTree.CreateItem(root);

            // Store original row values for tracking changes
            var originalValues = new Dictionary<string, object>();

            // Populate columns
            for (int col = 0; col < data.Columns.Count; col++)
            {
                var value = row[col]?.ToString() ?? "";
                var columnName = data.Columns[col].ColumnName;

                item.SetText(col, value);

                // Store original value
                originalValues[columnName] = value;

                // Set cell as editable if user has permissions and column is editable
                if (HasEditPermissions && _editableColumns.Contains(columnName))
                {
                    item.SetEditable(col, true);
                }

                // Set text alignment
                if (columnName == "Part Number")
                {
                    item.SetTextAlignment(col, HorizontalAlignment.Left);
                }
                else
                {
                    item.SetTextAlignment(col, HorizontalAlignment.Center);
                }
            }

            // Store original values using row index as key
            _originalRowValues[i] = originalValues;

            LoadingProgressBar.Value = i + 1;

            // Yield control periodically to prevent UI freezing
            if (i % 50 == 0)
            {
                await ToSignal(GetTree().CreateTimer(0.001), "timeout");
            }
        }
    }

    /// <summary>
    /// Shows or hides the loading state UI elements.
    /// </summary>
    /// <param name="isLoading">Whether to show loading state</param>
    private void ShowLoadingState(bool isLoading)
    {
        if (LoadingProgressBar != null)
        {
            LoadingProgressBar.Visible = isLoading;
        }

        if (RefreshButton != null)
        {
            RefreshButton.Disabled = isLoading;
        }
    }
    #endregion

    #region Event Handlers
    /// <summary>
    /// Handles search button press events.
    /// </summary>
    private void OnSearchButtonPressed()
    {
        ApplyFiltersAndSearch();
    }

    /// <summary>
    /// Handles filter button press events.
    /// </summary>
    private void OnFilterButtonPressed()
    {
        if (FilterWindow != null && IsInstanceValid(FilterWindow))
        {
            FilterWindow.Show();
            GD.Print("Showing assigned FilterWindow");
        }
        else
        {
            // Create a simple filter dialog if no window is assigned
            GD.Print("Creating simple filter dialog");
            CreateSimpleFilterDialog();
        }
    }

    /// <summary>
    /// Handles refresh button press events.
    /// </summary>
    private void OnRefreshButtonPressed()
    {
        LoadInventoryData();
        _modifiedCells.Clear();
        _originalRowValues.Clear();
        SaveButton.Disabled = true;
        StatusLabel.Text = "Data refreshed";
    }

    /// <summary>
    /// Handles save button press events.
    /// </summary>
    private async void OnSaveButtonPressed()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "No edit permissions";
            return;
        }

        await SaveChangesToDatabase();
    }

    /// <summary>
    /// Handles search bar text changes for real-time filtering.
    /// </summary>
    /// <param name="newText">New search text</param>
    private void OnSearchBarTextChanged(string newText)
    {
        // Implement real-time search with a small delay
        GetTree().CreateTimer(0.3).Timeout += () => ApplyFiltersAndSearch();
    }

    /// <summary>
    /// Handles tree item selection events.
    /// </summary>
    private void OnTreeItemSelected()
    {
        var selectedItem = InventoryTree.GetSelected();
        if (selectedItem != null)
        {
            var partNumber = selectedItem.GetText(0);
            StatusLabel.Text = $"Selected: {partNumber}";
        }
    }

    /// <summary>
    /// Handles tree item edit events.
    /// </summary>
    private void OnTreeItemEdited()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "Edit not allowed - insufficient permissions";
            return;
        }

        var editedItem = InventoryTree.GetEdited();
        var editedColumn = InventoryTree.GetEditedColumn();

        if (editedItem != null)
        {
            TrackCellModification(editedItem, editedColumn);
            SaveButton.Disabled = false;
            StatusLabel.Text = "Changes pending - click Save to commit";
        }
    }

    /// <summary>
    /// Handles column title click events for sorting.
    /// </summary>
    /// <param name="column">Column index that was clicked</param>
    /// <param name="mouseButtonIndex">Mouse button that was clicked</param>
    private void OnColumnTitleClicked(long column, long mouseButtonIndex)
    {
        if (mouseButtonIndex == (long)MouseButton.Left)
        {
            SortByColumn((int)column);
        }
    }

    /// <summary>
    /// Handles filter window close events.
    /// </summary>
    private void OnFilterWindowCloseRequested()
    {
        FilterWindow?.Hide();
    }

    /// <summary>
    /// Handles add part button press events.
    /// </summary>
    private void OnAddPartButtonPressed()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "No edit permissions";
            return;
        }

        ShowAddPartDialog();
    }

    /// <summary>
    /// Handles delete part button press events.
    /// </summary>
    private void OnDeletePartButtonPressed()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "No edit permissions";
            return;
        }

        var selectedItem = InventoryTree.GetSelected();
        if (selectedItem == null)
        {
            StatusLabel.Text = "Please select a part to delete";
            return;
        }

        var partNumber = selectedItem.GetText(0);
        ShowDeleteConfirmationDialog(partNumber);
    }
    #endregion

    #region Parts Request Event Handlers
    /// <summary>
    /// Handles part search field text changes for real-time filtering.
    /// </summary>
    /// <param name="newText">New search text</param>
    private void OnPartSearchFieldTextChanged(string newText)
    {
        // TODO: Implement real-time part search filtering
        GD.Print($"Part search text changed: {newText}");
    }

    /// <summary>
    /// Handles add to request button press events.
    /// </summary>
    private void OnAddToRequestButtonPressed()
    {
        var selectedItem = PartSelectionTree?.GetSelected();
        if (selectedItem == null)
        {
            StatusLabel.Text = "Please select a part to add to request";
            return;
        }

        var partNumber = selectedItem.GetText(0);
        var availableQty = int.Parse(selectedItem.GetText(2));

        ShowAddPartToRequestDialog(partNumber, availableQty);
    }

    /// <summary>
    /// Handles submit request button press events.
    /// </summary>
    private async void OnSubmitRequestButtonPressed()
    {
        if (_currentRequestItems.Count == 0)
        {
            StatusLabel.Text = "Please add items to the request before submitting";
            return;
        }

        if (!ValidateRequestData())
        {
            return;
        }

        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Submitting request...";

            await SubmitRequestToDatabase();

            StatusLabel.Text = "Request submitted successfully!";
            ShowLoadingState(false);

            // Clear the current request and refresh UI
            InitializeRequestCreation();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error submitting request: {ex.Message}");
            StatusLabel.Text = "Error submitting request";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Handles part selection tree item selection events.
    /// </summary>
    private void OnPartSelectionTreeItemSelected()
    {
        // TODO: Implement part selection handling
        GD.Print("Part selection tree item selected");
    }

    /// <summary>
    /// Handles request items tree item selection events.
    /// </summary>
    private void OnRequestItemsTreeItemSelected()
    {
        // TODO: Implement request item selection handling
        GD.Print("Request items tree item selected");
    }

    /// <summary>
    /// Handles requests management tree item selection events.
    /// </summary>
    private void OnRequestsManagementTreeItemSelected()
    {
        // TODO: Implement request management selection handling
        GD.Print("Requests management tree item selected");
    }

    /// <summary>
    /// Handles approve request button press events.
    /// </summary>
    private async void OnApproveRequestButtonPressed()
    {
        var selectedItem = RequestsManagementTree?.GetSelected();
        if (selectedItem == null)
        {
            StatusLabel.Text = "Please select a request to approve";
            return;
        }

        var requestId = selectedItem.GetText(0); // Assuming RequestID is in first column
        await UpdateRequestStatus(requestId, "Approved", CurrentUser);
    }

    /// <summary>
    /// Handles reject request button press events.
    /// </summary>
    private async void OnRejectRequestButtonPressed()
    {
        var selectedItem = RequestsManagementTree?.GetSelected();
        if (selectedItem == null)
        {
            StatusLabel.Text = "Please select a request to reject";
            return;
        }

        var requestId = selectedItem.GetText(0); // Assuming RequestID is in first column
        await UpdateRequestStatus(requestId, "Rejected", CurrentUser);
    }

    /// <summary>
    /// Handles request status filter changes.
    /// </summary>
    /// <param name="index">Selected filter index</param>
    private void OnRequestStatusFilterChanged(long index)
    {
        // TODO: Implement status filtering
        GD.Print($"Request status filter changed to index: {index}");
    }

    /// <summary>
    /// Handles request priority filter changes.
    /// </summary>
    /// <param name="index">Selected filter index</param>
    private void OnRequestPriorityFilterChanged(long index)
    {
        // TODO: Implement priority filtering
        GD.Print($"Request priority filter changed to index: {index}");
    }

    /// <summary>
    /// Handles main tab container tab changes.
    /// </summary>
    /// <param name="tab">Selected tab index</param>
    private void OnMainTabChanged(long tab)
    {
        // TODO: Implement tab change handling
        GD.Print($"Main tab changed to: {tab}");

        // Load appropriate data based on selected tab
        switch (tab)
        {
            case 0: // Inventory tab
                // Already loaded, but refresh status
                StatusLabel.Text = $"Showing {_filteredInventoryData?.Rows.Count ?? 0} of {_originalInventoryData?.Rows.Count ?? 0} items";
                break;
            case 1: // Request Parts tab
                InitializeRequestCreation();
                StatusLabel.Text = "Create new parts request";
                break;
            case 2: // My Requests tab
                StatusLabel.Text = "Loading your requests...";
                LoadUserRequests();
                break;
            case 3: // Manage Requests tab (admin only)
                if (HasEditPermissions)
                {
                    StatusLabel.Text = "Loading all requests for management...";
                    LoadAllRequests();
                }
                else
                {
                    StatusLabel.Text = "Access denied - Admin permissions required";
                }
                break;
        }
    }
    #endregion

    #region Search and Filtering
    /// <summary>
    /// Applies current filters and search criteria to the data.
    /// </summary>
    private async void ApplyFiltersAndSearch()
    {
        if (_originalInventoryData == null) return;

        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Applying filters...";

            var filteredData = _originalInventoryData.Copy();
            filteredData.Clear();

            var searchText = SearchBar.Text.Trim().ToLower();

            foreach (DataRow row in _originalInventoryData.Rows)
            {
                bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                    row.ItemArray.Any(field => field?.ToString().ToLower().Contains(searchText) == true);

                bool matchesFilters = true;
                foreach (var filter in _columnFilters)
                {
                    if (!string.IsNullOrEmpty(filter.Value))
                    {
                        var columnValue = row[filter.Key]?.ToString().ToLower() ?? "";
                        if (!columnValue.Contains(filter.Value.ToLower()))
                        {
                            matchesFilters = false;
                            break;
                        }
                    }
                }

                if (matchesSearch && matchesFilters)
                {
                    filteredData.ImportRow(row);
                }
            }

            _filteredInventoryData = filteredData;
            await PopulateTreeWithData(_filteredInventoryData);

            var activeFilters = _columnFilters.Count(f => !string.IsNullOrEmpty(f.Value));
            var filterText = activeFilters > 0 ? $" ({activeFilters} filters active)" : "";
            StatusLabel.Text = $"Showing {_filteredInventoryData.Rows.Count} of {_originalInventoryData.Rows.Count} items{filterText}";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error applying filters: {ex.Message}");
            StatusLabel.Text = "Error applying filters";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Sorts the data by the specified column.
    /// </summary>
    /// <param name="columnIndex">Index of column to sort by</param>
    private async void SortByColumn(int columnIndex)
    {
        if (_filteredInventoryData == null || columnIndex >= _filteredInventoryData.Columns.Count) return;

        try
        {
            ShowLoadingState(true);
            var columnName = _filteredInventoryData.Columns[columnIndex].ColumnName;
            StatusLabel.Text = $"Sorting by {columnName}...";

            // Create a new DataTable with the same structure
            var sortedData = _filteredInventoryData.Clone(); // Clone structure, not data

            // Get all rows and sort them
            var allRows = _filteredInventoryData.Select();
            DataRow[] sortedRows;

            // Handle numeric sorting for Quantity column
            if (columnName == "Quantity")
            {
                sortedRows = allRows.OrderBy(row =>
                {
                    var value = row[columnIndex]?.ToString();
                    return int.TryParse(value, out int numValue) ? numValue : 0;
                }).ToArray();
            }
            else
            {
                // String sorting for other columns
                sortedRows = allRows.OrderBy(row => row[columnIndex]?.ToString() ?? "").ToArray();
            }

            // Add sorted rows to new table
            foreach (var row in sortedRows)
            {
                var newRow = sortedData.NewRow();
                newRow.ItemArray = row.ItemArray;
                sortedData.Rows.Add(newRow);
            }

            _filteredInventoryData = sortedData;
            await PopulateTreeWithData(_filteredInventoryData);

            StatusLabel.Text = $"Sorted by {columnName} ({_filteredInventoryData.Rows.Count} items)";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error sorting data: {ex.Message}");
            StatusLabel.Text = "Error sorting data";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Creates a comprehensive filter dialog for column-based filtering.
    /// </summary>
    private void CreateSimpleFilterDialog()
    {
        var dialog = new AcceptDialog
        {
            Title = "Column Filters",
            Size = new Vector2I(450, 400)
        };

        // Create main content container
        var vbox = new VBoxContainer();
        dialog.AddChild(vbox);

        // Add title and instructions
        var titleLabel = new Label
        {
            Text = "Filter Inventory Data",
            HorizontalAlignment = HorizontalAlignment.Center
        };
        vbox.AddChild(titleLabel);

        var instructionLabel = new Label
        {
            Text = "Enter text to filter each column. Leave blank to show all items.",
            AutowrapMode = TextServer.AutowrapMode.WordSmart,
            HorizontalAlignment = HorizontalAlignment.Center,
            Modulate = Colors.Gray
        };
        vbox.AddChild(instructionLabel);

        // Add separator
        var separator = new HSeparator();
        vbox.AddChild(separator);

        // Store references to input fields for clearing
        var filterInputs = new Dictionary<string, LineEdit>();

        // Add filter controls for each column
        var columnNames = new[] { "Part Number", "Quantity", "Room", "Bin", "Alternate Part Number" };
        var columnDescriptions = new[] {
            "Filter by part number",
            "Filter by quantity (e.g., >10, <5, =0)",
            "Filter by room location",
            "Filter by bin location",
            "Filter by alternate part number"
        };

        for (int i = 0; i < columnNames.Length; i++)
        {
            var columnName = columnNames[i];

            // Column header
            var headerLabel = new Label
            {
                Text = columnName,
                HorizontalAlignment = HorizontalAlignment.Left
            };
            headerLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat { BgColor = Colors.DarkGray });
            vbox.AddChild(headerLabel);

            var hbox = new HBoxContainer();
            vbox.AddChild(hbox);

            var lineEdit = new LineEdit
            {
                PlaceholderText = columnDescriptions[i],
                Text = _columnFilters.GetValueOrDefault(columnName, ""),
                SizeFlagsHorizontal = Control.SizeFlags.ExpandFill
            };

            // Update filter when text changes
            lineEdit.TextChanged += (newText) => {
                _columnFilters[columnName] = newText;
            };

            hbox.AddChild(lineEdit);
            filterInputs[columnName] = lineEdit;

            // Add clear button for individual filter
            var clearIndividualButton = new Button
            {
                Text = "Clear",
                CustomMinimumSize = new Vector2(60, 0)
            };
            clearIndividualButton.Pressed += () => {
                lineEdit.Text = "";
                _columnFilters[columnName] = "";
            };
            hbox.AddChild(clearIndividualButton);

            // Add some spacing
            var spacer = new Control { CustomMinimumSize = new Vector2(0, 5) };
            vbox.AddChild(spacer);
        }

        // Add separator before buttons
        var separator2 = new HSeparator();
        vbox.AddChild(separator2);

        // Add button container
        var buttonHBox = new HBoxContainer();
        vbox.AddChild(buttonHBox);

        // Add clear all filters button
        var clearAllButton = new Button
        {
            Text = "Clear All Filters",
            SizeFlagsHorizontal = Control.SizeFlags.ExpandFill
        };
        clearAllButton.Pressed += () => {
            _columnFilters.Clear();
            foreach (var input in filterInputs.Values)
            {
                input.Text = "";
            }
            ApplyFiltersAndSearch();
        };
        buttonHBox.AddChild(clearAllButton);

        // Add apply filters button
        var applyButton = new Button
        {
            Text = "Apply Filters",
            SizeFlagsHorizontal = Control.SizeFlags.ExpandFill
        };
        applyButton.Pressed += () => {
            ApplyFiltersAndSearch();
            dialog.Hide();
        };
        buttonHBox.AddChild(applyButton);

        // Add status label
        var statusLabel = new Label
        {
            Text = $"Currently showing {_filteredInventoryData?.Rows.Count ?? 0} of {_originalInventoryData?.Rows.Count ?? 0} items",
            HorizontalAlignment = HorizontalAlignment.Center,
            Modulate = Colors.LightBlue
        };
        vbox.AddChild(statusLabel);

        // Add dialog to tree first, then popup
        GetTree().Root.AddChild(dialog);
        dialog.PopupCentered();

        GD.Print($"Filter dialog created with {filterInputs.Count} input fields");
        GD.Print($"Dialog size: {dialog.Size}");
        GD.Print($"VBox children count: {vbox.GetChildCount()}");

        // Auto-cleanup when dialog is closed
        dialog.CloseRequested += () => {
            GD.Print("Filter dialog closed");
            dialog.QueueFree();
        };

        dialog.Confirmed += () => {
            GD.Print("Filter dialog confirmed");
            ApplyFiltersAndSearch();
        };
    }
    #endregion

    #region Part Management
    /// <summary>
    /// Shows a dialog for adding a new part to the inventory.
    /// </summary>
    private void ShowAddPartDialog()
    {
        var dialog = new AcceptDialog
        {
            Title = "Add New Part",
            Size = new Vector2I(500, 400)
        };

        var vbox = new VBoxContainer();
        dialog.AddChild(vbox);

        // Create input fields for each column
        var inputs = new Dictionary<string, LineEdit>();
        var columnNames = new[] { "Part Number", "Quantity", "Room", "Bin", "Alternate Part Number" };
        var placeholders = new[] { "Enter part number...", "Enter quantity...", "Enter room...", "Enter bin...", "Enter alternate part number..." };
        var required = new[] { true, true, false, false, false }; // Part Number and Quantity are required

        for (int i = 0; i < columnNames.Length; i++)
        {
            var hbox = new HBoxContainer();
            vbox.AddChild(hbox);

            var label = new Label
            {
                Text = columnNames[i] + (required[i] ? " *:" : ":"),
                CustomMinimumSize = new Vector2(150, 0)
            };
            hbox.AddChild(label);

            var lineEdit = new LineEdit
            {
                PlaceholderText = placeholders[i],
                SizeFlagsHorizontal = Control.SizeFlags.ExpandFill
            };
            hbox.AddChild(lineEdit);
            inputs[columnNames[i]] = lineEdit;

            // Set numeric input for quantity
            if (columnNames[i] == "Quantity")
            {
                lineEdit.TextChanged += (text) => {
                    // Only allow numeric input
                    if (!string.IsNullOrEmpty(text) && !int.TryParse(text, out _))
                    {
                        lineEdit.Text = new string(text.Where(char.IsDigit).ToArray());
                        lineEdit.CaretColumn = lineEdit.Text.Length;
                    }
                };
            }
        }

        // Add validation message label
        var validationLabel = new Label
        {
            Text = "* Required fields",
            Modulate = Colors.Orange
        };
        vbox.AddChild(validationLabel);

        // Add dialog to tree first, then popup
        GetTree().Root.AddChild(dialog);
        dialog.PopupCentered();

        dialog.Confirmed += async () => {
            await AddNewPartToDatabase(inputs);
        };
    }

    /// <summary>
    /// Shows a confirmation dialog for deleting a part.
    /// </summary>
    /// <param name="partNumber">Part number to delete</param>
    private void ShowDeleteConfirmationDialog(string partNumber)
    {
        var dialog = new ConfirmationDialog
        {
            DialogText = $"Are you sure you want to delete part '{partNumber}'?\n\nThis action cannot be undone.",
            Title = "Confirm Delete"
        };

        // Add dialog to tree first, then popup
        GetTree().Root.AddChild(dialog);
        dialog.PopupCentered();

        dialog.Confirmed += async () => {
            await DeletePartFromDatabase(partNumber);
        };
    }

    /// <summary>
    /// Adds a new part to the database.
    /// </summary>
    /// <param name="inputs">Dictionary of input fields with values</param>
    private async Task AddNewPartToDatabase(Dictionary<string, LineEdit> inputs)
    {
        try
        {
            // Validate required fields
            var partNumber = inputs["Part Number"].Text.Trim();
            var quantityText = inputs["Quantity"].Text.Trim();

            if (string.IsNullOrEmpty(partNumber))
            {
                StatusLabel.Text = "Part Number is required";
                return;
            }

            if (string.IsNullOrEmpty(quantityText) || !int.TryParse(quantityText, out int quantity))
            {
                StatusLabel.Text = "Valid Quantity is required";
                return;
            }

            // Check if part already exists
            var existingPart = await SqlHandler.QueryAsync($"SELECT COUNT(*) as Count FROM [SysTest].[dbo].[Inventory] WHERE [Part_Number] = '{partNumber}'");
            if (existingPart.Rows.Count > 0 && Convert.ToInt32(existingPart.Rows[0]["Count"]) > 0)
            {
                StatusLabel.Text = "Part Number already exists";
                return;
            }

            ShowLoadingState(true);
            StatusLabel.Text = "Adding new part...";

            // Get values from inputs
            var room = inputs["Room"].Text.Trim();
            var bin = inputs["Bin"].Text.Trim();
            var altPartNumber = inputs["Alternate Part Number"].Text.Trim();

            // Insert new part
            var insertQuery = $@"
                INSERT INTO [SysTest].[dbo].[Inventory]
                ([Part_Number], [QTY], [ROOM], [Bin], [ALT_P_N])
                VALUES ('{partNumber}', {quantity}, '{room}', '{bin}', '{altPartNumber}')";

            await SqlHandler.QueryAsync(insertQuery);

            StatusLabel.Text = $"Successfully added part: {partNumber}";
            ShowLoadingState(false);

            // Refresh the data to show the new part
            LoadInventoryData();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error adding new part: {ex.Message}");
            StatusLabel.Text = "Error adding new part";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Deletes a part from the database.
    /// </summary>
    /// <param name="partNumber">Part number to delete</param>
    private async Task DeletePartFromDatabase(string partNumber)
    {
        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = $"Deleting part: {partNumber}...";

            var deleteQuery = $@"
                DELETE FROM [SysTest].[dbo].[Inventory]
                WHERE [Part_Number] = '{partNumber}'";

            await SqlHandler.QueryAsync(deleteQuery);

            StatusLabel.Text = $"Successfully deleted part: {partNumber}";
            ShowLoadingState(false);

            // Refresh the data to remove the deleted part
            LoadInventoryData();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error deleting part: {ex.Message}");
            StatusLabel.Text = "Error deleting part";
            ShowLoadingState(false);
        }
    }
    #endregion

    #region Data Modification
    /// <summary>
    /// Tracks modifications made to cells for later saving.
    /// </summary>
    /// <param name="item">The tree item that was modified</param>
    /// <param name="column">The column index that was modified</param>
    private void TrackCellModification(TreeItem item, int column)
    {
        try
        {
            // Find the row index by getting the item's position in the tree
            var root = InventoryTree.GetRoot();
            var rowIndex = -1;
            var currentItem = root.GetFirstChild();
            var index = 0;

            while (currentItem != null)
            {
                if (currentItem == item)
                {
                    rowIndex = index;
                    break;
                }
                currentItem = currentItem.GetNext();
                index++;
            }

            if (rowIndex == -1)
            {
                GD.PrintErr("Could not find row index for modified item");
                return;
            }

            var columnName = _filteredInventoryData.Columns[column].ColumnName;
            var newValue = item.GetText(column);

            if (!_modifiedCells.ContainsKey(rowIndex))
            {
                _modifiedCells[rowIndex] = new Dictionary<string, object>();
            }

            _modifiedCells[rowIndex][columnName] = newValue;

            var displayId = _originalRowValues.ContainsKey(rowIndex) ?
                _originalRowValues[rowIndex].GetValueOrDefault("Part Number", "Unknown") : "Unknown";

            GD.Print($"Tracked modification: Row {rowIndex} ({displayId}).{columnName} = {newValue}");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error tracking cell modification: {ex.Message}");
        }
    }

    /// <summary>
    /// Saves all tracked changes back to the database.
    /// </summary>
    private async Task SaveChangesToDatabase()
    {
        if (!HasEditPermissions)
        {
            StatusLabel.Text = "No edit permissions";
            return;
        }

        if (_modifiedCells.Count == 0)
        {
            StatusLabel.Text = "No changes to save";
            return;
        }

        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Saving changes...";

            int savedCount = 0;
            foreach (var rowModifications in _modifiedCells)
            {
                var rowIndex = rowModifications.Key;
                var modifications = rowModifications.Value;

                await SaveRowModifications(rowIndex, modifications);
                savedCount++;
            }

            _modifiedCells.Clear();
            _originalRowValues.Clear();
            SaveButton.Disabled = true;

            StatusLabel.Text = $"Saved {savedCount} item(s) successfully";
            ShowLoadingState(false);

            // Refresh data to show updated values
            LoadInventoryData();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error saving changes: {ex.Message}");
            StatusLabel.Text = "Error saving changes";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Saves modifications for a specific row to the database.
    /// </summary>
    /// <param name="rowIndex">Row index to update</param>
    /// <param name="modifications">Dictionary of column changes</param>
    private async Task SaveRowModifications(int rowIndex, Dictionary<string, object> modifications)
    {
        if (!_originalRowValues.ContainsKey(rowIndex))
        {
            GD.PrintErr($"No original values found for row {rowIndex}");
            return;
        }

        // Get the original part number to use in WHERE clause
        var originalPartNumber = _originalRowValues[rowIndex]["Part Number"]?.ToString();
        if (string.IsNullOrEmpty(originalPartNumber))
        {
            GD.PrintErr($"No original part number found for row {rowIndex}");
            return;
        }

        var updateParts = new List<string>();

        foreach (var modification in modifications)
        {
            var columnName = modification.Key;
            var value = modification.Value;

            // Map display column names to database column names
            var dbColumnName = GetDbColumnName(columnName);
            updateParts.Add($"[{dbColumnName}] = '{value}'");
        }

        if (updateParts.Count > 0)
        {
            // Use original part number in WHERE clause since it might have been changed
            var updateQuery = $@"
                UPDATE [SysTest].[dbo].[Inventory]
                SET {string.Join(", ", updateParts)}
                WHERE [Part_Number] = '{originalPartNumber}'";

            await SqlHandler.QueryAsync(updateQuery);

            GD.Print($"Updated row {rowIndex} (original part: {originalPartNumber}) with {updateParts.Count} changes");
        }
    }

    /// <summary>
    /// Maps display column names to database column names.
    /// </summary>
    /// <param name="displayName">Display column name</param>
    /// <returns>Database column name</returns>
    private static string GetDbColumnName(string displayName)
    {
        return displayName switch
        {
            "Part Number" => "Part_Number",
            "Quantity" => "QTY",
            "Room" => "ROOM",
            "Bin" => "Bin",
            "Alternate Part Number" => "ALT_P_N",
            _ => displayName
        };
    }
    #endregion

    #region Parts Request Management
    /// <summary>
    /// Initializes the request creation interface.
    /// </summary>
    private void InitializeRequestCreation()
    {
        try
        {
            // Initialize new request
            _currentRequest = new PartRequest
            {
                RequestedBy = CurrentUser,
                Department = DepartmentField?.Text ?? "",
                RequestDate = DateTime.Now
            };

            _currentRequestItems.Clear();

            // Setup UI components
            SetupRequestCreationUI();

            // Load available parts for selection
            PopulatePartSelectionTree();

            GD.Print("Request creation initialized");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error initializing request creation: {ex.Message}");
        }
    }

    /// <summary>
    /// Sets up the UI components for request creation.
    /// </summary>
    private void SetupRequestCreationUI()
    {
        // Setup priority selector
        if (PrioritySelector != null)
        {
            PrioritySelector.Clear();
            PrioritySelector.AddItem("Low");
            PrioritySelector.AddItem("Normal");
            PrioritySelector.AddItem("High");
            PrioritySelector.AddItem("Urgent");
            PrioritySelector.Selected = 1; // Default to Normal
        }

        // Setup required date field
        if (RequiredDateField != null)
        {
            RequiredDateField.PlaceholderText = "YYYY-MM-DD (Required date for parts)";
            RequiredDateField.Text = DateTime.Now.AddDays(7).ToString("yyyy-MM-dd"); // Default to 1 week from now
        }

        // Setup requester and department fields
        if (RequesterNameField != null)
        {
            RequesterNameField.Text = CurrentUser;
        }

        // Clear request items tree
        if (RequestItemsTree != null)
        {
            SetupRequestItemsTree();
        }

        // Update total cost
        UpdateTotalCost();
    }

    /// <summary>
    /// Sets up the request items tree structure.
    /// </summary>
    private void SetupRequestItemsTree()
    {
        if (RequestItemsTree == null) return;

        RequestItemsTree.Clear();
        RequestItemsTree.Columns = 4; // Part Number, Description, Quantity, Justification
        RequestItemsTree.ColumnTitlesVisible = true;
        RequestItemsTree.HideRoot = true;

        var columnTitles = new[] { "Part Number", "Description", "Quantity", "Justification" };
        var columnWidths = new[] { 150, 250, 80, 200 };

        for (int i = 0; i < columnTitles.Length; i++)
        {
            RequestItemsTree.SetColumnTitle(i, columnTitles[i]);
            RequestItemsTree.SetColumnCustomMinimumWidth(i, columnWidths[i]);
            RequestItemsTree.SetColumnExpand(i, true);
        }
    }

    /// <summary>
    /// Populates the part selection tree with available inventory.
    /// </summary>
    private void PopulatePartSelectionTree()
    {
        if (PartSelectionTree == null || _originalInventoryData == null) return;

        try
        {
            PartSelectionTree.Clear();
            PartSelectionTree.Columns = 3; // Part Number, Description, Available Quantity
            PartSelectionTree.ColumnTitlesVisible = true;
            PartSelectionTree.HideRoot = true;

            var columnTitles = new[] { "Part Number", "Room/Bin", "Available Qty" };
            var columnWidths = new[] { 150, 150, 100 };

            for (int i = 0; i < columnTitles.Length; i++)
            {
                PartSelectionTree.SetColumnTitle(i, columnTitles[i]);
                PartSelectionTree.SetColumnCustomMinimumWidth(i, columnWidths[i]);
                PartSelectionTree.SetColumnExpand(i, true);
            }

            var root = PartSelectionTree.CreateItem();
            PartSelectionTree.HideRoot = true;

            // Populate with inventory data
            foreach (DataRow row in _originalInventoryData.Rows)
            {
                var item = PartSelectionTree.CreateItem(root);
                item.SetText(0, row["Part Number"]?.ToString() ?? "");
                item.SetText(1, $"{row["Room"]?.ToString() ?? ""}/{row["Bin"]?.ToString() ?? ""}");
                item.SetText(2, row["Quantity"]?.ToString() ?? "0");
            }

            GD.Print($"Populated part selection tree with {_originalInventoryData.Rows.Count} parts");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error populating part selection tree: {ex.Message}");
        }
    }

    /// <summary>
    /// Loads the current user's requests.
    /// </summary>
    private async void LoadUserRequests()
    {
        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Loading your requests...";

            var userRequestsQuery = $@"
                SELECT
                    RequestNumber,
                    Priority,
                    Status,
                    RequestDate,
                    RequiredDate,
                    TotalEstimatedCost,
                    Notes
                FROM [SysTest].[dbo].[PartRequests]
                WHERE RequestedBy = '{CurrentUser}'
                ORDER BY RequestDate DESC";

            var userRequestsData = await SqlHandler.QueryAsync(userRequestsQuery);

            // Populate user requests tree with data
            PopulateUserRequestsTree(userRequestsData);

            StatusLabel.Text = $"Loaded {userRequestsData.Rows.Count} requests";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error loading user requests: {ex.Message}");
            StatusLabel.Text = "Error loading requests";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Loads all requests for management view (admin only).
    /// </summary>
    private async void LoadAllRequests()
    {
        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = "Loading all requests for management...";

            var allRequestsQuery = @"
                SELECT
                    RequestID,
                    RequestNumber,
                    RequestedBy,
                    Department,
                    Priority,
                    Status,
                    RequestDate,
                    RequiredDate,
                    ApprovedBy,
                    ApprovedDate,
                    TotalEstimatedCost,
                    Notes
                FROM [SysTest].[dbo].[PartRequests]
                ORDER BY RequestDate DESC";

            _allRequestsData = await SqlHandler.QueryAsync(allRequestsQuery);
            _filteredRequestsData = _allRequestsData.Copy();

            // Populate management requests tree
            PopulateManagementRequestsTree(_filteredRequestsData);

            StatusLabel.Text = $"Loaded {_allRequestsData.Rows.Count} requests for management";
            ShowLoadingState(false);
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error loading all requests: {ex.Message}");
            StatusLabel.Text = "Error loading requests";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Updates the total estimated cost display.
    /// </summary>
    private void UpdateTotalCost()
    {
        if (TotalCostLabel == null) return;

        decimal totalCost = _currentRequestItems.Sum(item => item.TotalCost ?? 0);
        TotalCostLabel.Text = $"Total Estimated Cost: ${totalCost:F2}";
    }

    /// <summary>
    /// Shows dialog for adding a part to the current request.
    /// </summary>
    /// <param name="partNumber">Part number to add</param>
    /// <param name="availableQty">Available quantity in inventory</param>
    private void ShowAddPartToRequestDialog(string partNumber, int availableQty)
    {
        var dialog = new AcceptDialog
        {
            Title = $"Add {partNumber} to Request",
            Size = new Vector2I(400, 300)
        };

        var vbox = new VBoxContainer();
        dialog.AddChild(vbox);

        // Part info
        var partLabel = new Label { Text = $"Part Number: {partNumber}" };
        vbox.AddChild(partLabel);

        var availableLabel = new Label { Text = $"Available Quantity: {availableQty}" };
        vbox.AddChild(availableLabel);

        // Quantity input
        var qtyHBox = new HBoxContainer();
        vbox.AddChild(qtyHBox);

        var qtyLabel = new Label { Text = "Requested Quantity:" };
        qtyHBox.AddChild(qtyLabel);

        var qtySpinBox = new SpinBox
        {
            MinValue = 1,
            MaxValue = availableQty,
            Value = 1
        };
        qtyHBox.AddChild(qtySpinBox);

        // Justification input
        var justLabel = new Label { Text = "Justification (required):" };
        vbox.AddChild(justLabel);

        var justificationText = new TextEdit
        {
            PlaceholderText = "Explain why this part is needed...",
            CustomMinimumSize = new Vector2(0, 80)
        };
        vbox.AddChild(justificationText);

        GetTree().Root.AddChild(dialog);
        dialog.PopupCentered();

        dialog.Confirmed += () => {
            var quantity = (int)qtySpinBox.Value;
            var justification = justificationText.Text.Trim();

            if (string.IsNullOrEmpty(justification))
            {
                StatusLabel.Text = "Justification is required";
                return;
            }

            AddPartToCurrentRequest(partNumber, quantity, justification);
        };
    }

    /// <summary>
    /// Adds a part to the current request.
    /// </summary>
    /// <param name="partNumber">Part number to add</param>
    /// <param name="quantity">Requested quantity</param>
    /// <param name="justification">Justification for the request</param>
    private void AddPartToCurrentRequest(string partNumber, int quantity, string justification)
    {
        try
        {
            // Check if part already exists in request
            var existingItem = _currentRequestItems.FirstOrDefault(item => item.PartNumber == partNumber);
            if (existingItem != null)
            {
                existingItem.QuantityRequested += quantity;
                existingItem.Justification += $"; {justification}";
            }
            else
            {
                var newItem = new PartRequestItem
                {
                    PartNumber = partNumber,
                    Description = GetPartDescription(partNumber),
                    QuantityRequested = quantity,
                    Justification = justification,
                    Status = "Pending"
                };
                _currentRequestItems.Add(newItem);
            }

            RefreshRequestItemsTree();
            UpdateTotalCost();
            StatusLabel.Text = $"Added {partNumber} to request";
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error adding part to request: {ex.Message}");
            StatusLabel.Text = "Error adding part to request";
        }
    }

    /// <summary>
    /// Gets the description for a part number from inventory data.
    /// </summary>
    /// <param name="partNumber">Part number to look up</param>
    /// <returns>Part description or location info if no description available</returns>
    private string GetPartDescription(string partNumber)
    {
        if (_originalInventoryData == null) return "";

        try
        {
            var row = _originalInventoryData.Select($"[Part Number] = '{partNumber}'").FirstOrDefault();
            if (row == null) return "";

            // Since there's no Description column, create a description from available data
            var room = row["Room"]?.ToString() ?? "";
            var bin = row["Bin"]?.ToString() ?? "";
            var altPartNumber = row["Alternate Part Number"]?.ToString() ?? "";

            var description = $"Location: {room}/{bin}";
            if (!string.IsNullOrEmpty(altPartNumber))
            {
                description += $" (Alt: {altPartNumber})";
            }

            return description;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error getting part description: {ex.Message}");
            return "Part information unavailable";
        }
    }

    /// <summary>
    /// Refreshes the request items tree display.
    /// </summary>
    private void RefreshRequestItemsTree()
    {
        if (RequestItemsTree == null) return;

        RequestItemsTree.Clear();
        var root = RequestItemsTree.CreateItem();
        RequestItemsTree.HideRoot = true;

        foreach (var item in _currentRequestItems)
        {
            var treeItem = RequestItemsTree.CreateItem(root);
            treeItem.SetText(0, item.PartNumber);
            treeItem.SetText(1, item.Description);
            treeItem.SetText(2, item.QuantityRequested.ToString());
            treeItem.SetText(3, item.Justification);
        }
    }

    /// <summary>
    /// Validates the current request data before submission.
    /// </summary>
    /// <returns>True if data is valid, false otherwise</returns>
    private bool ValidateRequestData()
    {
        if (RequesterNameField == null || string.IsNullOrEmpty(RequesterNameField.Text.Trim()))
        {
            StatusLabel.Text = "Requester name is required";
            return false;
        }

        if (RequiredDateField == null || string.IsNullOrEmpty(RequiredDateField.Text.Trim()))
        {
            StatusLabel.Text = "Required date is required";
            return false;
        }

        if (!DateTime.TryParse(RequiredDateField.Text, out DateTime requiredDate))
        {
            StatusLabel.Text = "Invalid required date format (use YYYY-MM-DD)";
            return false;
        }

        if (requiredDate <= DateTime.Now)
        {
            StatusLabel.Text = "Required date must be in the future";
            return false;
        }

        return true;
    }

    /// <summary>
    /// Submits the current request to the database.
    /// </summary>
    private async Task SubmitRequestToDatabase()
    {
        // Generate request number
        var requestNumber = await GenerateRequestNumber();

        // Get form data
        var requesterName = RequesterNameField.Text.Trim();
        var department = DepartmentField?.Text.Trim() ?? "";
        var priority = GetSelectedPriority();
        var requiredDate = DateTime.Parse(RequiredDateField.Text);
        var justification = JustificationText?.Text.Trim() ?? "";
        var totalCost = _currentRequestItems.Sum(item => item.TotalCost ?? 0);

        // Insert main request
        var insertRequestQuery = $@"
            INSERT INTO [SysTest].[dbo].[PartRequests]
            (RequestNumber, RequestedBy, Department, Priority, Status, RequestDate, RequiredDate, Notes, TotalEstimatedCost)
            VALUES ('{requestNumber}', '{requesterName}', '{department}', '{priority}', 'Pending', GETDATE(), '{requiredDate:yyyy-MM-dd}', '{justification}', {totalCost});
            SELECT SCOPE_IDENTITY();";

        var requestIdResult = await SqlHandler.QueryAsync(insertRequestQuery);
        var requestId = Convert.ToInt32(requestIdResult.Rows[0][0]);

        // Insert request items
        foreach (var item in _currentRequestItems)
        {
            var insertItemQuery = $@"
                INSERT INTO [SysTest].[dbo].[PartRequestItems]
                (RequestID, PartNumber, Description, QuantityRequested, Justification, Status)
                VALUES ({requestId}, '{item.PartNumber}', '{item.Description}', {item.QuantityRequested}, '{item.Justification}', 'Pending')";

            await SqlHandler.QueryAsync(insertItemQuery);
        }

        GD.Print($"Request {requestNumber} submitted successfully with {_currentRequestItems.Count} items");
    }

    /// <summary>
    /// Generates a unique request number.
    /// </summary>
    /// <returns>Generated request number</returns>
    private async Task<string> GenerateRequestNumber()
    {
        var year = DateTime.Now.Year;
        var countQuery = $"SELECT COUNT(*) FROM [SysTest].[dbo].[PartRequests] WHERE YEAR(RequestDate) = {year}";
        var countResult = await SqlHandler.QueryAsync(countQuery);
        var count = Convert.ToInt32(countResult.Rows[0][0]) + 1;

        return $"REQ-{year}-{count:D3}";
    }

    /// <summary>
    /// Gets the selected priority from the priority selector.
    /// </summary>
    /// <returns>Selected priority as string</returns>
    private string GetSelectedPriority()
    {
        if (PrioritySelector == null) return "Normal";

        return PrioritySelector.Selected switch
        {
            0 => "Low",
            1 => "Normal",
            2 => "High",
            3 => "Urgent",
            _ => "Normal"
        };
    }

    /// <summary>
    /// Updates the status of a request in the database.
    /// </summary>
    /// <param name="requestId">Request ID to update</param>
    /// <param name="newStatus">New status to set</param>
    /// <param name="approvedBy">User who approved/rejected the request</param>
    private async Task UpdateRequestStatus(string requestId, string newStatus, string approvedBy)
    {
        try
        {
            ShowLoadingState(true);
            StatusLabel.Text = $"Updating request status to {newStatus}...";

            var updateQuery = $@"
                UPDATE [SysTest].[dbo].[PartRequests]
                SET Status = '{newStatus}',
                    ApprovedBy = '{approvedBy}',
                    ApprovedDate = GETDATE(),
                    ModifiedDate = GETDATE()
                WHERE RequestID = {requestId}";

            await SqlHandler.QueryAsync(updateQuery);

            // Add to history table
            var historyQuery = $@"
                INSERT INTO [SysTest].[dbo].[PartRequestHistory]
                (RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments)
                VALUES ({requestId}, 'Pending', '{newStatus}', '{approvedBy}', GETDATE(), 'Status changed via management interface')";

            await SqlHandler.QueryAsync(historyQuery);

            StatusLabel.Text = $"Request {newStatus.ToLower()} successfully";
            ShowLoadingState(false);

            // Refresh the requests view
            LoadAllRequests();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error updating request status: {ex.Message}");
            StatusLabel.Text = "Error updating request status";
            ShowLoadingState(false);
        }
    }

    /// <summary>
    /// Populates the user requests tree with data.
    /// </summary>
    /// <param name="requestsData">DataTable containing user's requests</param>
    private void PopulateUserRequestsTree(DataTable requestsData)
    {
        // For now, we'll use the RequestsManagementTree for both user and management views
        // In a full implementation, you might have separate trees
        if (RequestsManagementTree == null) return;

        try
        {
            RequestsManagementTree.Clear();
            RequestsManagementTree.Columns = 6; // Request#, Priority, Status, Date, Required, Cost
            RequestsManagementTree.ColumnTitlesVisible = true;
            RequestsManagementTree.HideRoot = true;

            var columnTitles = new[] { "Request #", "Priority", "Status", "Request Date", "Required Date", "Est. Cost" };
            var columnWidths = new[] { 120, 80, 100, 120, 120, 100 };

            for (int i = 0; i < columnTitles.Length; i++)
            {
                RequestsManagementTree.SetColumnTitle(i, columnTitles[i]);
                RequestsManagementTree.SetColumnCustomMinimumWidth(i, columnWidths[i]);
                RequestsManagementTree.SetColumnExpand(i, true);
            }

            var root = RequestsManagementTree.CreateItem();

            foreach (DataRow row in requestsData.Rows)
            {
                var item = RequestsManagementTree.CreateItem(root);

                item.SetText(0, row["RequestNumber"]?.ToString() ?? "");
                item.SetText(1, row["Priority"]?.ToString() ?? "");
                item.SetText(2, row["Status"]?.ToString() ?? "");

                // Format dates
                if (DateTime.TryParse(row["RequestDate"]?.ToString(), out DateTime requestDate))
                    item.SetText(3, requestDate.ToString("yyyy-MM-dd"));
                else
                    item.SetText(3, "");

                if (DateTime.TryParse(row["RequiredDate"]?.ToString(), out DateTime requiredDate))
                    item.SetText(4, requiredDate.ToString("yyyy-MM-dd"));
                else
                    item.SetText(4, "");

                // Format cost
                if (decimal.TryParse(row["TotalEstimatedCost"]?.ToString(), out decimal cost))
                    item.SetText(5, $"${cost:F2}");
                else
                    item.SetText(5, "$0.00");

                // Color code by status
                SetRequestItemColors(item, row["Status"]?.ToString() ?? "", row["Priority"]?.ToString() ?? "");
            }

            GD.Print($"Populated user requests tree with {requestsData.Rows.Count} requests");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error populating user requests tree: {ex.Message}");
        }
    }

    /// <summary>
    /// Populates the management requests tree with data.
    /// </summary>
    /// <param name="requestsData">DataTable containing all requests for management</param>
    private void PopulateManagementRequestsTree(DataTable requestsData)
    {
        if (RequestsManagementTree == null) return;

        try
        {
            RequestsManagementTree.Clear();
            RequestsManagementTree.Columns = 8; // ID, Request#, Requester, Dept, Priority, Status, Date, Cost
            RequestsManagementTree.ColumnTitlesVisible = true;
            RequestsManagementTree.HideRoot = true;

            var columnTitles = new[] { "ID", "Request #", "Requester", "Department", "Priority", "Status", "Request Date", "Est. Cost" };
            var columnWidths = new[] { 60, 120, 100, 100, 80, 100, 120, 100 };

            for (int i = 0; i < columnTitles.Length; i++)
            {
                RequestsManagementTree.SetColumnTitle(i, columnTitles[i]);
                RequestsManagementTree.SetColumnCustomMinimumWidth(i, columnWidths[i]);
                RequestsManagementTree.SetColumnExpand(i, true);
            }

            var root = RequestsManagementTree.CreateItem();

            foreach (DataRow row in requestsData.Rows)
            {
                var item = RequestsManagementTree.CreateItem(root);

                item.SetText(0, row["RequestID"]?.ToString() ?? "");
                item.SetText(1, row["RequestNumber"]?.ToString() ?? "");
                item.SetText(2, row["RequestedBy"]?.ToString() ?? "");
                item.SetText(3, row["Department"]?.ToString() ?? "");
                item.SetText(4, row["Priority"]?.ToString() ?? "");
                item.SetText(5, row["Status"]?.ToString() ?? "");

                // Format date
                if (DateTime.TryParse(row["RequestDate"]?.ToString(), out DateTime requestDate))
                    item.SetText(6, requestDate.ToString("yyyy-MM-dd"));
                else
                    item.SetText(6, "");

                // Format cost
                if (decimal.TryParse(row["TotalEstimatedCost"]?.ToString(), out decimal cost))
                    item.SetText(7, $"${cost:F2}");
                else
                    item.SetText(7, "$0.00");

                // Color code by status and priority
                SetRequestItemColors(item, row["Status"]?.ToString() ?? "", row["Priority"]?.ToString() ?? "");
            }

            GD.Print($"Populated management requests tree with {requestsData.Rows.Count} requests");
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error populating management requests tree: {ex.Message}");
        }
    }

    /// <summary>
    /// Sets colors for request tree items based on status and priority.
    /// </summary>
    /// <param name="item">Tree item to color</param>
    /// <param name="status">Request status</param>
    /// <param name="priority">Request priority</param>
    private void SetRequestItemColors(TreeItem item, string status, string priority)
    {
        if (RequestsManagementTree == null) return;

        // Status colors
        Color statusColor = status switch
        {
            "Pending" => Colors.Orange,
            "Approved" => Colors.Green,
            "Rejected" => Colors.Red,
            "Fulfilled" => Colors.Blue,
            "Cancelled" => Colors.Gray,
            _ => Colors.White
        };

        // Priority background tint
        Color priorityTint = priority switch
        {
            "Urgent" => new Color(1.0f, 0.8f, 0.8f), // Light red
            "High" => new Color(1.0f, 0.9f, 0.8f),   // Light orange
            "Normal" => Colors.White,
            "Low" => new Color(0.9f, 0.9f, 0.9f),    // Light gray
            _ => Colors.White
        };

        // Apply colors to the status column (column 5 for user view, column 5 for management view)
        var statusColumn = 5;
        if (statusColumn < RequestsManagementTree.Columns)
        {
            item.SetCustomColor(statusColumn, statusColor);
        }

        // Apply priority background tint to the entire row
        for (int i = 0; i < RequestsManagementTree.Columns; i++)
        {
            item.SetCustomBgColor(i, priorityTint);
        }
    }
    #endregion

    #region Public API
    /// <summary>
    /// Public method to add a new user to the approved users list.
    /// </summary>
    /// <param name="username">Username to add</param>
    public void AddApprovedUser(string username)
    {
        if (!string.IsNullOrEmpty(username))
        {
            _approvedUsers.Add(username.ToLower());
            GD.Print($"Added approved user: {username}");
        }
    }

    /// <summary>
    /// Public method to remove a user from the approved users list.
    /// </summary>
    /// <param name="username">Username to remove</param>
    public void RemoveApprovedUser(string username)
    {
        if (!string.IsNullOrEmpty(username))
        {
            _approvedUsers.Remove(username.ToLower());
            GD.Print($"Removed approved user: {username}");
        }
    }

    /// <summary>
    /// Public method to check if a user has edit permissions.
    /// </summary>
    /// <param name="username">Username to check</param>
    /// <returns>True if user has edit permissions</returns>
    public bool CheckUserPermissions(string username)
    {
        return _approvedUsers.Contains(username?.ToLower() ?? "");
    }
    #endregion
}

#region Data Models
/// <summary>
/// Represents a parts request with all its metadata.
/// </summary>
public class PartRequest
{
    public int RequestId { get; set; }
    public string RequestNumber { get; set; } = string.Empty;
    public string RequestedBy { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Priority { get; set; } = "Normal";
    public string Status { get; set; } = "Pending";
    public DateTime RequestDate { get; set; } = DateTime.Now;
    public DateTime? RequiredDate { get; set; }
    public string ApprovedBy { get; set; } = string.Empty;
    public DateTime? ApprovedDate { get; set; }
    public string Notes { get; set; } = string.Empty;
    public decimal TotalEstimatedCost { get; set; }
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime ModifiedDate { get; set; } = DateTime.Now;
}

/// <summary>
/// Represents an individual item within a parts request.
/// </summary>
public class PartRequestItem
{
    public int ItemId { get; set; }
    public int RequestId { get; set; }
    public string PartNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int QuantityRequested { get; set; }
    public int? QuantityApproved { get; set; }
    public decimal? UnitCost { get; set; }
    public decimal? TotalCost => QuantityApproved * UnitCost;
    public string Justification { get; set; } = string.Empty;
    public string Status { get; set; } = "Pending";
    public string Notes { get; set; } = string.Empty;
}

/// <summary>
/// Enumeration for request priorities.
/// </summary>
public enum RequestPriority
{
    Low,
    Normal,
    High,
    Urgent
}

/// <summary>
/// Enumeration for request statuses.
/// </summary>
public enum RequestStatus
{
    Pending,
    Approved,
    Rejected,
    Fulfilled,
    Cancelled
}
#endregion
