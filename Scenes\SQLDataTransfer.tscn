[gd_scene load_steps=4 format=3 uid="uid://0gd6hcua6tfk"]

[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_p37ke"]
[ext_resource type="Script" uid="uid://vctnsh8jb30i" path="res://Scripts/SqlDataTransfer.cs" id="2_ubfxo"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8qixn"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[node name="SQLDataTransfer" type="PanelContainer" node_paths=PackedStringArray("TransferButton", "TableListContainer")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_p37ke")
theme_override_styles/panel = SubResource("StyleBoxFlat_8qixn")
script = ExtResource("2_ubfxo")
TransferButton = NodePath("MarginContainer/VBoxContainer2/TransferButton")
TableListContainer = NodePath("MarginContainer/VBoxContainer2/ScrollContainer/ListContainer")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="VBoxContainer2" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer2"]
layout_mode = 2
theme_override_font_sizes/font_size = 25
text = "Data Tables"

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer/VBoxContainer2"]
layout_mode = 2
size_flags_vertical = 3

[node name="ListContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer2/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="TransferButton" type="Button" parent="MarginContainer/VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
text = "Transfer"
