using Godot;
using System;
using System.Xml.Serialization;

public partial class CustomSignals : Node
{
    [Signal] public delegate void LoginSuccessfulEventHandler();
    [Signal] public delegate void LoginFailedEventHandler();
    [Signal] public delegate void AppSelectedEventHandler(string sceneName);
    [Signal] public delegate void ProcessCompleteEventHandler(string message);
    [Signal] public delegate void AttributeSetEventHandler(string attribute);
    [Signal] public delegate void WireSegmentChangedEventHandler(PanelContainer WireSegment, float Area);
}
