// ContactAssemblyRow.cs (Unified Version)
using Godot;
using System;
using static ContactDetailsPanel; // Use the helper classes from the main panel

public partial class ContactAssemblyRow : PanelContainer
{
    [Export] public Label CrimperLabel { get; set; }
    [Export] public VBoxContainer AccessoryContainer { get; set; }

    // This method now takes the entire data object
    public void Populate(CrimperUIData data)
    {
        // Set the main crimper label, which is always present
        CrimperLabel.Text = $"{data.Type}: {data.PartNumber}";

        // Clear any previous accessories from the container
        foreach (var child in AccessoryContainer.GetChildren())
        {
            child.QueueFree();
        }

        // A single loop now handles BOTH cases elegantly.
        // If there's one accessory, it runs once. If there are many, it runs many times.
        foreach (AccessoryInfo accessory in data.Accessories)
        {
            var accessoryLabel = new Label();
            accessoryLabel.Text = $"{accessory.Type}: {accessory.PartNumber} ";
            AccessoryContainer.AddChild(accessoryLabel);
        }
    }
}