[gd_scene load_steps=7 format=3 uid="uid://ck3x4jda56grk"]

[ext_resource type="Script" uid="uid://ta2aghfi0iko" path="res://Scripts/Main.cs" id="1_rgh2o"]
[ext_resource type="PackedScene" uid="uid://bai711sss5rqe" path="res://Scenes/SettingsBar.tscn" id="2_pbw6q"]
[ext_resource type="PackedScene" uid="uid://ds4qxmhlwh4ke" path="res://Scenes/NavigationBar.tscn" id="3_kln2b"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rgh2o"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_50glp"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kln2b"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[node name="Main" type="PanelContainer" node_paths=PackedStringArray("Navigation", "MainPanel", "AppBody")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_rgh2o")
script = ExtResource("1_rgh2o")
Navigation = NodePath("MainPanel/VBoxContainer/NavigationBar")
MainPanel = NodePath("MainPanel")
AppBody = NodePath("MainPanel/VBoxContainer/AppBody")

[node name="MainPanel" type="PanelContainer" parent="."]
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_50glp")

[node name="VBoxContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 2

[node name="SettingsBar" parent="MainPanel/VBoxContainer" instance=ExtResource("2_pbw6q")]
layout_mode = 2

[node name="NavigationBar" parent="MainPanel/VBoxContainer" instance=ExtResource("3_kln2b")]
layout_mode = 2

[node name="AppBody" type="PanelContainer" parent="MainPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_kln2b")
