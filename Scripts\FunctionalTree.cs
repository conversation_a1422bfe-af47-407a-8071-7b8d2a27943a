using Godot;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

/// <summary>
/// Enhanced Tree functionality that adds sorting and filtering capabilities.
/// Attach this script to any Tree node to enable advanced features.
/// 
/// Features:
/// - Ctrl+Click column headers to sort
/// - Ctrl+Click (no column) to show filter dialog
/// - Column-specific filtering with distinct value selection
/// - Maintains original data for filtering operations
/// </summary>
public partial class FunctionalTree : Tree
{
    #region Properties and Fields
    /// <summary>Original data table for filtering operations</summary>
    private DataTable _originalData;
    
    /// <summary>Currently filtered data table</summary>
    private DataTable _filteredData;
    
    /// <summary>Current sort column index (-1 if not sorted)</summary>
    private int _currentSortColumn = -1;
    
    /// <summary>Current sort direction (true = ascending, false = descending)</summary>
    private bool _sortAscending = true;
    
    /// <summary>Dictionary storing active column filters</summary>
    private Dictionary<int, HashSet<string>> _columnFilters = new();

    /// <summary>Column names for display purposes</summary>
    private string[] _columnNames = Array.Empty<string>();

    /// <summary>Column widths for resizing</summary>
    private List<int> _columnWidths = new();

    /// <summary>Column order for reordering</summary>
    private List<int> _columnOrder = new();

    /// <summary>Current drag state</summary>
    private enum DragState { None, ResizingColumn, ReorderingColumn }
    private DragState _currentDragState = DragState.None;

    /// <summary>Column being resized or reordered</summary>
    private int _dragColumnIndex = -1;

    /// <summary>Original mouse position when drag started</summary>
    private Vector2 _dragStartPosition;

    /// <summary>Original column width when resize started</summary>
    private int _originalColumnWidth;

    /// <summary>Resize handle detection threshold (pixels)</summary>
    private const int RESIZE_HANDLE_THRESHOLD = 5;

    /// <summary>Timer for checking scroll changes</summary>
    private Timer _scrollCheckTimer;

    /// <summary>Last known scroll position for change detection</summary>
    private Vector2 _lastScrollPosition = Vector2.Zero;

    // Removed ColorRect-based separator system - using _Draw() instead
    
    /// <summary>Event fired when data is filtered or sorted</summary>
    [Signal] public delegate void DataChangedEventHandler();

    /// <summary>Event fired when filter status changes</summary>
    [Signal] public delegate void FilterStatusChangedEventHandler(string statusText);

    /// <summary>Optional theme to apply to filter dialogs</summary>
    [Export] public Theme DialogTheme { get; set; }

    /// <summary>Color for column separator lines</summary>
    [Export] public Color ColumnSeparatorColor { get; set; } = new Color(0.5f, 0.5f, 0.5f, 1.0f); // Solid gray

    /// <summary>Color for resize handle hover highlight</summary>
    [Export] public Color ResizeHandleHoverColor { get; set; } = new Color(0.3f, 0.6f, 1.0f, 0.3f);

    /// <summary>Width of column separator lines</summary>
    [Export] public float ColumnSeparatorWidth { get; set; } = 2.0f; // Make thicker

    /// <summary>Current mouse position for drawing</summary>
    private Vector2 _currentMousePosition;

    /// <summary>Whether mouse is currently hovering over a resize handle</summary>
    private bool _isHoveringResizeHandle = false;

    /// <summary>Column index of the resize handle being hovered</summary>
    private int _hoveredResizeColumn = -1;

    /// <summary>Estimated scroll offset based on column positions</summary>
    private float _estimatedScrollOffset = 0.0f;
    #endregion

    #region Initialization
    public override void _Ready()
    {
        // Ensure the tree can receive input events
        SetProcessInput(true);
        SetProcessUnhandledInput(true);

        // Connect input events
        GuiInput += OnGuiInput;

        // Connect to tree events that might affect drawing
        ItemSelected += OnItemChanged;

        // Enable processing for drawing updates
        SetProcess(true);

        GD.Print("FunctionalTree initialized and ready");
    }

    /// <summary>
    /// Handles item changes that might affect drawing.
    /// </summary>
    private void OnItemChanged()
    {
        QueueRedraw();
    }

    // Process and notification methods removed to focus on core functionality
    // TODO: Re-implement scroll-aware drawing when needed

    /// <summary>
    /// Custom drawing for column separators and resize handle highlights.
    /// </summary>
    public override void _Draw()
    {
        if (!ColumnTitlesVisible || Columns == 0) return;

        DrawColumnSeparators();
        DrawResizeHandleHighlight();
    }

    /// <summary>
    /// Override notification to draw after the tree content.
    /// </summary>
    /// <param name="what">Notification type</param>
    public override void _Notification(int what)
    {
        base._Notification(what);

        if (what == NotificationDraw)
        {
            // Try drawing here as well, after the tree has drawn its content
            DrawColumnSeparatorsPost();
        }
    }

    /// <summary>
    /// Draws column separators after tree content is rendered, accounting for scroll.
    /// </summary>
    private void DrawColumnSeparatorsPost()
    {
        if (!ColumnTitlesVisible || Columns == 0) return;

        var treeHeight = Size.Y;

        // Get scroll offset - try multiple methods
        var scrollOffset = GetScrollOffset();

        float currentX = -scrollOffset; // Start with negative scroll offset

        GD.Print($"Post-draw: Scroll offset = {scrollOffset}");

        // Draw separator lines accounting for scroll
        for (int i = 0; i < Columns; i++)
        {
            var columnWidth = GetColumnWidth(i);
            currentX += columnWidth;

            if (i < Columns - 1)
            {
                // Only draw if the line would be visible on screen
                if (currentX >= -10 && currentX <= Size.X + 10)
                {
                    var startPoint = new Vector2(currentX, 0);
                    var endPoint = new Vector2(currentX, treeHeight);

                    // Use the customizable separator color and width
                    DrawLine(startPoint, endPoint, ColumnSeparatorColor, ColumnSeparatorWidth);

                    GD.Print($"Post-draw: Drew separator line at X={currentX} (scroll offset: {scrollOffset})");
                }
            }
        }
    }

    /// <summary>
    /// Gets the current horizontal scroll offset of the tree.
    /// For now, returns 0 since Tree's internal scrolling is complex to access.
    /// </summary>
    /// <returns>Horizontal scroll offset in pixels</returns>
    private float GetScrollOffset()
    {
        // Tree control's internal scrolling is not easily accessible
        // For now, we'll draw the lines at their calculated positions
        // and let the Tree's internal clipping handle visibility
        return 0.0f;
    }

    /// <summary>
    /// Draws vertical lines between columns that extend through the entire tree.
    /// </summary>
    private void DrawColumnSeparators()
    {
        if (!ColumnTitlesVisible || Columns == 0)
        {
            GD.Print($"DrawColumnSeparators: Skipped - ColumnTitlesVisible={ColumnTitlesVisible}, Columns={Columns}");
            return;
        }

        var treeHeight = Size.Y;
        float currentX = 0;

        GD.Print($"DrawColumnSeparators: Drawing separators for {Columns} columns, tree height={treeHeight}");

        // Draw separator lines between columns - extend through entire tree height
        for (int i = 0; i < Columns; i++)
        {
            var columnWidth = GetColumnWidth(i);
            currentX += columnWidth;

            GD.Print($"Column {i}: width={columnWidth}, currentX={currentX}");

            // Don't draw separator after the last column
            if (i < Columns - 1)
            {
                // Draw multiple lines to ensure visibility through tree content
                for (int lineOffset = -1; lineOffset <= 1; lineOffset++)
                {
                    var lineX = currentX + lineOffset;
                    var startPoint = new Vector2(lineX, 0);
                    var endPoint = new Vector2(lineX, treeHeight);

                    // Use a more visible color and thicker line
                    var lineColor = new Color(0.3f, 0.3f, 0.3f, 0.8f); // Dark gray, semi-transparent
                    var lineWidth = 1.0f;

                    DrawLine(startPoint, endPoint, lineColor, lineWidth);
                }

                GD.Print($"Drew separator lines at X={currentX}");

                // Draw test rectangles at multiple heights to see if they're visible
                for (int y = 50; y < treeHeight; y += 100)
                {
                    var testRect = new Rect2(currentX - 2, y, 4, 20);
                    DrawRect(testRect, Colors.Red);
                }
            }
        }

        GD.Print("DrawColumnSeparators: Finished drawing");
    }

    /// <summary>
    /// Draws highlight for resize handle when hovering.
    /// </summary>
    private void DrawResizeHandleHighlight()
    {
        if (!_isHoveringResizeHandle || _hoveredResizeColumn < 0) return;

        // Get the actual header height from the tree
        var headerHeight = ColumnTitlesVisible ? GetThemeConstant("item_margin") + GetThemeFont("title_font").GetHeight() + 8 : 0;
        if (headerHeight <= 0) headerHeight = 28f; // Fallback height

        float currentX = 0;

        // Find the position of the hovered column's right edge
        for (int i = 0; i <= _hoveredResizeColumn && i < Columns; i++)
        {
            var columnWidth = GetColumnWidth(i);
            if (i == _hoveredResizeColumn)
            {
                // Draw highlight rectangle around the resize handle area
                var handleRect = new Rect2(
                    currentX + columnWidth - RESIZE_HANDLE_THRESHOLD,
                    0,
                    RESIZE_HANDLE_THRESHOLD * 2,
                    headerHeight
                );

                DrawRect(handleRect, ResizeHandleHoverColor);
                break;
            }
            currentX += columnWidth;
        }
    }

    /// <summary>
    /// Override to handle input events directly as a fallback.
    /// </summary>
    /// <param name="event">Input event</param>
    public override void _Input(InputEvent @event)
    {
        // Trigger redraw on scroll wheel events
        if (@event is InputEventMouseButton mouseEvent &&
            (mouseEvent.ButtonIndex == MouseButton.WheelUp ||
             mouseEvent.ButtonIndex == MouseButton.WheelDown ||
             mouseEvent.ButtonIndex == MouseButton.WheelLeft ||
             mouseEvent.ButtonIndex == MouseButton.WheelRight))
        {
            // Schedule redraw for next frame to handle scroll
            CallDeferred(nameof(QueueRedraw));
        }

        if (@event is InputEventMouseButton mouseButtonEvent &&
            mouseButtonEvent.Pressed &&
            mouseButtonEvent.ButtonIndex == MouseButton.Left)
        {
            // Check if the click is within our bounds
            var globalRect = GetGlobalRect();
            if (globalRect.HasPoint(mouseButtonEvent.GlobalPosition))
            {
                var localPos = mouseButtonEvent.Position;
                var columnIndex = GetColumnAtPosition(localPos);

                if (mouseButtonEvent.CtrlPressed && columnIndex >= 0)
                {
                    // Ctrl+Click on column header - sort
                    HandleColumnSort(columnIndex);
                    GetViewport().SetInputAsHandled();
                }
                else if (!mouseButtonEvent.CtrlPressed && columnIndex >= 0)
                {
                    // Regular click on column header - filter that column
                    ShowColumnFilterDialog(columnIndex);
                    GetViewport().SetInputAsHandled();
                }
                // Let other clicks pass through to normal tree handling
            }
        }
    }
    #endregion

    #region Public API
    /// <summary>
    /// Sets the data for the tree and enables advanced functionality.
    /// </summary>
    /// <param name="data">DataTable containing the data to display</param>
    /// <param name="columnNames">Optional array of column display names</param>
    public void SetData(DataTable data, string[] columnNames = null)
    {
        if (data == null)
        {
            GD.PrintErr("FunctionalTree: Cannot set null data");
            return;
        }

        _originalData = data.Copy();
        _filteredData = data.Copy();
        _columnNames = columnNames ?? data.Columns.Cast<DataColumn>().Select(c => c.ColumnName).ToArray();
        
        // Clear any existing filters and reset column settings
        _columnFilters.Clear();
        _currentSortColumn = -1;
        _sortAscending = true;

        // Initialize column widths and order
        InitializeColumnSettings();

        RefreshDisplay();
        EmitSignal(SignalName.FilterStatusChanged, GetFilterStatusText());

        GD.Print($"FunctionalTree: Data set with {data.Rows.Count} rows and {data.Columns.Count} columns");
    }

    /// <summary>
    /// Gets the currently displayed (filtered/sorted) data.
    /// </summary>
    /// <returns>Current filtered data table</returns>
    public DataTable GetCurrentData()
    {
        return _filteredData?.Copy();
    }

    /// <summary>
    /// Clears all filters and sorting.
    /// </summary>
    public void ClearAllFilters()
    {
        _columnFilters.Clear();
        _currentSortColumn = -1;
        _sortAscending = true;
        
        if (_originalData != null)
        {
            _filteredData = _originalData.Copy();
            RefreshDisplay();
            EmitSignal(SignalName.FilterStatusChanged, GetFilterStatusText());
        }
    }

    /// <summary>
    /// Gets the current filter status text.
    /// </summary>
    /// <returns>Status text describing active filters</returns>
    public string GetFilterStatusText()
    {
        if (_originalData == null || _filteredData == null) return "";

        var activeFilters = _columnFilters.Count(f => f.Value.Count > 0);
        var sortText = _currentSortColumn >= 0 ? $", sorted by {_columnNames[_currentSortColumn]}" : "";
        var filterText = activeFilters > 0 ? $" ({activeFilters} filters active{sortText})" : sortText;

        return $"Showing {_filteredData.Rows.Count} of {_originalData.Rows.Count} items{filterText}";
    }

    /// <summary>
    /// Sets the theme for filter dialogs programmatically.
    /// </summary>
    /// <param name="theme">Theme to apply to dialogs</param>
    public void SetDialogTheme(Theme theme)
    {
        DialogTheme = theme;
        GD.Print($"FunctionalTree: Dialog theme set to {theme?.ResourceName ?? "default"}");
    }

    /// <summary>
    /// Initializes column width and order settings.
    /// </summary>
    private void InitializeColumnSettings()
    {
        if (_originalData == null) return;

        _columnWidths.Clear();
        _columnOrder.Clear();

        // Set default column widths
        var defaultWidths = new[] { 150, 80, 100, 100, 150 }; // Part Number, Quantity, Room, Bin, Alt Part

        for (int i = 0; i < _originalData.Columns.Count; i++)
        {
            var width = i < defaultWidths.Length ? defaultWidths[i] : 100;
            _columnWidths.Add(width);
            _columnOrder.Add(i); // Default order
        }
    }

    /// <summary>
    /// Auto-sizes a column to fit its content.
    /// </summary>
    /// <param name="columnIndex">Column to auto-size</param>
    private void AutoSizeColumn(int columnIndex)
    {
        if (_filteredData == null || columnIndex >= _filteredData.Columns.Count) return;

        // Calculate required width based on content
        int maxWidth = 50; // Minimum width

        // Check header width
        var headerText = _columnNames[columnIndex];
        maxWidth = Math.Max(maxWidth, headerText.Length * 8 + 20); // Approximate character width

        // Check content width (sample first 100 rows for performance)
        var rowsToCheck = Math.Min(100, _filteredData.Rows.Count);
        for (int i = 0; i < rowsToCheck; i++)
        {
            var cellText = _filteredData.Rows[i][columnIndex]?.ToString() ?? "";
            maxWidth = Math.Max(maxWidth, cellText.Length * 8 + 20);
        }

        // Cap maximum width
        maxWidth = Math.Min(maxWidth, 300);

        _columnWidths[columnIndex] = maxWidth;
        ApplyColumnSettings();

        GD.Print($"Auto-sized column {columnIndex} ({_columnNames[columnIndex]}) to {maxWidth}px");
    }

    /// <summary>
    /// Applies current column width and order settings to the tree.
    /// </summary>
    private void ApplyColumnSettings()
    {
        if (Columns == 0) return;

        for (int i = 0; i < Math.Min(_columnWidths.Count, Columns); i++)
        {
            SetColumnCustomMinimumWidth(i, _columnWidths[i]);
        }
    }

    /// <summary>
    /// Process function to trigger redraws for scroll synchronization.
    /// </summary>
    /// <param name="delta">Time delta</param>
    public override void _Process(double delta)
    {
        // Trigger redraw to keep separators synchronized with scrolling
        QueueRedraw();
    }
    #endregion

    #region Input Handling
    /// <summary>
    /// Handles GUI input events for column operations.
    /// </summary>
    /// <param name="event">Input event</param>
    private void OnGuiInput(InputEvent @event)
    {
        if (@event is InputEventMouseButton mouseEvent)
        {
            HandleMouseButtonEvent(mouseEvent);
        }
        else if (@event is InputEventMouseMotion motionEvent)
        {
            HandleMouseMotionEvent(motionEvent);
        }
    }

    /// <summary>
    /// Handles mouse button events for clicking, dragging, and double-clicking.
    /// </summary>
    /// <param name="mouseEvent">Mouse button event</param>
    private void HandleMouseButtonEvent(InputEventMouseButton mouseEvent)
    {
        var clickPosition = mouseEvent.Position;
        var columnIndex = GetColumnAtPosition(clickPosition);
        var resizeEdge = GetResizeEdgeAtPosition(clickPosition);

        if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
        {
            // Double-click on column edge - auto-size column
            if (mouseEvent.DoubleClick && resizeEdge >= 0)
            {
                AutoSizeColumn(resizeEdge);
                GetViewport().SetInputAsHandled();
                return;
            }

            // Start resize drag on column edge
            if (resizeEdge >= 0)
            {
                StartColumnResize(resizeEdge, clickPosition);
                GetViewport().SetInputAsHandled();
                return;
            }

            // Handle column header clicks
            if (columnIndex >= 0)
            {
                if (mouseEvent.CtrlPressed)
                {
                    // Ctrl+Click on column header - sort
                    GD.Print($"Sorting by column {columnIndex}");
                    HandleColumnSort(columnIndex);
                    GetViewport().SetInputAsHandled();
                }
                else
                {
                    // Regular click on column header - show filter for that column only
                    GD.Print($"Opening filter dialog for column {columnIndex}");
                    ShowColumnFilterDialog(columnIndex);
                    GetViewport().SetInputAsHandled();
                }
            }
        }
        else if (!mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
        {
            // End any drag operation
            EndDragOperation();
        }
    }

    /// <summary>
    /// Handles mouse motion events for dragging and cursor changes.
    /// </summary>
    /// <param name="motionEvent">Mouse motion event</param>
    private void HandleMouseMotionEvent(InputEventMouseMotion motionEvent)
    {
        var position = motionEvent.Position;
        _currentMousePosition = position;

        if (_currentDragState == DragState.ResizingColumn)
        {
            UpdateColumnResize(position);
        }
        else
        {
            // Update cursor and hover state based on position
            UpdateCursorAndHoverState(position);
        }
    }

    /// <summary>
    /// Gets the column edge that can be resized at the given position.
    /// </summary>
    /// <param name="position">Mouse position</param>
    /// <returns>Column index whose right edge can be resized, or -1 if none</returns>
    private int GetResizeEdgeAtPosition(Vector2 position)
    {
        if (!ColumnTitlesVisible) return -1;

        var headerHeight = 24; // Approximate header height
        if (position.Y > headerHeight) return -1;

        float currentX = 0;
        for (int i = 0; i < Columns; i++)
        {
            var columnWidth = GetColumnWidth(i);
            var rightEdge = currentX + columnWidth;

            // Check if mouse is near the right edge of this column
            if (Math.Abs(position.X - rightEdge) <= RESIZE_HANDLE_THRESHOLD)
            {
                return i; // Return the column whose right edge can be resized
            }

            currentX += columnWidth;
        }

        return -1;
    }

    /// <summary>
    /// Starts a column resize operation.
    /// </summary>
    /// <param name="columnIndex">Column to resize</param>
    /// <param name="startPosition">Starting mouse position</param>
    private void StartColumnResize(int columnIndex, Vector2 startPosition)
    {
        _currentDragState = DragState.ResizingColumn;
        _dragColumnIndex = columnIndex;
        _dragStartPosition = startPosition;
        _originalColumnWidth = _columnWidths[columnIndex];

        MouseDefaultCursorShape = Control.CursorShape.Hsize;
        GD.Print($"Started resizing column {columnIndex} from width {_originalColumnWidth}");
    }

    /// <summary>
    /// Updates column width during resize drag.
    /// </summary>
    /// <param name="currentPosition">Current mouse position</param>
    private void UpdateColumnResize(Vector2 currentPosition)
    {
        if (_currentDragState != DragState.ResizingColumn || _dragColumnIndex < 0) return;

        var deltaX = currentPosition.X - _dragStartPosition.X;
        var newWidth = Math.Max(50, _originalColumnWidth + (int)deltaX); // Minimum width of 50

        _columnWidths[_dragColumnIndex] = newWidth;
        SetColumnCustomMinimumWidth(_dragColumnIndex, newWidth);
    }

    /// <summary>
    /// Ends any active drag operation.
    /// </summary>
    private void EndDragOperation()
    {
        if (_currentDragState != DragState.None)
        {
            GD.Print($"Ended drag operation: {_currentDragState}");

            if (_currentDragState == DragState.ResizingColumn && _dragColumnIndex >= 0)
            {
                GD.Print($"Column {_dragColumnIndex} resized to {_columnWidths[_dragColumnIndex]}px");
            }
        }

        _currentDragState = DragState.None;
        _dragColumnIndex = -1;
        _isHoveringResizeHandle = false;
        _hoveredResizeColumn = -1;
        MouseDefaultCursorShape = Control.CursorShape.Arrow;

        // Trigger redraw to clear any highlights
        QueueRedraw();
    }

    /// <summary>
    /// Updates cursor shape and hover state based on mouse position.
    /// </summary>
    /// <param name="position">Current mouse position</param>
    private void UpdateCursorAndHoverState(Vector2 position)
    {
        var resizeEdge = GetResizeEdgeAtPosition(position);
        var wasHovering = _isHoveringResizeHandle;
        var previousColumn = _hoveredResizeColumn;

        _isHoveringResizeHandle = resizeEdge >= 0;
        _hoveredResizeColumn = resizeEdge;

        // Update cursor
        if (_isHoveringResizeHandle)
        {
            MouseDefaultCursorShape = Control.CursorShape.Hsize;
        }
        else
        {
            MouseDefaultCursorShape = Control.CursorShape.Arrow;
        }

        // Trigger redraw if hover state changed
        if (wasHovering != _isHoveringResizeHandle || previousColumn != _hoveredResizeColumn)
        {
            QueueRedraw();
        }
    }

    /// <summary>
    /// Gets the column index at the given position.
    /// </summary>
    /// <param name="position">Mouse position</param>
    /// <returns>Column index or -1 if not over a column header</returns>
    private new int GetColumnAtPosition(Vector2 position)
    {
        if (!ColumnTitlesVisible) return -1;
        
        // Check if click is in header area (approximate)
        var headerHeight = 24; // Approximate header height
        if (position.Y > headerHeight) return -1;
        
        // Calculate which column was clicked
        float currentX = 0;
        for (int i = 0; i < Columns; i++)
        {
            var columnWidth = GetColumnWidth(i);
            if (position.X >= currentX && position.X < currentX + columnWidth)
            {
                return i;
            }
            currentX += columnWidth;
        }
        
        return -1;
    }
    #endregion

    #region Sorting
    /// <summary>
    /// Handles column sorting when Ctrl+Click on column header.
    /// </summary>
    /// <param name="columnIndex">Column to sort by</param>
    private void HandleColumnSort(int columnIndex)
    {
        if (_filteredData == null || columnIndex >= _filteredData.Columns.Count) return;
        
        // Toggle sort direction if same column, otherwise ascending
        if (_currentSortColumn == columnIndex)
        {
            _sortAscending = !_sortAscending;
        }
        else
        {
            _currentSortColumn = columnIndex;
            _sortAscending = true;
        }
        
        SortData();
        RefreshDisplay();
        EmitSignal(SignalName.FilterStatusChanged, GetFilterStatusText());
        
        var direction = _sortAscending ? "ascending" : "descending";
        GD.Print($"FunctionalTree: Sorted by column {_columnNames[columnIndex]} ({direction})");
    }

    /// <summary>
    /// Sorts the filtered data by the current sort column.
    /// </summary>
    private void SortData()
    {
        if (_filteredData == null || _currentSortColumn < 0) return;
        
        var sortedRows = _filteredData.Rows.Cast<DataRow>().ToArray();
        var columnName = _filteredData.Columns[_currentSortColumn].ColumnName;
        
        // Determine if column contains numeric data
        var isNumeric = _filteredData.Rows.Cast<DataRow>()
            .Take(10) // Sample first 10 rows
            .All(row => int.TryParse(row[_currentSortColumn]?.ToString(), out _));
        
        if (isNumeric)
        {
            // Numeric sorting
            sortedRows = _sortAscending
                ? sortedRows.OrderBy(row => {
                    var value = row[_currentSortColumn]?.ToString();
                    return int.TryParse(value, out int numValue) ? numValue : 0;
                }).ToArray()
                : sortedRows.OrderByDescending(row => {
                    var value = row[_currentSortColumn]?.ToString();
                    return int.TryParse(value, out int numValue) ? numValue : 0;
                }).ToArray();
        }
        else
        {
            // String sorting
            sortedRows = _sortAscending
                ? sortedRows.OrderBy(row => row[_currentSortColumn]?.ToString() ?? "").ToArray()
                : sortedRows.OrderByDescending(row => row[_currentSortColumn]?.ToString() ?? "").ToArray();
        }
        
        // Create new sorted DataTable
        var sortedTable = _filteredData.Clone();
        foreach (var row in sortedRows)
        {
            sortedTable.ImportRow(row);
        }
        
        _filteredData = sortedTable;
    }
    #endregion

    #region Filtering
    /// <summary>
    /// Shows the filter dialog for column selection.
    /// </summary>
    private void ShowFilterDialog()
    {
        if (_originalData == null) return;

        // Clear previous dialog data
        _filterDialogData.Clear();
        
        var dialog = new AcceptDialog
        {
            Title = "Column Filters",
            Size = new Vector2I(500, 400),
            OkButtonText = "Cancel"
        };

        // Apply theme if available
        if (DialogTheme != null)
        {
            dialog.Theme = DialogTheme;
        }
        
        var scrollContainer = new ScrollContainer
        {
            SizeFlagsHorizontal = Control.SizeFlags.ExpandFill,
            SizeFlagsVertical = Control.SizeFlags.ExpandFill
        };
        
        var vbox = new VBoxContainer();
        scrollContainer.AddChild(vbox);
        dialog.AddChild(scrollContainer);
        
        // Add title
        var titleLabel = new Label
        {
            Text = "Select items to show for each column:",
            HorizontalAlignment = HorizontalAlignment.Center
        };
        vbox.AddChild(titleLabel);
        
        var separator = new HSeparator();
        vbox.AddChild(separator);
        
        // Create filter sections for each column
        for (int columnIndex = 0; columnIndex < _originalData.Columns.Count; columnIndex++)
        {
            CreateColumnFilterSection(vbox, columnIndex);
        }
        
        // Add action buttons
        var buttonHBox = new HBoxContainer();
        vbox.AddChild(buttonHBox);
        
        var clearAllButton = new Button { Text = "Clear All Filters" };
        clearAllButton.Pressed += () => {
            ClearAllFilters();
            dialog.QueueFree();
        };
        buttonHBox.AddChild(clearAllButton);
        
        var applyButton = new Button { Text = "Apply Filters" };
        applyButton.Pressed += () => {
            ApplyFilters();
            dialog.QueueFree();
        };
        buttonHBox.AddChild(applyButton);
        
        // Add proper cleanup handlers
        dialog.CloseRequested += () => {
            _filterDialogData.Clear();
            dialog.QueueFree();
        };

        dialog.Confirmed += () => {
            ApplyFilters();
        };

        // Show dialog
        GetTree().Root.AddChild(dialog);
        dialog.PopupCentered();
    }

    /// <summary>
    /// Shows a filter dialog for a specific column only (Excel-like behavior).
    /// </summary>
    /// <param name="columnIndex">Index of the column to filter</param>
    private void ShowColumnFilterDialog(int columnIndex)
    {
        if (_originalData == null || columnIndex >= _originalData.Columns.Count) return;

        var columnName = _columnNames[columnIndex];

        var dialog = new AcceptDialog
        {
            Title = $"Filter: {columnName}",
            Size = new Vector2I(300, 400),
            OkButtonText = "Cancel"
        };

        // Apply theme if available
        if (DialogTheme != null)
        {
            dialog.Theme = DialogTheme;
        }

        var vbox = new VBoxContainer();
        dialog.AddChild(vbox);

        // Add title
        var titleLabel = new Label
        {
            Text = $"Filter By {columnName}:",
            HorizontalAlignment = HorizontalAlignment.Center
        };
        vbox.AddChild(titleLabel);

        var separator = new HSeparator();
        vbox.AddChild(separator);

        // Get distinct values for this column
        var distinctValues = _originalData.Rows.Cast<DataRow>()
            .Select(row => row[columnIndex]?.ToString() ?? "")
            .Distinct()
            .OrderBy(v => v)
            .ToList();

        // Create scrollable container for checkboxes
        var scrollContainer = new ScrollContainer
        {
            CustomMinimumSize = new Vector2(0, Math.Min(250, distinctValues.Count * 25 + 10)),
            SizeFlagsHorizontal = Control.SizeFlags.ExpandFill,
            SizeFlagsVertical = Control.SizeFlags.ExpandFill
        };
        vbox.AddChild(scrollContainer);

        var checkboxVBox = new VBoxContainer();
        scrollContainer.AddChild(checkboxVBox);

        // Get current filter for this column
        var currentFilter = _columnFilters.GetValueOrDefault(columnIndex, new HashSet<string>());
        var showAll = currentFilter.Count == 0;

        // Add "Select All" checkbox
        var selectAllCheckbox = new CheckBox
        {
            Text = "Select All",
            ButtonPressed = showAll,
            SizeFlagsHorizontal = SizeFlags.ExpandFill,
            Flat = true
        };

        var valueCheckboxes = new List<CheckBox>();

        selectAllCheckbox.Toggled += (pressed) => {
            foreach (var checkbox in valueCheckboxes)
            {
                checkbox.ButtonPressed = pressed;
            }
        };
        checkboxVBox.AddChild(selectAllCheckbox);

        // Add separator
        var checkboxSeparator = new HSeparator();
        checkboxVBox.AddChild(checkboxSeparator);

        // Add checkbox for each distinct value
        foreach (var value in distinctValues)
        {
            var checkbox = new CheckBox
            {
                Text = string.IsNullOrEmpty(value) ? "(Empty)" : value,
                ButtonPressed = showAll || currentFilter.Contains(value),
                SizeFlagsHorizontal = SizeFlags.ExpandFill,
                Flat = true
            };

            checkbox.Toggled += (pressed) => {
                // Update select all checkbox
                selectAllCheckbox.ButtonPressed = valueCheckboxes.All(cb => cb.ButtonPressed);
            };

            valueCheckboxes.Add(checkbox);
            checkboxVBox.AddChild(checkbox);
        }

        // Add action buttons
        var buttonHBox = new HBoxContainer
        {
            SizeFlagsHorizontal = SizeFlags.ShrinkCenter
        };
        vbox.AddChild(buttonHBox);

        var clearButton = new Button { Text = "Clear Filter" };
        clearButton.Pressed += () => {
            // Remove filter for this column
            _columnFilters.Remove(columnIndex);
            ApplyFiltersAndRefresh();
            dialog.QueueFree();
        };
        buttonHBox.AddChild(clearButton);

        var applyButton = new Button { Text = "Apply" };
        applyButton.Pressed += () => {
            // Collect selected values
            var selectedValues = new HashSet<string>();

            for (int i = 0; i < valueCheckboxes.Count && i < distinctValues.Count; i++)
            {
                if (valueCheckboxes[i].ButtonPressed)
                {
                    selectedValues.Add(distinctValues[i]);
                }
            }

            // Update filter for this column
            if (selectedValues.Count < distinctValues.Count)
            {
                _columnFilters[columnIndex] = selectedValues;
            }
            else
            {
                // All values selected = no filter
                _columnFilters.Remove(columnIndex);
            }

            ApplyFiltersAndRefresh();
            dialog.QueueFree();
        };
        buttonHBox.AddChild(applyButton);

        // Add proper cleanup handlers
        dialog.CloseRequested += () => {
            dialog.QueueFree();
        };

        dialog.Confirmed += () => {
            // Apply button functionality (already handled above)
        };

        // Show dialog
        GetTree().Root.AddChild(dialog);
        dialog.PopupCentered();
    }

    /// <summary>
    /// Applies current filters and refreshes the display.
    /// </summary>
    private void ApplyFiltersAndRefresh()
    {
        FilterData();
        RefreshDisplay();
        EmitSignal("FilterStatusChanged", GetFilterStatusText());

        var activeFilters = _columnFilters.Count;
        GD.Print($"FunctionalTree: Applied {activeFilters} column filters");
    }

    /// <summary>
    /// Creates a filter section for a specific column.
    /// </summary>
    /// <param name="parent">Parent container</param>
    /// <param name="columnIndex">Column index to create filter for</param>
    private void CreateColumnFilterSection(VBoxContainer parent, int columnIndex)
    {
        var columnName = _columnNames[columnIndex];

        // Column header
        var headerLabel = new Label
        {
            Text = columnName,
            HorizontalAlignment = HorizontalAlignment.Left
        };
        headerLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat { BgColor = Colors.DarkGray });
        parent.AddChild(headerLabel);

        // Get distinct values for this column
        var distinctValues = _originalData.Rows.Cast<DataRow>()
            .Select(row => row[columnIndex]?.ToString() ?? "")
            .Distinct()
            .OrderBy(v => v)
            .ToList();

        // Create scrollable container for checkboxes
        var checkboxScrollContainer = new ScrollContainer
        {
            CustomMinimumSize = new Vector2(0, Math.Min(150, distinctValues.Count * 25 + 10))
        };
        parent.AddChild(checkboxScrollContainer);

        var checkboxVBox = new VBoxContainer();
        checkboxScrollContainer.AddChild(checkboxVBox);

        // Get current filter for this column
        var currentFilter = _columnFilters.GetValueOrDefault(columnIndex, new HashSet<string>());
        var showAll = currentFilter.Count == 0;

        // Add "Select All" checkbox
        var selectAllCheckbox = new CheckBox
        {
            Text = "Select All",
            ButtonPressed = showAll
        };

        var valueCheckboxes = new List<CheckBox>();

        selectAllCheckbox.Toggled += (pressed) => {
            foreach (var checkbox in valueCheckboxes)
            {
                checkbox.ButtonPressed = pressed;
            }
        };
        checkboxVBox.AddChild(selectAllCheckbox);

        // Add separator
        var checkboxSeparator = new HSeparator();
        checkboxVBox.AddChild(checkboxSeparator);

        // Add checkbox for each distinct value
        foreach (var value in distinctValues)
        {
            var checkbox = new CheckBox
            {
                Text = string.IsNullOrEmpty(value) ? "(Empty)" : value,
                ButtonPressed = showAll || currentFilter.Contains(value)
            };

            checkbox.Toggled += (pressed) => {
                // Update select all checkbox
                selectAllCheckbox.ButtonPressed = valueCheckboxes.All(cb => cb.ButtonPressed);
            };

            valueCheckboxes.Add(checkbox);
            checkboxVBox.AddChild(checkbox);
        }

        // Store references for applying filters
        _filterDialogData[columnIndex] = (valueCheckboxes, distinctValues);

        // Add some spacing
        var spacer = new Control { CustomMinimumSize = new Vector2(0, 10) };
        parent.AddChild(spacer);
    }

    /// <summary>
    /// Dictionary to store filter dialog references for applying filters
    /// </summary>
    private Dictionary<int, (List<CheckBox> valueCheckboxes, List<string> distinctValues)> _filterDialogData = new();

    /// <summary>
    /// Applies the current filter selections.
    /// </summary>
    private void ApplyFilters()
    {
        if (_originalData == null) return;

        // Collect filter settings from stored dialog data
        _columnFilters.Clear();

        foreach (var kvp in _filterDialogData)
        {
            var columnIndex = kvp.Key;
            var (valueCheckboxes, distinctValues) = kvp.Value;

            var selectedValues = new HashSet<string>();

            for (int i = 0; i < valueCheckboxes.Count && i < distinctValues.Count; i++)
            {
                if (valueCheckboxes[i].ButtonPressed)
                {
                    selectedValues.Add(distinctValues[i]);
                }
            }

            // Only store filter if not all values are selected
            if (selectedValues.Count < distinctValues.Count)
            {
                _columnFilters[columnIndex] = selectedValues;
            }
        }

        // Clear dialog data
        _filterDialogData.Clear();

        // Apply filters to data
        FilterData();
        RefreshDisplay();
        EmitSignal("FilterStatusChanged", GetFilterStatusText());

        GD.Print($"FunctionalTree: Applied filters to {_columnFilters.Count} columns");
    }

    /// <summary>
    /// Filters the original data based on current filter settings.
    /// </summary>
    private void FilterData()
    {
        if (_originalData == null) return;

        _filteredData = _originalData.Clone();

        foreach (DataRow row in _originalData.Rows)
        {
            bool includeRow = true;

            // Check each column filter
            foreach (var filter in _columnFilters)
            {
                var columnIndex = filter.Key;
                var allowedValues = filter.Value;
                var cellValue = row[columnIndex]?.ToString() ?? "";

                if (!allowedValues.Contains(cellValue))
                {
                    includeRow = false;
                    break;
                }
            }

            if (includeRow)
            {
                _filteredData.ImportRow(row);
            }
        }

        // Re-apply sorting if active
        if (_currentSortColumn >= 0)
        {
            SortData();
        }
    }

    /// <summary>
    /// Refreshes the tree display with current filtered data.
    /// </summary>
    private void RefreshDisplay()
    {
        if (_filteredData == null) return;

        Clear();

        // Set up columns if not already done
        if (Columns != _filteredData.Columns.Count)
        {
            Columns = _filteredData.Columns.Count;
            ColumnTitlesVisible = true;
            HideRoot = true;

            for (int i = 0; i < _columnNames.Length && i < Columns; i++)
            {
                SetColumnTitle(i, _columnNames[i]);
                SetColumnExpand(i, false); // Disable auto-expand for manual sizing
            }

            // Apply column width settings
            ApplyColumnSettings();
        }

        var root = CreateItem();
        HideRoot = true;

        // Populate with filtered data
        foreach (DataRow row in _filteredData.Rows)
        {
            var item = CreateItem(root);

            for (int col = 0; col < _filteredData.Columns.Count && col < Columns; col++)
            {
                var value = row[col]?.ToString() ?? "";
                item.SetText(col, value);

                // Set text alignment
                if (col == 0) // First column (usually ID/Part Number)
                {
                    item.SetTextAlignment(col, HorizontalAlignment.Left);
                }
                else
                {
                    item.SetTextAlignment(col, HorizontalAlignment.Center);
                }
            }
        }

        EmitSignal("DataChanged");
    }
    #endregion
}
