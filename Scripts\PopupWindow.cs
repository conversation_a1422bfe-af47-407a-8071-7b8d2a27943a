using Global;
using Godot;
using System;

public partial class PopupWindow : Window
{

    public override void _Ready()
    {
        ConnectSignals();
    }
    private void ConnectSignals()
    {
        CloseRequested += OnCloseRequested;
        SizeChanged += OnSizeChanged;
    }
    private void OnSizeChanged()
    {
        MinSize = (Vector2I)GetContentsMinimumSize();
    }
    private void OnCloseRequested()
    {
        GD.Print("Close requested");
        QueueFree();
    }
}