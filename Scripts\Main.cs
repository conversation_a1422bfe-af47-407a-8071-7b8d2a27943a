using Godot;
using Godot.NativeInterop;
using System;
using System.Collections.Generic;

public partial class Main : PanelContainer
{
	[Export]
	public NavigationBar Navigation { get; set; }
	[Export]
	public PanelContainer MainPanel { get; set; }
	[Export]
	public PanelContainer AppBody { get; set; }
	public PackedScene LoginScenePacked { get; set; }
	public CustomSignals customSignals;
	public Boolean UserLoggedIn = false;
	public PackedScene AssistantSuitePacked { get; set; } = GD.Load<PackedScene>("res://Scenes/AssistantSuiteMain.tscn");
	public PackedScene ConnectorAssistantPacked { get; set; } = GD.Load<PackedScene>("res://Scenes/ConnectorAssistantMain.tscn");
	public PackedScene InventoryAssistantPacked { get; set; } = GD.Load<PackedScene>("res://Scenes/InventoryAssistantMain.tscn");
	public PackedScene ToolAssistantPacked { get; set; } = GD.Load<PackedScene>("res://Scenes/ToolAssistantMain.tscn");
	public Dictionary<string, PackedScene> appScenes { get; set; }


	public override void _Ready()
	{
		Initialize();
		ConnectSignals();
	}
	private void Initialize()
	{
		PromptLogin();
		appScenes = new Dictionary<string, PackedScene>()
		{
			{ "Assistant Suite", AssistantSuitePacked },
			{ "Connector Assistant", ConnectorAssistantPacked },
			{ "Inventory Assistant", InventoryAssistantPacked },
			{ "Tool Assistant", ToolAssistantPacked }
		};
		OpenScene("Assistant Suite");
	}
	private void ConnectSignals()
	{
		customSignals = GetNode<CustomSignals>("/root/CustomSignals");
		customSignals.AppSelected += OpenScene;
		customSignals.LoginSuccessful += SetLoginStatus;
	}
	private void PromptLogin()
	{
		if (!UserLoggedIn)
		{
			PackedScene LoginScenePacked = GD.Load<PackedScene>("res://Scenes/LoginScene.tscn");
			var loginSceneInstance = LoginScenePacked.Instantiate();
			AddChild(loginSceneInstance);
		}
	}
	private void SetLoginStatus()
	{
		UserLoggedIn = true;
	}
	private void OpenScene(string sceneName)
	{
		AppBody.GetChildren().Clear();
		var scene = appScenes[sceneName].Instantiate();
		AppBody.AddChild(scene);
		Navigation.PreviousApp = Navigation.CurrentApp;
		Navigation.CurrentApp = sceneName;

		if (sceneName == "Assistant Suite")
		{
			Navigation.UpdateNavigation(new List<string> { "Assistant Suite" });
			Navigation.BackButton.Visible = false;
			Navigation.ApplicationTitle.Text = "Assistant Suite";
			Navigation.ApplicationLogoButton.Icon = GD.Load<Texture2D>("res://Icons/AssistantSuiteLogo.svg");
		}
		else if (sceneName == "Connector Assistant")
		{
			Navigation.UpdateNavigation(new List<string> { "Assistant Suite", "Connector Assistant" });
			Navigation.BackButton.Visible = true;
			Navigation.ApplicationTitle.Text = "Connector Assistant";
			Navigation.ApplicationLogoButton.Icon = GD.Load<Texture2D>("res://Icons/ConnectorAssistantLogo.svg");
		}
		else if (sceneName == "Inventory Assistant")
		{
			Navigation.UpdateNavigation(new List<string> { "Assistant Suite", "Inventory Assistant" });
			Navigation.BackButton.Visible = true;
			Navigation.ApplicationTitle.Text = "Inventory Assistant";
			Navigation.ApplicationLogoButton.Icon = GD.Load<Texture2D>("res://Icons/InventoryAssistantLogo.svg");
		}
		else if (sceneName == "Tool Assistant")
		{
			Navigation.UpdateNavigation(new List<string> { "Assistant Suite", "Tool Assistant" });
			Navigation.BackButton.Visible = true;
			Navigation.ApplicationTitle.Text = "Tool Assistant";
			Navigation.ApplicationLogoButton.Icon = GD.Load<Texture2D>("res://Icons/ToolAssistantLogo.svg");
		}
	}
}
