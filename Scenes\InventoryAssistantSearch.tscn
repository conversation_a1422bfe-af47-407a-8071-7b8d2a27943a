[gd_scene load_steps=8 format=3 uid="uid://cgnudm1ucjwej"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_x8cwu"]
[ext_resource type="Texture2D" uid="uid://cmeh2k15j2ngc" path="res://Icons/Search.svg" id="2_8u6u6"]
[ext_resource type="Texture2D" uid="uid://dhkmsq5so7yi3" path="res://Icons/Filter.svg" id="3_vodu7"]
[ext_resource type="Texture2D" uid="uid://k1ubl6xcngk2" path="res://Icons/Question.svg" id="4_l23yq"]
[ext_resource type="Texture2D" uid="uid://d14cwjbmu8htb" path="res://Icons/Good.svg" id="5_xn8n3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sfcpv"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qgc6x"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[node name="InventoryAssistantSearch" type="HSplitContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_vertical = 3
theme = ExtResource("1_x8cwu")
theme_override_constants/separation = 0

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="SearchPanel" type="PanelContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 75)
layout_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/SearchPanel"]
layout_mode = 2
size_flags_vertical = 4

[node name="LineEdit" type="LineEdit" parent="VBoxContainer/SearchPanel/HBoxContainer"]
custom_minimum_size = Vector2(400, 0)
layout_mode = 2
placeholder_text = "Search Part Number"
expand_to_text_length = true
right_icon = ExtResource("2_8u6u6")

[node name="Button" type="Button" parent="VBoxContainer/SearchPanel/HBoxContainer"]
layout_mode = 2
text = "Search"

[node name="Button2" type="Button" parent="VBoxContainer/SearchPanel/HBoxContainer"]
layout_mode = 2
icon = ExtResource("3_vodu7")
flat = true

[node name="Button3" type="Button" parent="VBoxContainer/SearchPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 10
icon = ExtResource("4_l23yq")
flat = true

[node name="SearchTree" type="PanelContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(800, 0)
layout_mode = 2
size_flags_vertical = 3

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer/SearchTree"]
layout_mode = 2

[node name="Tree" type="Tree" parent="VBoxContainer/SearchTree/ScrollContainer"]
custom_minimum_size = Vector2(500, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/title_button_normal = SubResource("StyleBoxEmpty_sfcpv")
theme_override_styles/selected_focus = SubResource("StyleBoxFlat_qgc6x")
select_mode = 1

[node name="StatusBar" type="PanelContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 8

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/StatusBar"]
layout_mode = 2

[node name="TextureRect" type="TextureRect" parent="VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
texture = ExtResource("5_xn8n3")
stretch_mode = 5

[node name="Label" type="Label" parent="VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
text = "Connected to Database"

[node name="LineEdit" type="LineEdit" parent="VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Database Status"
expand_to_text_length = true

[node name="ProgressBar" type="ProgressBar" parent="VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
value = 64.37
show_percentage = false

[node name="PanelContainer2" type="PanelContainer" parent="."]
layout_mode = 2
