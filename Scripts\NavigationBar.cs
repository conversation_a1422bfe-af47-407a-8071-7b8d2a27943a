using Godot;
using System;
using System.Collections.Generic;

public partial class NavigationBar : PanelContainer
{
    [Export]
    public Button BackButton { get; set; }
    [Export]
    public Button ApplicationLogoButton { get; set; }
    [Export]
    public Label ApplicationTitle { get; set; }
    [Export]
    public Button HomeButton { get; set; }
    [Export]
    public HBoxContainer ApplicationNavigationContainer { get; set; }
    public CustomSignals customSignals { get; set; }
    public Main main { get; set; }
    public string PreviousApp = "";
    public string CurrentApp = "";

    public override void _Ready()
    {
        ConnectSignals();
    }
    private void ConnectSignals()
    {
        try
        {
            main = GetNode<Main>("/root/Main");
            customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        }
        catch
        {
            GD.Print("Error connecting to global scripts");
        }

        BackButton.Pressed += OnBackButtonClicked;
        HomeButton.Pressed += OnHomeButtonClicked;
        ApplicationLogoButton.Pressed += OnApplicationLogoButtonClicked;
    }
    private void OnBackButtonClicked()
    {
        GD.Print("Back button clicked");
        customSignals.EmitSignal("AppSelected", PreviousApp);
    }
    private void OnHomeButtonClicked()
    {
        GD.Print("Home button clicked");
        customSignals.EmitSignal("AppSelected", "Assistant Suite");
    }
    private void OnApplicationLogoButtonClicked()
    {
        GD.Print("Application logo button clicked");
        customSignals.EmitSignal("AppSelected", "Assistant Suite");
    }

    public void UpdateNavigation(List<string> Navigation)
    {
        foreach (Control child in ApplicationNavigationContainer.GetChildren())
        {
            if (child != HomeButton)
            {
                child.QueueFree();
            }
        }

        for (int i = 0; i < Navigation.Count; i++)
        {
            GD.Print(Navigation[i]);
            Button LocationButton = new Button();
            LocationButton.Icon = GD.Load<Texture2D>("res://Icons/Right.svg");
            LocationButton.IconAlignment = HorizontalAlignment.Left;
            LocationButton.VerticalIconAlignment = VerticalAlignment.Center;
            LocationButton.Flat = true;
            LocationButton.Text = Navigation[i];
            ApplicationNavigationContainer.AddChild(LocationButton);
        }
    }
}
