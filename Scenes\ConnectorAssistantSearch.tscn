[gd_scene load_steps=6 format=3 uid="uid://ch26x1bs8mjgi"]

[ext_resource type="Texture2D" uid="uid://cmeh2k15j2ngc" path="res://Icons/Search.svg" id="1_1qrho"]
[ext_resource type="Script" uid="uid://b6bfp4xrw78k" path="res://Scripts/ConnectorAssistantMain.cs" id="1_vxpl3"]
[ext_resource type="Texture2D" uid="uid://dhkmsq5so7yi3" path="res://Icons/Filter.svg" id="2_vxpl3"]
[ext_resource type="Texture2D" uid="uid://k1ubl6xcngk2" path="res://Icons/Question.svg" id="3_hsy2e"]
[ext_resource type="Texture2D" uid="uid://d14cwjbmu8htb" path="res://Icons/Good.svg" id="4_f6c7j"]

[node name="PanelContainer" type="PanelContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_vxpl3")

[node name="ConnectorAssistantSearch" type="HSplitContainer" parent="."]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 0

[node name="VBoxContainer" type="VBoxContainer" parent="ConnectorAssistantSearch"]
layout_mode = 2

[node name="SearchPanel" type="PanelContainer" parent="ConnectorAssistantSearch/VBoxContainer"]
custom_minimum_size = Vector2(0, 75)
layout_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="ConnectorAssistantSearch/VBoxContainer/SearchPanel"]
layout_mode = 2
size_flags_vertical = 4

[node name="LineEdit" type="LineEdit" parent="ConnectorAssistantSearch/VBoxContainer/SearchPanel/HBoxContainer"]
custom_minimum_size = Vector2(400, 0)
layout_mode = 2
placeholder_text = "Search Part Number"
expand_to_text_length = true
right_icon = ExtResource("1_1qrho")

[node name="Button" type="Button" parent="ConnectorAssistantSearch/VBoxContainer/SearchPanel/HBoxContainer"]
layout_mode = 2
text = "Search"

[node name="Button2" type="Button" parent="ConnectorAssistantSearch/VBoxContainer/SearchPanel/HBoxContainer"]
layout_mode = 2
icon = ExtResource("2_vxpl3")
flat = true

[node name="Button3" type="Button" parent="ConnectorAssistantSearch/VBoxContainer/SearchPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 10
icon = ExtResource("3_hsy2e")
flat = true

[node name="SearchTree" type="PanelContainer" parent="ConnectorAssistantSearch/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Tree" type="Tree" parent="ConnectorAssistantSearch/VBoxContainer/SearchTree"]
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
select_mode = 1

[node name="ScrollContainer" type="ScrollContainer" parent="ConnectorAssistantSearch/VBoxContainer/SearchTree"]
visible = false
layout_mode = 2

[node name="StatusBar" type="PanelContainer" parent="ConnectorAssistantSearch/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 8

[node name="HBoxContainer" type="HBoxContainer" parent="ConnectorAssistantSearch/VBoxContainer/StatusBar"]
layout_mode = 2

[node name="TextureRect" type="TextureRect" parent="ConnectorAssistantSearch/VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
texture = ExtResource("4_f6c7j")
stretch_mode = 5

[node name="Label" type="Label" parent="ConnectorAssistantSearch/VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
text = "Connected to Database"

[node name="LineEdit" type="LineEdit" parent="ConnectorAssistantSearch/VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Database Status"
expand_to_text_length = true

[node name="ProgressBar" type="ProgressBar" parent="ConnectorAssistantSearch/VBoxContainer/StatusBar/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
value = 64.37
show_percentage = false
