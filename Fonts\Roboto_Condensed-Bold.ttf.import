[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://b7sws01stp0qb"
path="res://.godot/imported/Roboto_Condensed-Bold.ttf-51f5a474e2180da41cc55d29c9802d93.fontdata"

[deps]

source_file="res://Fonts/Roboto_Condensed-Bold.ttf"
dest_files=["res://.godot/imported/Roboto_Condensed-Bold.ttf-51f5a474e2180da41cc55d29c9802d93.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
