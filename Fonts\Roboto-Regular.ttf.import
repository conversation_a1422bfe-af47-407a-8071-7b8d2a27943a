[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dg6qs6ursn3qb"
path="res://.godot/imported/Roboto-Regular.ttf-53299d412e710e22f7b066c65749c529.fontdata"

[deps]

source_file="res://Fonts/Roboto-Regular.ttf"
dest_files=["res://.godot/imported/Roboto-Regular.ttf-53299d412e710e22f7b066c65749c529.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
