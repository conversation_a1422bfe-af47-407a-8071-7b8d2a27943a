[gd_scene load_steps=13 format=3 uid="uid://c64qqrmhahd1q"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_vp8ct"]
[ext_resource type="Script" uid="uid://b1gimt56i2nw5" path="res://Scripts/ToolAssistantWireBundleCalculator.cs" id="2_t0edo"]
[ext_resource type="Script" uid="uid://clwhk1tx1kwrq" path="res://Scripts/ItemTreePanel.cs" id="3_wom67"]
[ext_resource type="Texture2D" uid="uid://dsxwipstyp22a" path="res://Icons/Reset.svg" id="4_l71wd"]
[ext_resource type="PackedScene" uid="uid://d11qaem6f3lvm" path="res://Scenes/CirclePacker.tscn" id="5_l71wd"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t0edo"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_wom67"]
content_margin_left = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_30021"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_l71wd"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_xhsh1"]
content_margin_left = 4.0
content_margin_top = 4.0
content_margin_right = 4.0
content_margin_bottom = 4.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_cgdm6"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2g5cf"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[node name="WireBundleMain" type="PanelContainer" node_paths=PackedStringArray("WireGroups", "ResetButton", "CalculateButton", "AddButton", "BundleDiameterLine", "WireTree", "CirclePacker", "CirclePackerResetButton")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_vp8ct")
theme_override_styles/panel = SubResource("StyleBoxFlat_t0edo")
script = ExtResource("2_t0edo")
WireGroups = NodePath("HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer/ScrollContainer/WireGroups")
ResetButton = NodePath("HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer3/ResetButton")
CalculateButton = NodePath("HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/CalculateButton")
AddButton = NodePath("HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer/ScrollContainer/WireGroups/AddButton")
BundleDiameterLine = NodePath("HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer/BundleDiameterLine")
WireTree = NodePath("HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer2/WireTree")
CirclePacker = NodePath("HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/PanelContainer/SubViewportContainer/SubViewport/CirclePacker")
CirclePackerResetButton = NodePath("HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer4/CirclePackerResetButton")

[node name="HSplitContainer" type="HSplitContainer" parent="."]
layout_mode = 2

[node name="PanelContainer2" type="PanelContainer" parent="HSplitContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_wom67")

[node name="MarginContainer" type="MarginContainer" parent="HSplitContainer/PanelContainer2"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="VBoxContainer2" type="VBoxContainer" parent="HSplitContainer/PanelContainer2/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="VSplitContainer" type="VSplitContainer" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2"]
layout_mode = 2
size_flags_vertical = 3

[node name="PanelContainer2" type="PanelContainer" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_30021")

[node name="WireTree" type="Tree" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer2"]
layout_mode = 2
size_flags_vertical = 3
select_mode = 1
scroll_horizontal_enabled = false
script = ExtResource("3_wom67")

[node name="PanelContainer" type="PanelContainer" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_30021")

[node name="ScrollContainer" type="ScrollContainer" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer"]
custom_minimum_size = Vector2(350, 200)
layout_mode = 2
size_flags_vertical = 3
horizontal_scroll_mode = 0

[node name="WireGroups" type="VBoxContainer" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 10

[node name="AddButton" type="Button" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2/VSplitContainer/PanelContainer/ScrollContainer/WireGroups"]
layout_mode = 2
size_flags_horizontal = 4
text = "Add"

[node name="CalculateButton" type="Button" parent="HSplitContainer/PanelContainer2/MarginContainer/VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
text = "Calculate"

[node name="PanelContainer" type="PanelContainer" parent="HSplitContainer"]
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_wom67")

[node name="MarginContainer2" type="MarginContainer" parent="HSplitContainer/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="PanelContainer" type="PanelContainer" parent="HSplitContainer/PanelContainer/MarginContainer2"]
custom_minimum_size = Vector2(400, 400)
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxEmpty_l71wd")

[node name="VBoxContainer" type="VBoxContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="HBoxContainer4" type="HBoxContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer4"]
layout_mode = 2
size_flags_horizontal = 2
text = "Bundle Visualizer"
horizontal_alignment = 1

[node name="CirclePackerResetButton" type="Button" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer4"]
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0
icon = ExtResource("4_l71wd")
flat = true

[node name="PanelContainer" type="PanelContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_30021")

[node name="SubViewportContainer" type="SubViewportContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/PanelContainer"]
custom_minimum_size = Vector2(400, 400)
layout_mode = 2
size_flags_vertical = 3
stretch = true

[node name="SubViewport" type="SubViewport" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/PanelContainer/SubViewportContainer"]
transparent_bg = true
handle_input_locally = false
size = Vector2i(400, 416)
render_target_update_mode = 4

[node name="CirclePacker" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/PanelContainer/SubViewportContainer/SubViewport" node_paths=PackedStringArray("AirGapSlider", "TightenButton", "ResetButton") instance=ExtResource("5_l71wd")]
ScaleFactor = 100.0
CircleCount = 35
MinRadius = 0.1
MaxRadius = 1.5
TighteningForce = 0.538
AirGapSlider = NodePath("../../../../HBoxContainer2/HSlider")
TightenButton = NodePath("../../../../HBoxContainer3/TightenButton")
ResetButton = NodePath("../../../../HBoxContainer3/ResetButton")
_airGapLabelPath = NodePath("../../../../HBoxContainer2/Label")
_boundingRadiusLabelPath = NodePath("../../../../HBoxContainer/BundleDiameterLine")

[node name="HBoxContainer2" type="VBoxContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
text = "Adjust Packing Border"
horizontal_alignment = 1

[node name="HSlider" type="HSlider" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
theme_override_styles/slider = SubResource("StyleBoxFlat_xhsh1")

[node name="HBoxContainer3" type="HBoxContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2

[node name="TightenButton" type="Button" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2
toggle_mode = true
text = "Tighten"

[node name="ResetButton" type="Button" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3
text = "Reset"

[node name="HBoxContainer" type="HBoxContainer" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 8

[node name="Label" type="Label" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
text = "Bundle Diameter"

[node name="BundleDiameterLine" type="LineEdit" parent="HSplitContainer/PanelContainer/MarginContainer2/PanelContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/read_only = SubResource("StyleBoxFlat_cgdm6")
theme_override_styles/normal = SubResource("StyleBoxFlat_2g5cf")
editable = false
expand_to_text_length = true
