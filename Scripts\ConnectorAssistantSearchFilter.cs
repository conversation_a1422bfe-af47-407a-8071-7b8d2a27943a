using Godot;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

public partial class ConnectorAssistantSearchFilter : Window
{
    [Export] public Tree TypeTree { get; set; }
    [Export] public Tree AttributeTree { get; set; }
    [Export] public Tree ValueTree { get; set; }
    [Export] public ConnectorAssistantMain connectorAssistantMain { get; set; }
    [Export] public Button SubmitButton { get; set; }
    [Export] public Button ResetButton { get; set; }
    [Export] public VBoxContainer ValueContainer { get; set; }
    [Export] public ItemPanel itemPanel { get; set; }
    private CustomSignals customSignals;
    private SQLHandler sqlHandler;
    public List<string> attributesList = new List<string>();
    public string type = "ALL";
    public bool RefineSearch = false;

    public async override void _Ready()
    {
        ConnectSignals();
        await LoadTypeTree();
    }
    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");

        TypeTree.ItemSelected += OnTypeTreeItemSelected;
        AttributeTree.ItemSelected += OnAttributeTreeItemSelected;
        ValueTree.ItemSelected += OnValueTreeItemSelected;
        AboutToPopup += OnAboutToPopup;
        CloseRequested += OnCloseRequested;
        VisibilityChanged += OnVisibilityChanged;
        SubmitButton.Pressed += OnSubmitButtonPressed;
        ResetButton.Pressed += OnResetButtonPressed;
    }

    private async Task LoadTypeTree()
    {
        TypeTree.Clear();
        TypeTree.Columns = 2;
        TypeTree.SetColumnTitle(1, "Type");
        TypeTree.SetColumnExpand(0, false);
        TypeTree.SetColumnExpand(1, true);
        TypeTree.SetColumnTitleAlignment(0, HorizontalAlignment.Center);
        TypeTree.SetColumnCustomMinimumWidth(0, 64);

        var root = TypeTree.CreateItem();
        TypeTree.HideRoot = true;

        DataTable partTypes = await sqlHandler.QueryAsync($"SELECT [type] FROM dbo.types");

        TreeItem allType = TypeTree.CreateItem(root);
        allType.SetCellMode(0, TreeItem.TreeCellMode.Check);
        allType.SetTextAlignment(0, HorizontalAlignment.Center);
        allType.SetEditable(0, false);
        allType.SetText(1, "ALL".ToString());

        foreach (DataRow row in partTypes.Rows)
        {
            TreeItem item = TypeTree.CreateItem(root);
            item.SetCellMode(0, TreeItem.TreeCellMode.Check);
            item.SetTextAlignment(0, HorizontalAlignment.Center);
            item.SetEditable(0, false);
            item.SetText(1, row["type"].ToString());
        }

        allType.Select(0);
    }

    private async void LoadAttributeTree(string type)
    {
        AttributeTree.Clear();
        AttributeTree.Columns = 2;
        AttributeTree.SetColumnTitle(1, "Attribute");
        AttributeTree.SetColumnExpand(0, false);
        AttributeTree.SetColumnExpand(1, true);
        AttributeTree.SetColumnTitleAlignment(0, HorizontalAlignment.Center);
        AttributeTree.SetColumnCustomMinimumWidth(0, 64);

        var root = AttributeTree.CreateItem();
        AttributeTree.HideRoot = true;
        attributesList = new List<string>();
        List<string> defaultChecked = new List<string>()
        {
            "Part Number",
            "Description",
            "Manufacturer",
            "Gender",
            "Shell Size",
            "Number of Contacts",
            "Contact Type",
            "Angle",
            "Wire Gauge"
        };
        if (type != "ALL")
        {
            DataTable partAttributes = await sqlHandler.QueryAsync($"SELECT dbo.attributes.display_name, types.type FROM dbo.attributes JOIN dbo.type_attribs ON attributes.[index] = type_attribs.attrib_index JOIN dbo.types ON type_attribs.type_index = types.[index] WHERE types.type IN ('{type}') ORDER BY type_attribs.attrib_index");
            foreach (DataRow row in partAttributes.Rows)
            {
                if (row["type"].ToString() == type)
                {
                    TreeItem item = AttributeTree.CreateItem(root);
                    item.SetCellMode(0, TreeItem.TreeCellMode.Check);
                    item.SetTextAlignment(0, HorizontalAlignment.Center);
                    item.SetEditable(0, false);
                    item.SetChecked(0, false);
                    item.SetText(1, row["display_name"].ToString());
                    attributesList.Add(row["display_name"].ToString());
                    if (defaultChecked.Contains(row["display_name"].ToString()))
                    {
                        item.SetChecked(0, true);
                    }
                }
            }
        }
        else if (type == "ALL")
        {
            DataTable partAttributes = await sqlHandler.QueryAsync($"SELECT [attribute],[display_name] FROM [dbo].[attributes]");

            foreach (DataRow row in partAttributes.Rows)
            {
                TreeItem item = AttributeTree.CreateItem(root);
                item.SetCellMode(0, TreeItem.TreeCellMode.Check);
                item.SetTextAlignment(0, HorizontalAlignment.Center);
                item.SetEditable(0, false);
                item.SetChecked(0, false);
                item.SetText(1, row["display_name"].ToString());
                attributesList.Add(row["display_name"].ToString());
                if (defaultChecked.Contains(row["display_name"].ToString()))
                {
                    item.SetChecked(0, true);
                }
            }
        }
        await LoadValueTree();
    }
    private async Task LoadValueTree()
    {
        DataTable dataTypeTable = await sqlHandler.QueryAsync($"SELECT [display_name], [dt_index] FROM [dbo].[attributes]");
        DataTable attributeStringTable = await sqlHandler.QueryAsync($"SELECT [display_name], [attribute], [display], [value] FROM [dbo].[csAttrib_str] JOIN attributes ON attributes.[index] = csAttrib_str.attrib_index");
        DataTable attributeIntTable = await sqlHandler.QueryAsync($"SELECT [display_name], [attribute], [display], [value] FROM [dbo].[csAttrib_int] JOIN attributes ON attributes.[index] = csAttrib_int.attrib_index");
        // DataTable PartAttributeStringTable = await sqlHandler.QueryAsync($"SELECT [display_name], [attribute], [value] FROM [CableDesignTool_Play].[dbo].[part_attrib_str] JOIN attributes ON attributes.[index] = part_attrib_str.attrib_index");
        PackedScene packedScene = GD.Load<PackedScene>("res://Scenes/DataContainer.tscn");

        List<string> missingAttributes = new List<string>();
        List<string> hiddenAttributes = new List<string>();
        List<string> shownAttributes = new List<string>();
        foreach (TreeItem attribute in AttributeTree.GetRoot().GetChildren())
        {
            if (attribute.IsChecked(0))
            {
                missingAttributes.Add(attribute.GetText(1));
                shownAttributes.Add(attribute.GetText(1));
            }
            else
            {
                hiddenAttributes.Add(attribute.GetText(1));
            }
        }

        if (ValueContainer.GetChildCount() > 0)
        {
            foreach (DataContainer item in ValueContainer.GetChildren())
            {
                if (missingAttributes.Contains(item.label.Text))
                {
                    missingAttributes.Remove(item.label.Text);
                }
            }
        }

        foreach (string attribute in missingAttributes)
        {
            DataContainer Row = (DataContainer)packedScene.Instantiate();
            ValueContainer.AddChild(Row);

            Label attributeLabel = Row.label;
            attributeLabel.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            attributeLabel.Text = attribute;

            string datatype = dataTypeTable.Select($"[display_name] = '{attribute}'").ToList()[0]["dt_index"].ToString();
            if (datatype == "1")
            {
                // DataType: String
                if (attributeStringTable.Select($"[display_name] = '{attribute}'").Count() == 0)
                {
                    LineEdit search = new LineEdit();
                    search.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
                    search.PlaceholderText = "Search";
                    Row.DataContainerPanel.AddChild(search);
                }
                else
                {
                    OptionButton valueOptions = new OptionButton();
                    valueOptions.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
                    Row.DataContainerPanel.AddChild(valueOptions);

                    foreach (DataRow row in attributeStringTable.Select($"[display_name] = '{attribute}'"))
                    {
                        valueOptions.AddItem(row["display"].ToString());
                    }
                }

            }
            else if (datatype == "2")
            {
                // DataType: Integer
                if (attributeIntTable.Select($"[display_name] = '{attribute}'").Count() == 0)
                {
                    SpinBox searchInt = new SpinBox();
                    searchInt.MaxValue = 999;
                    searchInt.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
                    Row.DataContainerPanel.AddChild(searchInt);
                }
                else
                {
                    OptionButton valueOptions = new OptionButton();
                    valueOptions.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
                    Row.DataContainerPanel.AddChild(valueOptions);

                    foreach (DataRow row in attributeIntTable.Select($"[display_name] = '{attribute}'"))
                    {
                        valueOptions.AddItem(row["display"].ToString());
                    }
                }
            }
            else if (datatype == "3")
            {
                // DataType: Float
            }
            else if (datatype == "4")
            {
                // DataType: Boolean
                CheckBox checkBox = new CheckBox();
                checkBox.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
                Row.DataContainerPanel.AddChild(checkBox);
                
            }
        }
        foreach (DataContainer item in ValueContainer.GetChildren())
        {
            if (hiddenAttributes.Contains(item.label.Text))
            {
                item.Hide();
            }
            else if (shownAttributes.Contains(item.label.Text))
            {
                item.Show();
            }
            else
            {
                item.Hide();
            }
        }
    }
    private void OnTypeTreeItemSelected()
    {
        TreeItem selectedItem = TypeTree.GetSelected();
        selectedItem.SetChecked(0, true);

        foreach (TreeItem item in TypeTree.GetRoot().GetChildren())
        {
            if (item != selectedItem)
            {
                item.SetChecked(0, false);
            }
        }

        LoadAttributeTree(selectedItem.GetText(1));
    }
    private async void OnAttributeTreeItemSelected()
    {
        TreeItem selectedItem = AttributeTree.GetSelected();
        if (selectedItem.IsChecked(0))
        {
            selectedItem.SetChecked(0, false);
        }
        else
        {
            selectedItem.SetChecked(0, true);
        }
        AttributeTree.DeselectAll();

        await LoadValueTree();
    }
    private void OnValueTreeItemSelected()
    {
        TreeItem selectedItem = ValueTree.GetSelected();
        // if (selectedItem.IsChecked(0))
        // {
        //     selectedItem.SetChecked(0, false);
        // }
        // else
        // {
        //     selectedItem.SetChecked(0, true);
        // }

    }
    private void OnAboutToPopup()
    {
        

    }
    private void OnVisibilityChanged()
    {
        // Position = GetTree().Root.Size / 2 - Size / 2;

        // if (Visible == false)
        // {
        //     Tween tween = GetTree().CreateTween();
        //     tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        //     tween.TweenProperty(this, "position", Position + new Vector2I(0, -200), 0.3f);
        // }
        // else
        // {
        //     Tween tween = GetTree().CreateTween();
        //     tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        //     tween.TweenProperty(this, "position", Position + new Vector2I(0, -200), 0.3f);
        // }
    }
    private void OnCloseRequested()
    {
        // Tween tween = GetTree().CreateTween();
        // tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        // tween.TweenProperty(this, "position", Position + new Vector2I(0, 200), 0.3f);
    }
    public string GetSelectedType()
    {
        type = "ALL";
        foreach (TreeItem item in TypeTree.GetRoot().GetChildren())
        {
            if (item.IsChecked(0))
            {
                type = item.GetText(1);
            }
        }
        return type;
    }
    public async Task<List<string>> GetSelectedAttributes()
    {
        List<string> attributes = new List<string>();
        foreach (TreeItem item in AttributeTree.GetRoot().GetChildren())
        {
            if (item.IsChecked(0))
            {
                attributes.Add(item.GetText(1));
            }
        }
        List<string> correctedAttributes = new List<string>();
        DataTable attributeTable = await sqlHandler.QueryAsync($"SELECT [attribute], [display_name] FROM [dbo].[attributes] WHERE [display_name] IN ('{string.Join("','", attributes)}')");
        foreach (string attribute in attributes)
        {
            attributeTable.Select($"[display_name] = '{attribute}'").ToList().ForEach(row => correctedAttributes.Add(row["attribute"].ToString()));
        }

        return correctedAttributes;
    }
    public async void OnSubmitButtonPressed()
    {
        Visible = false;
        await connectorAssistantMain.LoadItemTree(QueryBuild());
    }
    public void OnResetButtonPressed()
    {
        LoadAttributeTree("ALL");
    }
    public string QueryBuild()
    {
        RefineSearch = false;
        string PartType = TypeTree.GetSelected().GetText(1);

        List<string> attributeList = new List<string>();
        foreach (TreeItem item in AttributeTree.GetRoot().GetChildren())
        {
            if (item.IsChecked(0))
            {
                attributeList.Add(item.GetText(1));
            }
        }

        string attributeColumnSection = "";
        string attributeSelectionSection = "";
        attributeColumnSection = "";
        foreach (string attribute in attributeList)
        {
            attributeColumnSection += $"STRING_AGG(CASE WHEN attr.display_name = '{attribute}' THEN COALESCE(csl.DisplayValue, uav.attribute_value) END, ', ') AS [{attribute}], \n";
            attributeSelectionSection += $"[{attribute}],";
        }
        attributeColumnSection = attributeColumnSection.TrimEnd(' ', '\n', ',');
        attributeSelectionSection = attributeSelectionSection.TrimEnd(',');

        string whereClauseSection = "";
        if (ValueContainer.GetChildCount() > 0)
        {
            foreach (DataContainer item in ValueContainer.GetChildren())
            {
                if (item.checkBox.IsPressed() && item.Visible)
                {
                    RefineSearch = true;
                    whereClauseSection += item.GetData() + " AND ";
                }
            }
            if (PartType == "ALL")
            {
                whereClauseSection = "WHERE " + whereClauseSection.TrimEnd('A', 'N', 'D', ' ');
            }
            else
            {
                whereClauseSection = "WHERE " + $"[PartType] = '{PartType}'" + " AND " + whereClauseSection.TrimEnd('A', 'N', 'D', ' ');
            }
        }

        if (!RefineSearch)
        {
            if (PartType == "ALL")
            {
                whereClauseSection = "";
            }
            else
            {
                whereClauseSection = $"WHERE [PartType] = '{PartType}'";
            }
        }

        string ultimateQuery = @$"
        WITH UnifiedAttributeValues AS 
        (
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_str
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_int
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_float
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_bit
        ),
        ChoiceSetLookups AS (
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)) AS [Value], display AS DisplayValue FROM csAttrib_str
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_int
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_float
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_bit
        ),
        PivotedPartsData AS
        (
        SELECT
            p.[index] AS PartId,
            p.PartNumber,
            t.type AS [PartType],
            {attributeColumnSection}
        FROM
            [dbo].[parts] p
        JOIN
            dbo.part_types pt ON p.[index] = pt.part_index
        JOIN
            dbo.types t ON pt.type_index = t.[index]
        LEFT JOIN
            dbo.type_attribs ta ON t.[index] = ta.type_index
        LEFT JOIN
            dbo.attributes attr ON ta.attrib_index = attr.[index]
        LEFT JOIN
            UnifiedAttributeValues uav ON p.[index] = uav.part_index AND attr.[index] = uav.attrib_index
        LEFT JOIN
            ChoiceSetLookups csl ON uav.attrib_index = csl.attrib_index AND uav.attribute_value = csl.[Value]
        GROUP BY
            t.type, p.[index], p.PartNumber
        )
        SELECT [PartNumber], [PartType], {attributeSelectionSection}
        FROM PivotedPartsData
        {whereClauseSection}
        ORDER BY PartNumber";

        GD.Print(ultimateQuery);
        return ultimateQuery;
    }
}
