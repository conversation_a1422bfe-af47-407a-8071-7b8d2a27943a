[gd_scene load_steps=9 format=3 uid="uid://b8hsj1lvnjiff"]

[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_r8mba"]
[ext_resource type="Script" uid="uid://dqe3fn53ndfyy" path="res://Scripts/PopupWindow.cs" id="2_lk1l7"]
[ext_resource type="Script" uid="uid://bkd2o4p6b1msr" path="res://Scripts/HelpScene.cs" id="3_6oq1s"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6oq1s"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_yuu6e"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.350846, 0.306551, 0.671066, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_r8mba"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.251, 0.271, 0.294, 0.051)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_lk1l7"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.313726, 0.270588, 0.611765, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f3218"]
content_margin_left = 8.0
content_margin_top = 6.0
content_margin_right = 8.0
content_margin_bottom = 6.0
bg_color = Color(0.314, 0.271, 0.612, 0)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[node name="HelpScene" type="Window"]
title = "Help"
initial_position = 1
size = Vector2i(500, 450)
theme = ExtResource("1_r8mba")
script = ExtResource("2_lk1l7")

[node name="HelpScene" type="PanelContainer" parent="." node_paths=PackedStringArray("BugID", "BugCategoryContainer", "BugPrioritySelection", "BugDescription", "BugEmailCheckbox", "BugSubmitButton", "BugErrorLabel", "RequestID", "RequestCategoryContainer", "RequestAssistantSelection", "RequestDescription", "RequestEmailCheckbox", "RequestSubmitButton", "RequestErrorLabel")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_6oq1s")
script = ExtResource("3_6oq1s")
BugID = NodePath("TabContainer/Bug Report/VBoxContainer/BugID")
BugCategoryContainer = NodePath("TabContainer/Bug Report/VBoxContainer/BugCategoryContainer")
BugPrioritySelection = NodePath("TabContainer/Bug Report/VBoxContainer/HBoxContainer3/BugPrioritySelection")
BugDescription = NodePath("TabContainer/Bug Report/VBoxContainer/BugDescription")
BugEmailCheckbox = NodePath("TabContainer/Bug Report/VBoxContainer/HBoxContainer4/BugEmailCheckbox")
BugSubmitButton = NodePath("TabContainer/Bug Report/VBoxContainer/HBoxContainer4/BugSubmitButton")
BugErrorLabel = NodePath("TabContainer/Bug Report/VBoxContainer/BugErrorLabel")
RequestID = NodePath("TabContainer/Request Feature/VBoxContainer/RequestID")
RequestCategoryContainer = NodePath("TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer")
RequestAssistantSelection = NodePath("TabContainer/Request Feature/VBoxContainer/HBoxContainer2/RequestAssistantSelection")
RequestDescription = NodePath("TabContainer/Request Feature/VBoxContainer/RequestDescription")
RequestEmailCheckbox = NodePath("TabContainer/Request Feature/VBoxContainer/HBoxContainer3/RequestEmailCheckbox")
RequestSubmitButton = NodePath("TabContainer/Request Feature/VBoxContainer/HBoxContainer3/RequestSubmitButton")
RequestErrorLabel = NodePath("TabContainer/Request Feature/VBoxContainer/RequestErrorLabel")

[node name="TabContainer" type="TabContainer" parent="HelpScene"]
layout_mode = 2
current_tab = 1

[node name="Bug Report" type="MarginContainer" parent="HelpScene/TabContainer"]
visible = false
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10
metadata/_tab_index = 0

[node name="VBoxContainer" type="VBoxContainer" parent="HelpScene/TabContainer/Bug Report"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="BugID" type="LineEdit" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2
placeholder_text = "ID Number"
editable = false

[node name="HSeparator2" type="HSeparator" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2

[node name="BugCategoryContainer" type="FlowContainer" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2

[node name="Button" type="Button" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/BugCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Visual Issue"

[node name="Button2" type="Button" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/BugCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Performance Issue"

[node name="Button3" type="Button" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/BugCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Data Issue"

[node name="Button4" type="Button" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/BugCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Crash/Error"

[node name="Button5" type="Button" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/BugCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Other"

[node name="HSeparator" type="HSeparator" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2

[node name="HBoxContainer3" type="HBoxContainer" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/HBoxContainer3"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "Priority:"

[node name="BugPrioritySelection" type="OptionButton" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3
selected = 2
item_count = 3
popup/item_0/text = "High"
popup/item_0/id = 0
popup/item_1/text = "Medium"
popup/item_1/id = 1
popup/item_2/text = "Low"
popup/item_2/id = 2

[node name="BugDescription" type="TextEdit" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
custom_minimum_size = Vector2(0, 100)
layout_mode = 2
size_flags_vertical = 3
placeholder_text = "Type out your issue"

[node name="HBoxContainer4" type="HBoxContainer" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2

[node name="BugEmailCheckbox" type="CheckBox" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/HBoxContainer4"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
size_flags_horizontal = 3
text = "Email Bug Report"
alignment = 1

[node name="BugSubmitButton" type="Button" parent="HelpScene/TabContainer/Bug Report/VBoxContainer/HBoxContainer4"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 10
size_flags_vertical = 8
text = "Submit"

[node name="BugErrorLabel" type="Label" parent="HelpScene/TabContainer/Bug Report/VBoxContainer"]
layout_mode = 2

[node name="Request Feature" type="MarginContainer" parent="HelpScene/TabContainer"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10
metadata/_tab_index = 1

[node name="VBoxContainer" type="VBoxContainer" parent="HelpScene/TabContainer/Request Feature"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="RequestID" type="LineEdit" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2
placeholder_text = "ID Number"
editable = false

[node name="HSeparator2" type="HSeparator" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2

[node name="RequestCategoryContainer" type="FlowContainer" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2

[node name="Button" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Visual Feature"

[node name="Button2" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Performance Feature"

[node name="Button3" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Data Request"

[node name="Button4" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "UX Feature"

[node name="Button5" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Suggestion"

[node name="Button6" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/RequestCategoryContainer"]
layout_mode = 2
theme_override_styles/hover_pressed = SubResource("StyleBoxFlat_yuu6e")
theme_override_styles/hover = SubResource("StyleBoxFlat_r8mba")
theme_override_styles/pressed = SubResource("StyleBoxFlat_lk1l7")
theme_override_styles/normal = SubResource("StyleBoxFlat_f3218")
toggle_mode = true
text = "Other"

[node name="HSeparator" type="HSeparator" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2

[node name="HBoxContainer2" type="HBoxContainer" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/HBoxContainer2"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "Choose Assistant:"

[node name="RequestAssistantSelection" type="OptionButton" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
selected = 1
item_count = 4
popup/item_0/text = "Assistant Suite"
popup/item_0/id = 3
popup/item_1/text = "Connector Assistant"
popup/item_1/id = 0
popup/item_2/text = "Inventory Assistant"
popup/item_2/id = 1
popup/item_3/text = "Tool Assistant"
popup/item_3/id = 2

[node name="RequestDescription" type="TextEdit" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
custom_minimum_size = Vector2(0, 100)
layout_mode = 2
size_flags_vertical = 3
placeholder_text = "Type here"

[node name="HBoxContainer3" type="HBoxContainer" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2

[node name="RequestEmailCheckbox" type="CheckBox" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/HBoxContainer3"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
size_flags_horizontal = 3
text = "Email Request"
alignment = 1

[node name="RequestSubmitButton" type="Button" parent="HelpScene/TabContainer/Request Feature/VBoxContainer/HBoxContainer3"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 10
size_flags_vertical = 8
text = "Submit"

[node name="RequestErrorLabel" type="Label" parent="HelpScene/TabContainer/Request Feature/VBoxContainer"]
layout_mode = 2
