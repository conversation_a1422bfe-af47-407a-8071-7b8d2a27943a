using Godot;
using System;
using System.Data;

public partial class MatchingBackshellPanel : PanelContainer
{
    [Export] public Tree BackshellTree;
    DataTable BackshellTable { get; set; }
    SQLHandler sqlHandler;
    CustomSignals customSignals;

    public override void _Ready()
    {
        ConnectSignals();
    }

    public void OnTreeEntered()
    {
        ConnectSignals();
    }

    private void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        BackshellTree.ButtonClicked += OnBackshellTreeButtonClicked;
    }

    public void PopulatePanel(string PartNumber)
    {
        if (sqlHandler == null)
        {
            GD.Print("SQLHandler is null");
        }
        BackshellTable = sqlHandler.Query(@$"-- Define the Connector PartNumber you want to search for
        DECLARE @InputConnectorPN VARCHAR(50);
        SET @InputConnectorPN = '{PartNumber}'; -- <<< REPLACE THIS VALUE

        WITH InputConnectorShellInfo AS ( SELECT p.PartNumber AS InputPartNumber, pas.value AS ShellSize FROM dbo.parts p JOIN dbo.part_attrib_str pas ON p.[index] = pas.part_index WHERE p.PartNumber = @InputConnectorPN AND pas.attrib_index = 30 -- Attribute index for 'Shell Size' AND pas.value IS NOT NULL AND LTRIM(RTRIM(pas.value)) != ''
        ),
        AllBackshellsWithShellSize AS (
            SELECT p.PartNumber AS BackshellPartNumber, pas.value AS ShellSize
            FROM dbo.parts p
            JOIN dbo.part_types pt ON p.[index] = pt.part_index
            JOIN dbo.part_attrib_str pas ON p.[index] = pas.part_index
            WHERE pt.type_index = 6 -- Type index for 'Backshell' AND pas.attrib_index = 30 -- Attribute index for 'Shell Size' AND pas.value IS NOT NULL AND LTRIM(RTRIM(pas.value)) != ''
        ),
        AllAttributes AS (
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS AttributeValue -- Cast value to a common type
            FROM part_attrib_str
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS AttributeValue
            FROM part_attrib_int
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS AttributeValue
            FROM part_attrib_float
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS AttributeValue
            FROM part_attrib_bit
        ),
        ChoiceSetLookups AS (
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)) AS [Value], display AS DisplayValue FROM csAttrib_str
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_int
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_float
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_bit
        )
        SELECT abws.BackshellPartNumber, MAX(CASE WHEN a.display_name = 'Description' THEN COALESCE(cs.DisplayValue, aa.AttributeValue) END) AS Description, MAX(CASE WHEN a.display_name = 'Manufacturer' THEN COALESCE(cs.DisplayValue, aa.AttributeValue) END) AS Manufacturer, MAX(CASE WHEN a.display_name = 'Angle' THEN COALESCE(cs.DisplayValue, aa.AttributeValue) END) AS Angle, MAX(CASE WHEN a.display_name = 'Shell Size' THEN COALESCE(cs.DisplayValue, aa.AttributeValue) END) AS ShellSize, CASE WHEN MAX(CASE WHEN a.display_name = 'Description' THEN COALESCE(cs.DisplayValue, aa.AttributeValue) END) LIKE '%EMI%' THEN 1 ELSE 0  END AS EMI
        FROM AllBackshellsWithShellSize abws
        INNER JOIN InputConnectorShellInfo ics ON LTRIM(RTRIM(abws.ShellSize)) = LTRIM(RTRIM(ics.ShellSize))
        LEFT JOIN dbo.parts p ON abws.BackshellPartNumber = p.PartNumber
        LEFT JOIN AllAttributes aa ON p.[index] = aa.part_index
        LEFT JOIN attributes a ON aa.attrib_index = a.[index]
        LEFT JOIN ChoiceSetLookups cs ON a.[index] = cs.attrib_index AND aa.AttributeValue = cs.[Value]
        WHERE ics.InputPartNumber IS NOT NULL
        GROUP BY abws.BackshellPartNumber, abws.ShellSize
        ORDER BY abws.BackshellPartNumber;");

        BackshellTree.Clear();

        BackshellTree.Columns = BackshellTable.Columns.Count;
        BackshellTree.ColumnTitlesVisible = true;
        TreeItem root = BackshellTree.CreateItem();
        BackshellTree.HideRoot = true;

        var buttonIcon = GD.Load<Texture2D>("res://Icons/ExportWhite.svg");

        BackshellTree.Columns = BackshellTable.Columns.Count + 1;

        BackshellTree.SetColumnTitle(0, "");
        BackshellTree.SetColumnCustomMinimumWidth(0, 50);
        BackshellTree.SetColumnExpand(0, false);

        foreach (DataColumn column in BackshellTable.Columns)
        {
            int columnIndex = BackshellTable.Columns.IndexOf(column) + 1;

            BackshellTree.SetColumnTitle(columnIndex, column.ColumnName);
            BackshellTree.SetColumnExpand(columnIndex, true);
            BackshellTree.SetColumnCustomMinimumWidth(columnIndex, 50);
        }

        foreach (DataRow row in BackshellTable.Rows)
        {
            TreeItem item = BackshellTree.CreateItem(root);

            item.AddButton(0, buttonIcon);

            item.SetText(1, row["BackshellPartNumber"].ToString());
            item.SetText(2, row["Description"].ToString());
            item.SetText(3, row["Manufacturer"].ToString());
            item.SetText(4, row["Angle"].ToString());
            item.SetText(5, row["ShellSize"].ToString());
            item.SetText(6, row["EMI"].ToString());

            item.SetTextAlignment(0, HorizontalAlignment.Left);
            item.SetTextAlignment(1, HorizontalAlignment.Left);
            item.SetTextAlignment(2, HorizontalAlignment.Center);
            item.SetTextAlignment(3, HorizontalAlignment.Center);
            item.SetTextAlignment(4, HorizontalAlignment.Center);
            item.SetTextAlignment(5, HorizontalAlignment.Center);
            item.SetTextAlignment(6, HorizontalAlignment.Center);
        }
    }

    private void OnBackshellTreeButtonClicked(TreeItem item, long column, long id, long mouseButtonIndex)
    {
        if (column == 0)
        {
            string partNumber = item.GetText(1);

            GD.Print($"Button clicked for PartNumber: {partNumber}");
            
            var popupWindow = new Window();
            PackedScene _itemPanelScene = GD.Load<PackedScene>("res://Scenes/ItemPanel.tscn");

            // 2. Configure the Window's properties
            popupWindow.Title = "Contact Details";
            popupWindow.InitialPosition = Window.WindowInitialPosition.CenterMainWindowScreen;
            popupWindow.Size = new Vector2I(900, 700); // Set a default size
            popupWindow.Unresizable = false; // Allow resizing if you want
            
            // IMPORTANT: Handle the close request to free the window from memory.
            // A lambda function is a clean way to do this inline.
            popupWindow.CloseRequested += () => popupWindow.QueueFree();

            // 3. Load and instance your existing ContactDetailsPanel scene
            ItemPanel panelInstance = _itemPanelScene.Instantiate<ItemPanel>();

            // 4. Add the panel as a child of the window
            popupWindow.AddChild(panelInstance);

            // 5. Add the completed window to the main scene so it becomes visible
            AddChild(popupWindow);

            // 6. Populate the panel with data and show the window
            panelInstance.PopulatePanel(partNumber);
            popupWindow.Show();
        }
    }
}

