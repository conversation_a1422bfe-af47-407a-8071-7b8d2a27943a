; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Assistant Suite"
run/main_scene="uid://cn5ih51qdlvpp"
config/features=PackedStringArray("4.4", "C#", "GL Compatibility")
boot_splash/bg_color=Color(0.117647, 0.12549, 0.137255, 1)
boot_splash/image="uid://0jx633fa6f6m"
config/icon="uid://bcud4si30xsl2"
boot_splash/minimum_display_time=200

[autoload]

CustomSignals="*res://Scripts/CustomSignals.cs"
SQLHandler="*res://Scripts/SQLHandler.cs"
ConnectorAssistantGlobalData="*res://Scripts/ConnectorAssistantGlobalData.cs"
GlobalFunctions="*res://Scripts/GlobalFunctions.cs"

[display]

window/subwindows/embed_subwindows=false

[dotnet]

project/assembly_name="New Game Project"

[input]

wheeldown={
"deadzone": 0.2,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":16,"position":Vector2(274, 44),"global_position":Vector2(288, 117),"factor":1.0,"button_index":5,"canceled":false,"pressed":true,"double_click":false,"script":null)
]
}
wheelup={
"deadzone": 0.2,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":8,"position":Vector2(231, 10),"global_position":Vector2(245, 83),"factor":1.0,"button_index":4,"canceled":false,"pressed":true,"double_click":false,"script":null)
]
}

[rendering]

renderer/rendering_method="gl_compatibility"
renderer/rendering_method.mobile="gl_compatibility"
