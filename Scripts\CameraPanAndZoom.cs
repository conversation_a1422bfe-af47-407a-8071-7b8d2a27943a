using Godot;
using System;

public partial class CameraPanAndZoom : Camera2D
{
    [ExportGroup("Controls")]
     [Export(PropertyHint.Range, "1.01,2.0,0.01")] public float zoomSpeed = 1.1f;
    [Export(PropertyHint.Range, "0.1,1.0,0.05")] public float minZoom = 0.2f;
    [Export(PropertyHint.Range, "1.0,10.0,0.5")] public float maxZoom = 5.0f;
    

    private bool isPanning = false;

    private bool _isPanning = false;
    private Vector2 _initialPanPosition;

    public override void _Input(InputEvent @event)
    {

        if (@event is InputEventMouseButton mouseButtonEvent)
        {
            if (mouseButtonEvent.IsPressed())
            {
                if (mouseButtonEvent.ButtonIndex == MouseButton.WheelDown || mouseButtonEvent.ButtonIndex == MouseButton.WheelUp)
                {
                    Vector2 pointBeforeZoom = GetCanvasTransform().AffineInverse() * mouseButtonEvent.Position;

                    Vector2 newZoom = Zoom;
                    if (mouseButtonEvent.ButtonIndex == MouseButton.WheelDown)
                    {
                        newZoom /= zoomSpeed;
                    }
                    else
                    {
                        newZoom *= zoomSpeed;
                    }
                    Zoom = newZoom.Clamp(new Vector2(minZoom, minZoom), new Vector2(maxZoom, maxZoom));
                    
                    Vector2 pointAfterZoom = GetCanvasTransform().AffineInverse() * mouseButtonEvent.Position;
                    
                    Position += pointBeforeZoom - pointAfterZoom;

                    GetViewport().SetInputAsHandled();
                    return;
                }
            }

            if (mouseButtonEvent.ButtonIndex == MouseButton.Middle)
            {
                isPanning = mouseButtonEvent.Pressed;
                Input.SetDefaultCursorShape(isPanning ? Input.CursorShape.Move : Input.CursorShape.Arrow);
                GetViewport().SetInputAsHandled();
            }
        }

        if (@event is InputEventMouseMotion mouseMotionEvent && isPanning)
        {
            Position -= mouseMotionEvent.Relative / Zoom;
            GetViewport().SetInputAsHandled();
        }
    }
}
