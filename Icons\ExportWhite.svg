<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20.26 13.41">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: #d1d2d4;
        stroke-linecap: round;
        stroke-linejoin: round;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer_1">
    <g>
      <path class="cls-1" d="M6.1,6.71h13.66S13.56.5,13.56.5M19.76,6.71l-6.21,6.21"/>
      <polyline class="cls-1" points="6.1 12.91 .5 12.91 .5 .5 6.1 .5"/>
    </g>
  </g>
</svg>