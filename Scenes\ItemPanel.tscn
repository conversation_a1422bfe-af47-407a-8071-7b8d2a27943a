[gd_scene load_steps=24 format=3 uid="uid://d1kn6a5tc6nyf"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_co6bh"]
[ext_resource type="Script" uid="uid://cnh0el5rmqf6r" path="res://Scripts/ItemPanel.cs" id="1_x3kt4"]
[ext_resource type="PackedScene" uid="uid://y5s7u6crukcd" path="res://Scenes/InsertBuilder.tscn" id="3_025si"]
[ext_resource type="PackedScene" uid="uid://cr0t1b540fj8b" path="res://Scenes/InventoryCheckPanel.tscn" id="3_co6bh"]
[ext_resource type="Texture2D" uid="uid://nkdrj3hkuxp5" path="res://Icons/ExportWhite.svg" id="3_sqgtj"]
[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="4_8y2xj"]
[ext_resource type="Texture2D" uid="uid://bxfit3wxmgp0e" path="res://Icons/ExitWhite.svg" id="4_fidqg"]
[ext_resource type="Script" uid="uid://dlvabx3giecx3" path="res://Scripts/ContactsPanel.cs" id="5_2nb4b"]
[ext_resource type="Script" uid="uid://byhclffhwwqgt" path="res://Scripts/MatchingBackshellPanel.cs" id="5_byq5g"]
[ext_resource type="Texture2D" uid="uid://b8yoeayg6dh2h" path="res://Icons/Expand.svg" id="8_fidqg"]
[ext_resource type="Texture2D" uid="uid://dsxwipstyp22a" path="res://Icons/Reset.svg" id="8_rrtsw"]
[ext_resource type="Script" uid="uid://clwhk1tx1kwrq" path="res://Scripts/ItemTreePanel.cs" id="9_vd0ui"]
[ext_resource type="PackedScene" uid="uid://dux4y7u2xjgkj" path="res://Scenes/ContactDetailsPanel.tscn" id="11_sqgtj"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2nb4b"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8y2xj"]
content_margin_left = 15.0
content_margin_top = 5.0
content_margin_right = 15.0
content_margin_bottom = 5.0
bg_color = Color(0.180392, 0.458824, 0.745098, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_78bf3"]
content_margin_left = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_byq5g"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_sqgtj"]
content_margin_left = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_uypgd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ogq5r"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ngmgf"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_vd0ui"]
content_margin_left = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_fidqg"]
content_margin_left = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="ItemPanel" type="PanelContainer" node_paths=PackedStringArray("DetailsContainer", "InsertContainer", "ContactsContainer", "ContactDetailsContainer", "MatchingBackshellsContainer", "DetailsVbox", "InsertBuilder", "ContactsPanel", "MatchingBackshellPanel", "PartNumberLine", "TotalContactsLine", "PopoutButton", "CloseButton", "progressBar", "scrollContainer", "ScrollTimer", "ExpandButton", "InsertResetButton", "InsertViewportContainer")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2
theme = ExtResource("1_co6bh")
theme_override_styles/panel = SubResource("StyleBoxFlat_2nb4b")
script = ExtResource("1_x3kt4")
DetailsContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/DetailsContainer")
InsertContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer")
ContactsContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer")
ContactDetailsContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactDetailsPanel")
MatchingBackshellsContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/MatchingBackshellsContainer")
DetailsVbox = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/DetailsContainer/VBoxContainer/PanelContainer/DetailsVBox")
InsertBuilder = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/PanelContainer/SubViewportContainer/SubViewport/InsertBuilder")
ContactsPanel = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer")
MatchingBackshellPanel = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/MatchingBackshellsContainer/VBoxContainer/MatchingBackshellPanel")
PartNumberLine = NodePath("MarginContainer/VBoxContainer/MarginContainer/HBoxContainer/PartNumberLine")
TotalContactsLine = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer/HBoxContainer/TotalContactsLine")
PopoutButton = NodePath("MarginContainer/VBoxContainer/MarginContainer/HBoxContainer/PopoutButton")
CloseButton = NodePath("MarginContainer/VBoxContainer/MarginContainer/HBoxContainer/CloseButton")
progressBar = NodePath("MarginContainer/VBoxContainer/StatusBar/MarginContainer/DatabaseProgressBar")
scrollContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer")
ScrollTimer = NodePath("ScrollTimer")
ExpandButton = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/MarginContainer/HBoxContainer/ExpandButton")
InsertResetButton = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/MarginContainer/HBoxContainer/ResetButton")
InsertViewportContainer = NodePath("MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/PanelContainer/SubViewportContainer")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 0
theme_override_constants/margin_top = 0
theme_override_constants/margin_right = 0
theme_override_constants/margin_bottom = 0

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="CenterContainer" type="CenterContainer" parent="MarginContainer/VBoxContainer/MarginContainer"]
layout_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer/MarginContainer"]
layout_mode = 2

[node name="PopoutButton" type="Button" parent="MarginContainer/VBoxContainer/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
tooltip_text = "Popout Panel"
icon = ExtResource("3_sqgtj")
icon_alignment = 1

[node name="PartNumberLine" type="LineEdit" parent="MarginContainer/VBoxContainer/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 0
theme_override_colors/font_uneditable_color = Color(0.906493, 0.906493, 0.906493, 1)
theme_override_font_sizes/font_size = 20
theme_override_styles/read_only = SubResource("StyleBoxFlat_8y2xj")
placeholder_text = "Part Number"
alignment = 1
editable = false
expand_to_text_length = true

[node name="CloseButton" type="Button" parent="MarginContainer/VBoxContainer/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(50, 50)
layout_mode = 2
tooltip_text = "Close Window"
icon = ExtResource("4_fidqg")
icon_alignment = 1

[node name="ConnectorContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 0

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer"]
layout_mode = 2
horizontal_scroll_mode = 0

[node name="VBoxContainer2" type="FlowContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer"]
clip_contents = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
last_wrap_alignment = 1

[node name="DetailsContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_78bf3")

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/DetailsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/DetailsContainer/VBoxContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
size_flags_vertical = 0
text = "Details"
horizontal_alignment = 1

[node name="PanelContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/DetailsContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_byq5g")

[node name="DetailsVBox" type="FlowContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/DetailsContainer/VBoxContainer/PanelContainer"]
custom_minimum_size = Vector2(0, 500)
layout_mode = 2
vertical = true

[node name="ContactsContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2" node_paths=PackedStringArray("contactTree")]
visible = false
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_sqgtj")
script = ExtResource("5_2nb4b")
contactTree = NodePath("VBoxContainer/ContactsPanel/Tree")

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer"]
custom_minimum_size = Vector2(0, 182.37)
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 5

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
size_flags_vertical = 0
text = "Contacts"
horizontal_alignment = 1

[node name="ContactsPanel" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2
theme = ExtResource("4_8y2xj")
theme_override_styles/panel = SubResource("StyleBoxFlat_byq5g")

[node name="Tree" type="Tree" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer/ContactsPanel"]
layout_mode = 2
theme_override_styles/title_button_hover = SubResource("StyleBoxEmpty_uypgd")
theme_override_styles/title_button_pressed = SubResource("StyleBoxEmpty_ogq5r")
theme_override_styles/title_button_normal = SubResource("StyleBoxEmpty_ngmgf")
select_mode = 1
scroll_horizontal_enabled = false
script = ExtResource("9_vd0ui")

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer"]
layout_mode = 2

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
text = "Total Contacts"

[node name="TotalContactsLine" type="LineEdit" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/ContactsContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_styles/read_only = SubResource("StyleBoxFlat_byq5g")
editable = false

[node name="InsertContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_vd0ui")

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer"]
custom_minimum_size = Vector2(0, 182.37)
layout_mode = 2
size_flags_horizontal = 3

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/margin_left = 2
theme_override_constants/margin_top = 2
theme_override_constants/margin_right = 2
theme_override_constants/margin_bottom = 2

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/MarginContainer"]
layout_mode = 2

[node name="ResetButton" type="Button" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/MarginContainer/HBoxContainer"]
layout_mode = 2
icon = ExtResource("8_rrtsw")
flat = true

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Insert Arrangement"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ExpandButton" type="Button" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/MarginContainer/HBoxContainer"]
layout_mode = 2
icon = ExtResource("8_fidqg")
flat = true

[node name="PanelContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_byq5g")

[node name="SubViewportContainer" type="SubViewportContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/PanelContainer"]
custom_minimum_size = Vector2(400, 400)
layout_mode = 2
size_flags_horizontal = 3
stretch = true

[node name="SubViewport" type="SubViewport" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/PanelContainer/SubViewportContainer"]
transparent_bg = true
handle_input_locally = false
size = Vector2i(400, 400)
render_target_update_mode = 4

[node name="InsertBuilder" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/InsertContainer/HBoxContainer/VBoxContainer/PanelContainer/SubViewportContainer/SubViewport" instance=ExtResource("3_025si")]
mouse_force_pass_scroll_events = false

[node name="MatchingBackshellsContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2"]
visible = false
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_fidqg")

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/MatchingBackshellsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/MatchingBackshellsContainer/VBoxContainer"]
layout_mode = 2
text = "Matching Backshells"
horizontal_alignment = 1

[node name="MatchingBackshellPanel" type="PanelContainer" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/MatchingBackshellsContainer/VBoxContainer" node_paths=PackedStringArray("BackshellTree")]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_byq5g")
script = ExtResource("5_byq5g")
BackshellTree = NodePath("Tree")

[node name="Tree" type="Tree" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2/MatchingBackshellsContainer/VBoxContainer/MatchingBackshellPanel"]
custom_minimum_size = Vector2(300, 250)
layout_mode = 2
mouse_force_pass_scroll_events = false
select_mode = 1
scroll_horizontal_enabled = false
script = ExtResource("9_vd0ui")

[node name="ContactDetailsPanel" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2" instance=ExtResource("11_sqgtj")]
visible = false
layout_mode = 2

[node name="InventoryCheckPanel" parent="MarginContainer/VBoxContainer/ConnectorContainer/ScrollContainer/VBoxContainer2" instance=ExtResource("3_co6bh")]
visible = false
layout_mode = 2
size_flags_vertical = 1

[node name="StatusBar" type="PanelContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 8

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer/StatusBar"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="DatabaseProgressBar" type="ProgressBar" parent="MarginContainer/VBoxContainer/StatusBar/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
show_percentage = false

[node name="ScrollTimer" type="Timer" parent="."]
wait_time = 0.5
