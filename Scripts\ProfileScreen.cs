using Global;
using Godot;
using System;

public partial class ProfileScreen : PanelContainer
{
    [Export] public LineEdit UsernameLine;
    [Export] public LineEdit AccountTypeLine;
    [Export] public Button RequestElevatedAccessButton;

    ConnectorAssistantGlobalData globalData;
    SQLHandler sqlHandler;

    public override void _Ready()
    {
        ConnectSignals();
        UsernameLine.Text = globalData.UserName;
        GetAccountType();
    }
    private void ConnectSignals()
    {
        globalData = GetNode<ConnectorAssistantGlobalData>("/root/ConnectorAssistantGlobalData");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        RequestElevatedAccessButton.Pressed += OnRequestElevatedAccessButtonPressed;
    }

    private void OnRequestElevatedAccessButtonPressed()
    {
        GlobalFunctions.Email("<EMAIL>", "Request Connector Assistant Elevated Access", "");
    }

    private void GetAccountType()
    {
        try
        {
            bool isReader = sqlHandler.SysTestQuery("SELECT IS_MEMBER('db_datareader')") == "1";
            bool isWriter = sqlHandler.SysTestQuery("SELECT IS_MEMBER('db_datawriter')") == "1";
            bool isAdmin = sqlHandler.SysTestQuery("SELECT IS_MEMBER('db_owner')") == "1";

            if (isAdmin)
            {
                AccountTypeLine.Text = "Administrator";
            }
            else if (isWriter && !isAdmin)
            {
                AccountTypeLine.Text = "Read/Write Access";
            }
            else if (isReader && !isAdmin && !isWriter)
            {
                AccountTypeLine.Text = "Read Only Access";
            }
            else
            {
                AccountTypeLine.Text = "None";
            }
        }
        catch
        {
            AccountTypeLine.Text = "Error, Not in Active Directory Group for Database";
        }
    }
}
