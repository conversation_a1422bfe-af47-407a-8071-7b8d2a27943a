using Godot;
using System;

public partial class ToolAssistantWireGroupSegment : PanelContainer
{
    public float WireDiameter { get; set; }
    public int Quantity { get; set; }
    public float CrossSectionArea { get; set; }
    [Export] public LineEdit WireDiameterLine { get; set; }
    [Export] public LineEdit QuantityLine { get; set; }
    [Export] public Button ExitButton { get; set; }
    [Export] public ColorPickerButton ColorButton { get; set; }
    public PanelContainer BundleCalculatorReference { get; set; }

    public override void _Ready()
    {
        ConnectSignals();
    }

    public void ConnectSignals()
    {
        WireDiameterLine.TextChanged += OnWireDiameterLineTextChanged;
        QuantityLine.TextChanged += OnQuantityLineTextChanged;
        ExitButton.Pressed += OnExitButtonPressed;
    }

    private void OnExitButtonPressed()
    {
        QueueFree();
    }


    private void OnWireDiameterLineTextChanged(string newText)
    {
        WireDiameter = float.Parse(newText);
    }


    private void OnQuantityLineTextChanged(string newText)
    {
        Quantity = int.Parse(newText);
    }

}
