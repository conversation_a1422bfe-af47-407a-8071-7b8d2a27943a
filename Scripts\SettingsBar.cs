using Godot;
using System;

public partial class SettingsBar : MarginContainer
{
    [Export]
    public Button ProfileButton;
    [Export]
    public Button SettingsButton;
    [Export]
    public Button HelpButton;
    public CustomSignals customSignals;

    public override void _Ready()
    {
        ConnectSignals();
    }

    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        ProfileButton.Pressed += OnProfileButtonPressed;
        // SettingsButton.Pressed += OnSettingsButtonPressed;
        HelpButton.Pressed += OnHelpButtonPressed;
    }

    private void OnProfileButtonPressed()
    {
        GD.Print("Profile button pressed");
        PackedScene ProfileScenePacked = GD.Load<PackedScene>("res://Scenes/ProfileScene.tscn");
        var profileSceneInstance = ProfileScenePacked.Instantiate();
        AddChild(profileSceneInstance);
    }

    private void OnSettingsButtonPressed()
    {
        GD.Print("Settings button pressed");
        PackedScene SettingsScenePacked = GD.Load<PackedScene>("res://Scenes/SettingsScene.tscn");
        var settingsSceneInstance = SettingsScenePacked.Instantiate();
        AddChild(settingsSceneInstance);
    }

    private void OnHelpButtonPressed()
    {
        GD.Print("Help button pressed");
        PackedScene HelpScenePacked = GD.Load<PackedScene>("res://Scenes/HelpScene.tscn");
        var helpSceneInstance = HelpScenePacked.Instantiate();
        AddChild(helpSceneInstance);
    }
}
