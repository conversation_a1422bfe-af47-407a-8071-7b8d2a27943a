/*
================================================================================================
Godot Tree Column Resizer & Sorter - C# Script

Instructions:
1. Attach this script directly to a Tree node in your Godot scene.
2. In the Inspector, you can adjust the resize and color properties.

This script enables column resizing with the mouse and allows sorting by clicking
on the column headers.
================================================================================================
*/
using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class ItemTreePanel : Tree
{
    // --- Resize Properties ---
    [ExportGroup("Resizing")]
    [Export]
    public float ResizeSensitivity { get; set; } = 15.0f;
    [Export]
    public Color ResizeLineColor { get; set; } = new Color(0.5f, 0.7f, 1.0f, 0.8f);
    [Export]
    public Color HoverLineColor { get; set; } = new Color(0.7f, 0.7f, 0.7f, 0.6f);
    [Export]
    public float MinColumnWidth { get; set; } = 20.0f;

    private bool _isResizing = false;
    private int _resizingColumn = -1;
    private int _hoveringColumn = -1;
    private float _headerHeight = -1;

    // --- Sorting Properties ---
    private int _sortColumn = -1;
    private bool _sortAscending = true;
    private string[] _originalColumnTitles;

    public override void _Ready()
    {
        // Connect signals for both resizing and sorting.
        MouseExited += OnMouseExited;
        ColumnTitleClicked += OnColumnTitleClicked;

        // Store the original column titles for sorting indicators.
        // This assumes columns are set before this node is ready.
        // If you add columns dynamically, you may need to call CacheColumnTitles() manually.
        CacheColumnTitles();
    }

    /// <summary>
    /// Stores the initial state of column titles before any sorting indicators are added.
    /// </summary>
    public void CacheColumnTitles()
    {
        if (Columns <= 0) return;
        _originalColumnTitles = new string[Columns];
        for (int i = 0; i < Columns; i++)
        {
            _originalColumnTitles[i] = GetColumnTitle(i);
        }
    }

    // --- Drawing Logic ---
    public override void _Draw()
    {
        // This method is called by the engine to draw custom graphics for resizing.
        if (_isResizing && _resizingColumn != -1)
        {
            var mousePos = GetLocalMousePosition();
            float columnStartX = GetColumnOffset(_resizingColumn);
            float drawX = Mathf.Max(mousePos.X, columnStartX + MinColumnWidth);
            DrawLine(new Vector2(drawX, 0), new Vector2(drawX, Size.Y), ResizeLineColor, 2.0f);
        }
        else if (_hoveringColumn != -1)
        {
            float lineX = GetColumnOffset(_hoveringColumn) + GetColumnWidth(_hoveringColumn);
            DrawLine(new Vector2(lineX, 0), new Vector2(lineX, Size.Y), HoverLineColor, 1.0f);
        }
    }

    // --- Input Handling ---
    public override void _GuiInput(InputEvent @event)
    {
        bool eventHandled = HandleResizeLogic(@event);
        if (!eventHandled)
        {
            base._GuiInput(@event);
        }
    }

    private void OnMouseExited()
    {
        if (_hoveringColumn != -1)
        {
            _hoveringColumn = -1;
            QueueRedraw();
        }
        MouseDefaultCursorShape = CursorShape.Arrow;
    }

    // --- Sorting Implementation ---
    private void OnColumnTitleClicked(long columnIndex, long mouseButtonIndex)
    {
        if (mouseButtonIndex != (long)MouseButton.Left) return;

        int col = (int)columnIndex;
        if (_sortColumn == col)
        {
            // If clicking the same column, reverse the sort order.
            _sortAscending = !_sortAscending;
        }
        else
        {
            // If clicking a new column, set it as the sort column and default to ascending.
            _sortColumn = col;
            _sortAscending = true;
        }

        UpdateColumnTitlesWithSortIndicator();
        SortTree();
    }

    private void UpdateColumnTitlesWithSortIndicator()
    {
        if (_originalColumnTitles == null || _originalColumnTitles.Length != Columns)
        {
            CacheColumnTitles();
        }

        // Reset all titles to their original state.
        for (int i = 0; i < Columns; i++)
        {
            if (_originalColumnTitles != null && i < _originalColumnTitles.Length)
            {
                SetColumnTitle(i, _originalColumnTitles[i]);
            }
        }

        // Add the indicator to the currently sorted column.
        if (_sortColumn != -1 && _originalColumnTitles != null && _sortColumn < _originalColumnTitles.Length)
        {
            // CORRECTED: Down arrow for ascending (A-Z), Up arrow for descending.
            string arrow = _sortAscending ? " 🡓" : " 🡑";
            SetColumnTitle(_sortColumn, _originalColumnTitles[_sortColumn] + arrow);
        }
    }

    private void SortTree()
    {
        var root = GetRoot();
        if (root == null || root.GetChildCount() == 0)
        {
            return;
        }

        // Get all top-level items into a list.
        var items = root.GetChildren().ToList();

        // Sort the list using a custom comparison function.
        items.Sort((a, b) =>
        {
            string textA = a.GetText(_sortColumn);
            string textB = b.GetText(_sortColumn);

            // Attempt to compare as numbers first.
            bool isNumericA = double.TryParse(textA, out double numA);
            bool isNumericB = double.TryParse(textB, out double numB);

            int compareResult;
            if (isNumericA && isNumericB)
            {
                compareResult = numA.CompareTo(numB);
            }
            else
            {
                // Fallback to string comparison.
                compareResult = string.Compare(textA, textB, StringComparison.OrdinalIgnoreCase);
            }

            // Apply ascending/descending order.
            return _sortAscending ? compareResult : -compareResult;
        });

        // Re-order the items in the actual Tree node using MoveAfter.
        for (int i = 1; i < items.Count; i++)
        {
            items[i].MoveAfter(items[i - 1]);
        }
    }


    // --- Resizing Implementation ---
    private bool HandleResizeLogic(InputEvent @event)
    {
        if (@event is InputEventMouseButton mouseButtonEvent)
        {
            if (mouseButtonEvent.ButtonIndex == MouseButton.Left)
            {
                if (mouseButtonEvent.Pressed)
                {
                    int column = GetColumnAtResizePosition(mouseButtonEvent.Position);
                    if (column != -1)
                    {
                        _isResizing = true;
                        _resizingColumn = column;
                        _hoveringColumn = -1;
                        QueueRedraw();
                        GetViewport().SetInputAsHandled();
                        return true;
                    }
                }
                else
                {
                    if (_isResizing)
                    {
                        _isResizing = false;
                        _resizingColumn = -1;
                        QueueRedraw();
                        return true;
                    }
                }
            }
        }

        if (@event is InputEventMouseMotion mouseMotionEvent)
        {
            if (_isResizing)
            {
                float columnStartX = GetColumnOffset(_resizingColumn);
                float newWidth = Mathf.Max(mouseMotionEvent.Position.X - columnStartX, MinColumnWidth);
                SetColumnCustomMinimumWidth(_resizingColumn, (int)newWidth);
                QueueRedraw();
                GetViewport().SetInputAsHandled();
                return true;
            }
            else
            {
                int newHoverColumn = GetColumnAtResizePosition(mouseMotionEvent.Position);
                if (newHoverColumn != _hoveringColumn)
                {
                    _hoveringColumn = newHoverColumn;
                    QueueRedraw();
                }

                MouseDefaultCursorShape = (_hoveringColumn != -1) ? CursorShape.Hsplit : CursorShape.Arrow;
                if (_hoveringColumn != -1)
                {
                    return true;
                }
            }
        }

        return false;
    }

    private int GetColumnAtResizePosition(Vector2 position)
    {
        if (position.Y > GetHeaderHeight())
        {
            return -1;
        }
        float currentX = 0;
        for (int i = 0; i < Columns; i++)
        {
            currentX += GetColumnWidth(i);
            if (Mathf.Abs(position.X - currentX) < ResizeSensitivity)
            {
                if (i < Columns - 1)
                {
                    return i;
                }
            }
        }
        return -1;
    }

    private float GetColumnOffset(int column)
    {
        float offset = 0;
        for (int i = 0; i < column; i++)
        {
            offset += GetColumnWidth(i);
        }
        return offset;
    }
    
    private float GetHeaderHeight()
    {
        if (_headerHeight < 0) // Calculate only once
        {
            // Get all the theme properties that contribute to the vertical size
            var font = GetThemeFont("title_button_font");
            int fontSize = GetThemeFontSize("title_button_font_size");
            var style = GetThemeStylebox("title_button_normal");

            // The height is the font's height plus the top/bottom margins of the stylebox
            _headerHeight = font.GetHeight(fontSize) + style.GetMinimumSize().Y;
        }
        return _headerHeight;
    }
}
