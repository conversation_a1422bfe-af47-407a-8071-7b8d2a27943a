using Godot;
using Microsoft.VisualBasic;
using System;

public partial class AssistantSuiteMain : PanelContainer
{
    [Export]
    public Button ConnectorAssistantButton { get; set; }
    [Export]
    public Button InventoryAssistantButton { get; set; }
    [Export]
    public Button ToolsAssistantButton { get; set; }
    public CustomSignals customSignals { get; set; }
    public Main main { get; set; }
    public override void _Ready()
    {
        ConnectSignals();
    }
    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        ConnectorAssistantButton.Pressed += OnConnectorAssistantButtonPressed;
        InventoryAssistantButton.Pressed += OnInventoryAssistantButtonPressed;
        ToolsAssistantButton.Pressed += OnToolsAssistantButtonPressed;
        ConnectorAssistantButton.MouseEntered += OnMouseEnteredConnectorAssistantButton;
        InventoryAssistantButton.MouseEntered += OnMouseEnteredInventoryAssistantButton;
        ToolsAssistantButton.MouseEntered += OnMouseEnteredToolsAssistantButton;
        ConnectorAssistantButton.MouseExited += OnMouseExitedConnectorAssistantButton;
        InventoryAssistantButton.MouseExited += OnMouseExitedInventoryAssistantButton;
        ToolsAssistantButton.MouseExited += OnMouseExitedToolsAssistantButton;
    }
    private void OnConnectorAssistantButtonPressed()
    {
        GD.Print("Connector Assistant button pressed");
        customSignals.EmitSignal("AppSelected", "Connector Assistant");
    }
    private void OnInventoryAssistantButtonPressed()
    {
        GD.Print("Inventory Assistant button pressed");
        customSignals.EmitSignal("AppSelected", "Inventory Assistant");
    }
    private void OnToolsAssistantButtonPressed()
    {
        GD.Print("Tools Assistant button pressed");
        customSignals.EmitSignal("AppSelected", "Tool Assistant");
    }
    private void TweenButtonUp(Button button)
    {
        Tween tween = GetTree().CreateTween();
        tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(button, "custom_minimum_size", new Vector2(200, 200), 0.5f);
    }
    private void TweenButtonDown(Button button)
    {
        Tween tween = GetTree().CreateTween();
        tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(button, "custom_minimum_size", new Vector2(200, 150), 0.5f);

    }
    private void OnMouseEnteredConnectorAssistantButton()
    {
        TweenButtonUp(ConnectorAssistantButton);
    }
    private void OnMouseEnteredInventoryAssistantButton()
    {
        TweenButtonUp(InventoryAssistantButton);
    }
    private void OnMouseEnteredToolsAssistantButton()
    {
        TweenButtonUp(ToolsAssistantButton);
    }
    private void OnMouseExitedConnectorAssistantButton()
    {
        TweenButtonDown(ConnectorAssistantButton);
    }
    private void OnMouseExitedInventoryAssistantButton()
    {
        TweenButtonDown(InventoryAssistantButton);
    }
    private void OnMouseExitedToolsAssistantButton()
    {
        TweenButtonDown(ToolsAssistantButton);
    }
}
