using Godot;
using Microsoft.Data.SqlClient;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;

public partial class ItemPanel : PanelContainer
{
    [Signal] public delegate void ContactDetailRequestedEventHandler(string partNumber);
    [Export] public PanelContainer DetailsContainer { get; set; }
    [Export] public PanelContainer InsertContainer { get; set; }
    [Export] public PanelContainer ContactsContainer { get; set; }
    [Export] public ContactDetailsPanel ContactDetailsContainer { get; set; }
    [Export] public PanelContainer MatchingBackshellsContainer { get; set; }
    [Export] public FlowContainer DetailsVbox { get; set; }
    [Export] public InsertBuilder InsertBuilder { get; set; }
    [Export] public ContactsPanel ContactsPanel { get; set; }
    [Export] public MatchingBackshellPanel MatchingBackshellPanel { get; set; }
    [Export] public LineEdit PartNumberLine { get; set; }
    [Export] public LineEdit TotalContactsLine { get; set; }
    [Export] public Button PopoutButton { get; set; }
    [Export] public Button CloseButton { get; set; }
    [Export] public ProgressBar progressBar { get; set; }
    [Export] public ScrollContainer scrollContainer { get; set; }
    [Export] public Timer ScrollTimer { get; set; }
    [Export] public Button ExpandButton { get; set; }
    [Export] public Button InsertResetButton { get; set; }
    [Export] public SubViewportContainer InsertViewportContainer { get; set; }
    public CustomSignals customSignals { get; set; }
    public SQLHandler sqlHandler { get; set; }
    public DataTable ItemTable { get; set; }
    public Button DatasheetButton { get; set; }
    public Button CopyDescriptionButton { get; set; }
    public string DatasheetPath { get; set; }
    public string Description { get; set; }
    public override void _Ready()
    {
        ConnectSignals();
    }

    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        PopoutButton.Pressed += () => OnPopoutButtonClicked(PartNumberLine.Text);
        CloseButton.Pressed += () => OnCloseButtonClicked();
        scrollContainer.GuiInput += OnScrollInput;
        ScrollTimer.Timeout += ScrollTimerTimeout;
        ExpandButton.Pressed += OnExpandButtonClicked;
        InsertResetButton.Pressed += OnInsertResetButtonClicked;
    }

    private void OnInsertResetButtonClicked()
    {
        InsertBuilder.camera.Zoom = new Vector2(1, 1);
        InsertBuilder.camera.Position = new Vector2(0, 0);
    }


    private void OnExpandButtonClicked()
    {
        Texture2D ExpandIcon = GD.Load<Texture2D>("res://Icons/Expand.svg");
        Texture2D CollapseIcon = GD.Load<Texture2D>("res://Icons/Collapse.svg");

        if (InsertViewportContainer.CustomMinimumSize == new Vector2(600,600))
        {
            InsertViewportContainer.CustomMinimumSize = new Vector2(400,400);
            ExpandButton.Icon = ExpandIcon;
            return;
        }
        else
        {
            InsertViewportContainer.CustomMinimumSize = new Vector2(600,600);
            ExpandButton.Icon = CollapseIcon;
            return;
        }
    }


    private void ScrollTimerTimeout()
    {
        InsertBuilder.MouseFilter = MouseFilterEnum.Stop;
        MatchingBackshellPanel.BackshellTree.MouseFilter = MouseFilterEnum.Stop;
    }


    private void OnScrollInput(InputEvent @event)
    {
        if (@event is InputEventMouseButton mouseButtonEvent)
        {
            if (mouseButtonEvent.ButtonIndex == MouseButton.WheelDown || mouseButtonEvent.ButtonIndex == MouseButton.WheelUp)
            {
                InsertBuilder.MouseFilter = MouseFilterEnum.Ignore;
                MatchingBackshellPanel.BackshellTree.MouseFilter = MouseFilterEnum.Ignore;
                ScrollTimer.Start();
            }
        }
    }


    private void OnCloseButtonClicked()
    {
        if (GetParent() is Window window)
        {
            window.QueueFree();
        }
        else
        {
            Visible = false;
        }
    }


    public async void PopulatePanel(string PartNumber)
    {
        MouseFilter = MouseFilterEnum.Ignore;
        MouseDefaultCursorShape = CursorShape.Wait;
        PartNumberLine.Text = PartNumber;

        await ToSignal(GetTree().CreateTimer(0.0001), "timeout");

        ItemTable = sqlHandler.Query(@$"
        WITH AllAttributes AS (
            SELECT part_index, attrib_index, CAST(value AS VARCHAR(MAX)) AS AttributeValue -- Cast value to a common type
            FROM part_attrib_str
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS VARCHAR(MAX)) AS AttributeValue
            FROM part_attrib_int
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS VARCHAR(MAX)) AS AttributeValue
            FROM part_attrib_float
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS VARCHAR(MAX)) AS AttributeValue
            FROM part_attrib_bit
        ),
        ChoiceSetLookups AS (
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)) AS [Value], display AS DisplayValue FROM csAttrib_str
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_int
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_float
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_bit
        )
        SELECT p.PartNumber, t.type AS PartType, a.display_name AS AttributeName, STRING_AGG(CASE WHEN cs.DisplayValue IS NOT NULL THEN cs.DisplayValue ELSE aa.AttributeValue END, ', ') AS AttributeValue
        FROM parts AS p
        LEFT JOIN part_types AS pt ON p.[index] = pt.part_index
        LEFT JOIN types AS t ON pt.type_index = t.[index]
        LEFT JOIN AllAttributes AS aa ON p.[index] = aa.part_index
        LEFT JOIN attributes AS a ON aa.attrib_index = a.[index]
        LEFT JOIN ChoiceSetLookups AS cs ON a.[index] = cs.attrib_index AND aa.AttributeValue = cs.[Value]

        WHERE p.PartNumber = '{PartNumber}'
        GROUP BY p.PartNumber, t.type, a.display_name");

        if (ItemTable.Rows.Count == 0)
        {
            GD.PrintErr($"No data found for part number: {PartNumber}");
            SwitchType("No Data", PartNumber);
            DetailsContainer.Hide();
            MouseDefaultCursorShape = CursorShape.Arrow;
            return; // Exit early if no data
        }

        progressBar.MaxValue = ItemTable.Rows.Count;

        foreach (var child in DetailsVbox.GetChildren())
        {
            child.QueueFree();
        }

        string type = ItemTable.Rows[0]["PartType"].ToString();

        SwitchType(type, PartNumber);

        foreach (DataRow row in ItemTable.Rows)
        {
            progressBar.Value++;
            HBoxContainer hBox = new HBoxContainer();
            hBox.Name = row["AttributeName"].ToString();

            if (row["AttributeName"].ToString() == "Number of Contacts")
            {
                TotalContactsLine.Text = $"{row["AttributeValue"]}";
            }



            if (row["AttributeName"].ToString() == "Datasheet Location")
            {
                DatasheetButton = new Button();
                DatasheetPath = row["AttributeValue"].ToString();
                DatasheetButton.Text = "Open Datasheet";
                DatasheetButton.CustomMinimumSize = new Vector2(50, 0);
                DatasheetButton.Connect("pressed", new Callable(this, "OpenDatasheet"));
                hBox.AddChild(DatasheetButton);
            }
            else if (row["AttributeName"].ToString() == "Description")
            {
                RichTextLabel attributeValue = new RichTextLabel();
                attributeValue.FitContent = true;
                attributeValue.CustomMinimumSize = new Vector2(250, 0);
                attributeValue.Text = $"{row["AttributeValue"]}";
                hBox.AddChild(attributeValue);

                Description = row["AttributeValue"].ToString();
                CopyDescriptionButton = new Button();
                CopyDescriptionButton.Text = "Copy";
                CopyDescriptionButton.Connect("pressed", new Callable(this, "CopyDescription"));
                hBox.AddChild(CopyDescriptionButton);

            }
            else
            {

                LineEdit attributeValue = new LineEdit();
                attributeValue.ExpandToTextLength = true;
                attributeValue.CustomMinimumSize = new Vector2(50, 0);
                if (row["AttributeName"].ToString() == "Connector / Backshell Torque")
                {
                    attributeValue.Text = $"Torque: {row["AttributeValue"]}";
                }
                else
                {
                    attributeValue.Text = $"{row["AttributeName"]}: {row["AttributeValue"]}";
                }

                hBox.AddChild(attributeValue);
            }

            DetailsVbox.AddChild(hBox);

            if (hBox.Name == "Description")
            {
                DetailsVbox.MoveChild(hBox, 0);
            }

            await ToSignal(GetTree().CreateTimer(0.0001), "timeout");
        }
        MouseFilter = MouseFilterEnum.Stop;
        MouseDefaultCursorShape = CursorShape.Arrow;
    }

    private void OpenDatasheet()
    {
        OS.ShellOpen(DatasheetPath);
    }

    private void CopyDescription()
    {
        DisplayServer.ClipboardSet(Description);
    }

    private void SwitchType(string type, string partNumber = "")
    {
        GD.Print(type);

        switch (type)
        {
            case "Connector":
                ContactDetailsContainer.Hide();
                InsertContainer.Show();
                ContactsContainer.Show();
                MatchingBackshellsContainer.Show();

                ContactsPanel.PopulatePanel(partNumber);
                MatchingBackshellPanel.PopulatePanel(partNumber);
                InsertBuilder.count = 0;
                InsertBuilder.SetArrangement(partNumber);
                if (InsertBuilder.count == 0)
                {
                    InsertContainer.Hide();
                }
                break;
            case "Backshell":
                InsertContainer.Hide();
                ContactsContainer.Hide();
                MatchingBackshellsContainer.Hide();
                ContactDetailsContainer.Hide();
                break;
            case "Replacement_Contacts":
                InsertContainer.Hide();
                ContactsContainer.Hide();
                MatchingBackshellsContainer.Hide();
                ContactDetailsContainer.Show();

                ContactDetailsContainer.PopulatePanel(partNumber);
                break;
            case "No Data":
                InsertContainer.Hide();
                ContactsContainer.Hide();
                MatchingBackshellsContainer.Hide();
                ContactDetailsContainer.Hide();
                DetailsContainer.Hide();
                break;
            default:
                DetailsContainer.Show();
                InsertContainer.Hide();
                ContactsContainer.Hide();
                MatchingBackshellsContainer.Hide();
                ContactDetailsContainer.Hide();
                break;
        }
    }
    
    private void OnPopoutButtonClicked(string PartNumber)
    {
        string partNumber = PartNumber;

        GD.Print($"Button clicked for PartNumber: {partNumber}");
        
        var popupWindow = new Window();
        PackedScene _itemPanelScene = GD.Load<PackedScene>("res://Scenes/ItemPanel.tscn");

        // 2. Configure the Window's properties
        popupWindow.Title = "Contact Details";
        popupWindow.InitialPosition = Window.WindowInitialPosition.CenterMainWindowScreen;
        popupWindow.Size = new Vector2I(900, 700); // Set a default size
        popupWindow.Unresizable = false; // Allow resizing if you want
        
        // IMPORTANT: Handle the close request to free the window from memory.
        // A lambda function is a clean way to do this inline.
        popupWindow.CloseRequested += () => popupWindow.QueueFree();

        // 3. Load and instance your existing ContactDetailsPanel scene
        ItemPanel panelInstance = _itemPanelScene.Instantiate<ItemPanel>();

        // 4. Add the panel as a child of the window
        popupWindow.AddChild(panelInstance);

        // 5. Add the completed window to the main scene so it becomes visible
        AddChild(popupWindow);

        // 6. Populate the panel with data and show the window
        panelInstance.PopulatePanel(partNumber);
        popupWindow.Show();
        
    }
}
