[gd_scene load_steps=9 format=3 uid="uid://8wukgq4tefk0"]

[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_5dyfq"]
[ext_resource type="Script" uid="uid://dc1q34cwdekmw" path="res://Scripts/AssistantSuiteMain.cs" id="2_nni7d"]
[ext_resource type="Texture2D" uid="uid://du1iuilgphjfj" path="res://Icons/ConnectorIconLarge.svg" id="3_nni7d"]
[ext_resource type="Texture2D" uid="uid://djy7ebsisrjsr" path="res://Icons/InventoryIconLarge.svg" id="4_y70th"]
[ext_resource type="Texture2D" uid="uid://cog2a4xlwokvf" path="res://Icons/ToolsIconLarge.svg" id="5_t527v"]
[ext_resource type="Texture2D" uid="uid://x0a0pqx5kxyw" path="res://Icons/E2DLogo.png" id="7_y70th"]
[ext_resource type="FontFile" uid="uid://dlvu30o86he7u" path="res://Fonts/Roboto-Light.ttf" id="8_t527v"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_cdnyj"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[node name="AssistantSuiteMain" type="PanelContainer" node_paths=PackedStringArray("ConnectorAssistantButton", "InventoryAssistantButton", "ToolsAssistantButton")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2
theme = ExtResource("1_5dyfq")
theme_override_styles/panel = SubResource("StyleBoxFlat_cdnyj")
script = ExtResource("2_nni7d")
ConnectorAssistantButton = NodePath("VBoxContainer/HBoxContainer/Control/VBoxContainer/ConnectorAssistantButton")
InventoryAssistantButton = NodePath("VBoxContainer/HBoxContainer/Control2/VBoxContainer2/InventoryAssistantButton")
ToolsAssistantButton = NodePath("VBoxContainer/HBoxContainer/Control3/VBoxContainer3/ToolAssistantButton")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer"]
layout_mode = 2
theme_override_constants/margin_top = 50
theme_override_constants/margin_bottom = 50

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 3
theme_override_constants/separation = 300

[node name="Control" type="Control" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="VBoxContainer/HBoxContainer/Control"]
custom_minimum_size = Vector2(0, 200)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -100.0
offset_right = 100.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3

[node name="ConnectorAssistantButton" type="Button" parent="VBoxContainer/HBoxContainer/Control/VBoxContainer"]
custom_minimum_size = Vector2(200, 150)
layout_mode = 2
icon = ExtResource("3_nni7d")
flat = true
icon_alignment = 1

[node name="Label" type="Label" parent="VBoxContainer/HBoxContainer/Control/VBoxContainer"]
layout_mode = 2
text = "CONNECTOR ASSISTANT"
horizontal_alignment = 1

[node name="Control2" type="Control" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="VBoxContainer2" type="VBoxContainer" parent="VBoxContainer/HBoxContainer/Control2"]
custom_minimum_size = Vector2(0, 200)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -88.5
offset_right = 100.0
offset_bottom = 88.5
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3

[node name="InventoryAssistantButton" type="Button" parent="VBoxContainer/HBoxContainer/Control2/VBoxContainer2"]
custom_minimum_size = Vector2(200, 150)
layout_mode = 2
icon = ExtResource("4_y70th")
flat = true
icon_alignment = 1

[node name="Label2" type="Label" parent="VBoxContainer/HBoxContainer/Control2/VBoxContainer2"]
layout_mode = 2
text = "INVENTORY ASSISTANT"
horizontal_alignment = 1

[node name="Control3" type="Control" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="VBoxContainer3" type="VBoxContainer" parent="VBoxContainer/HBoxContainer/Control3"]
custom_minimum_size = Vector2(0, 200)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -100.0
offset_right = 100.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3

[node name="ToolAssistantButton" type="Button" parent="VBoxContainer/HBoxContainer/Control3/VBoxContainer3"]
custom_minimum_size = Vector2(200, 150)
layout_mode = 2
icon = ExtResource("5_t527v")
flat = true
icon_alignment = 1

[node name="Label3" type="Label" parent="VBoxContainer/HBoxContainer/Control3/VBoxContainer3"]
layout_mode = 2
text = "TOOLS ASSISTANT"
horizontal_alignment = 1

[node name="TextureRect2" type="TextureRect" parent="VBoxContainer"]
modulate = Color(1, 1, 1, 0.0509804)
custom_minimum_size = Vector2(0, 64)
layout_mode = 2
texture = ExtResource("7_y70th")
expand_mode = 1
stretch_mode = 5

[node name="Label2" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 150)
layout_mode = 2
size_flags_vertical = 1
theme_override_fonts/font = ExtResource("8_t527v")
text = "VERSION 0.4"
horizontal_alignment = 1
vertical_alignment = 2
