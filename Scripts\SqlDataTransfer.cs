using Godot;
using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;

public partial class SqlDataTransfer : PanelContainer
{
    private string sourceConnectionString = "Server=AZ08SVDW0002,51122;Database=CableDesignTool_Play;Trusted_Connection=yes";
    private string targetConnectionString = "Server=RSSVAG-DB0161;Database=SysTest;Trusted_Connection=yes";

    [Export] Button TransferButton;
    [Export] VBoxContainer TableListContainer;

    


    // public override void _Ready()
    // {
    //     TransferButton.Pressed += TransferData;
    // }

    // public async void TransferData()
    // {
    //     using (SqlConnection sourceConnection = new SqlConnection(sourceConnectionString))
    //     {
    //         sourceConnection.Open();

    //         // Get all table names from the source database
    //         DataTable tables = sourceConnection.GetSchema("Tables");
    //         foreach (DataRow table in tables.Rows)
    //         {
    //             string tableName = table["TABLE_NAME"].ToString();
    //             GD.Print(tableName);
    //             DataTable dataTable = new DataTable(tableName);

    //             // Fetch the data from the source table
    //             using (SqlCommand command = new SqlCommand($"SELECT * FROM [{tableName}]", sourceConnection))
    //             using (SqlDataAdapter adapter = new SqlDataAdapter(command))
    //             {
    //                 adapter.Fill(dataTable);
    //             }

    //             // Create a new DataTable to hold the data with types
    //             DataTable targetTable = new DataTable(tableName);

    //             Label label = new Label();
    //             label.Text = tableName;
    //             TableListContainer.AddChild(label);

    //             // Define columns in the target DataTable
    //             foreach (DataColumn column in dataTable.Columns)
    //             {
    //                 targetTable.Columns.Add(column.ColumnName, column.DataType);
    //             }

    //             // Import data into the target DataTable
    //             foreach (DataRow row in dataTable.Rows)
    //             {
    //                 targetTable.ImportRow(row);
    //             }

    //             // Insert DataTable into the target database
    //             using (SqlConnection targetConnection = new SqlConnection(targetConnectionString))
    //             {
    //                 targetConnection.Open();

    //                 // Create the target table if it doesn't exist
    //                 // GD.Print(@$"IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{tableName}') BEGIN CREATE TABLE dbo.{tableName} ({GetCreateTableSQL(targetTable)})END");
    //                 using (SqlCommand createTableCommand = new SqlCommand(@$"IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{tableName}') BEGIN CREATE TABLE dbo.{tableName} ({GetCreateTableSQL(targetTable)}) END", targetConnection))
    //                 {
    //                     createTableCommand.ExecuteNonQuery();
    //                 }
    //             }

    //             //     // Insert data into the target table
    //                 //     foreach (DataRow row in targetTable.Rows)
    //                 //     {
    //                 //         // GD.Print($"Inserting row #{targetTable.Rows.IndexOf(row)} into table: {string.Join(",", row.ItemArray)}");
    //                 //         var columnNames = string.Join(",", targetTable.Columns.Cast<DataColumn>().Select(c => $"[{c.ColumnName}]"));
    //                 //         var valuesList = row.ItemArray.Select(item =>
    //                 //             item == DBNull.Value ? "NULL" : // Handle DBNull
    //                 //             item is string str && string.IsNullOrEmpty(str) ? "NULL" :
    //                 //             item is string ? $"'{((string)item).Replace("'", "''")}'" :
    //                 //             item is bool ? ((bool)item ? "1" : "0") :
    //                 //             item is DateTime ? $"'{((DateTime)item).ToString("yyyy-MM-dd HH:mm:ss")}'" :
    //                 //             item.ToString()
    //                 //         );

    //                 //         // Ensure we properly check for empty values to avoid trailing comma
    //                 //         var values = string.Join(",", valuesList); // This ensures no empty values are joined
    //                 //         // GD.Print("___________");
    //                 //         // GD.Print($"Column Names: {columnNames}");
    //                 //         // GD.Print($"Values: {values}");
    //                 //         // GD.Print($"Column Count: {targetTable.Columns.Count}, Value Count: {valuesList.Count()}");

    //                 //         if (targetTable.Columns.Count == valuesList.Count())
    //                 //         {
    //                 //             string sqlCommandText = $"INSERT INTO [{tableName}] ({columnNames}) VALUES ({values});";
    //                 //             // GD.Print($"Executing SQL Command: {sqlCommandText}");
    //                 //             using (SqlCommand insertCommand = new SqlCommand(sqlCommandText, targetConnection))
    //                 //             {
    //                 //                 insertCommand.ExecuteNonQuery();
    //                 //                 // await ToSignal(GetTree().CreateTimer(0.0001), "timeout");
    //                 //                 // GD.Print("Loaded " + targetTable.Rows.IndexOf(row) + " rows");
    //                 //                 // if (targetTable.Rows.IndexOf(row) % 100 == 0)
    //                 //                 // {
    //                 //                 // }
    //                 //             }
    //                 //             using (var bulkCopy = new SqlBulkCopy(targetConnection))
    //                 //             {
    //                 //                 bulkCopy.DestinationTableName = tableName;

    //                 //                 foreach (DataColumn column in targetTable.Columns)
    //                 //                 {
    //                 //                     bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
    //                 //                 }

    //                 //                 bulkCopy.WriteToServer(targetTable);
    //                 //             }
    //                 //         }
    //                 //         else
    //                 //         {
    //                 //             GD.PrintErr("Mismatch between number of columns and values!");
    //                 //         }
    //                 //     }
    //                 // }


    //                 // Assuming targetTable is your DataTable containing the data to be inserted
    //                 using (SqlConnection targetConnection = new SqlConnection(targetConnectionString))
    //                 {
    //                     targetConnection.Open();

    //                     // Use SqlBulkCopy for bulk insertion
    //                     if (tableName == "type_attribs")
    //                     {
    //                         GD.Print("parts entered");
    //                         using (var bulkCopy = new SqlBulkCopy(targetConnection))
    //                         {
    //                             bulkCopy.DestinationTableName = "[dbo]." + tableName;

    //                             // Map the columns dynamically
    //                             foreach (DataColumn column in targetTable.Columns)
    //                             {
    //                                 bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
    //                             }

    //                         // Write from the DataTable to the database
    //                         try
    //                         {
    //                             await bulkCopy.WriteToServerAsync(targetTable);
    //                             GD.Print($"Data for table {tableName} imported successfully.");
    //                         }
    //                         catch (Exception ex)
    //                         {
    //                             GD.PrintErr("Error during bulk insert: " + ex.Message);
    //                         }
    //                         }
    //                     }
    //                 }
    //         }
    //     }
    // }

    // private string GetCreateTableSQL(DataTable table)
    // {
    //     string createTableSQL = "";
    //     foreach (DataColumn column in table.Columns)
    //     {
    //         createTableSQL += $"[{column.ColumnName}] {GetSqlType(column.DataType)}, ";
    //     }
    //     // GD.Print(createTableSQL.TrimEnd(',', ' '));
    //     return createTableSQL.TrimEnd(',', ' ');
    // }

    // private string GetSqlType(Type type)
    // {
    //     if (type == typeof(int)) return "INT";
    //     if (type == typeof(string)) return "VARCHAR(MAX)";
    //     if (type == typeof(DateTime)) return "DATETIME";
    //     if (type == typeof(bool)) return "BIT";
    //     if (type == typeof(double)) return "FLOAT";
    //     if (type == typeof(float)) return "FLOAT";
    //     if (type == typeof(byte)) return "TINYINT";
    //     if (type == typeof(short)) return "SMALLINT";
    //     if (type == typeof(decimal)) return "DECIMAL";

    //     // Add more mappings as needed

    //     throw new NotSupportedException($"The type {type.Name} is not supported.");
    // }
}
