[gd_scene load_steps=2 format=3 uid="uid://bai711sss5rqe"]

[ext_resource type="Script" uid="uid://cdyfcrucbbbnw" path="res://Scripts/SettingsBar.cs" id="1_qxhf8"]

[node name="SettingsBar" type="MarginContainer" node_paths=PackedStringArray("ProfileButton", "SettingsButton", "HelpButton")]
theme_override_constants/margin_left = 25
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 25
theme_override_constants/margin_bottom = 10
script = ExtResource("1_qxhf8")
ProfileButton = NodePath("HBoxContainer2/ProfileButton")
SettingsButton = NodePath("HBoxContainer2/SettingsButton")
HelpButton = NodePath("HBoxContainer2/HelpButton")

[node name="HBoxContainer2" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 0

[node name="ProfileButton" type="Button" parent="HBoxContainer2"]
layout_mode = 2
text = "Profile"
flat = true

[node name="SettingsButton" type="Button" parent="HBoxContainer2"]
layout_mode = 2
text = "Settings"
flat = true

[node name="HelpButton" type="Button" parent="HBoxContainer2"]
layout_mode = 2
text = "Help"
flat = true
