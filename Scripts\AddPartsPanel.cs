using Godot;
using System;
using System.Data;

public partial class AddPartsPanel : PanelContainer
{
    [Export] public OptionButton PartTypeOptionButton;
    [Export] public OptionButton ManufacturerOptionButton;
    [Export] public FlowContainer AttributeContainer;
    public CustomSignals customSignals;
    public SQLHandler sqlHandler;

    public override void _Ready()
    {
        GD.Print(PartTypeOptionButton.Name);
        ConnectSignals();
        LoadPartTypes();

        // Get the popup and connect to its "AboutToPopup" signal
        ConfigureOptionButtonPopup(PartTypeOptionButton);
        ConfigureOptionButtonPopup(ManufacturerOptionButton);
    }

    // Configure popup size for any OptionButton
    private void ConfigureOptionButtonPopup(OptionButton optionButton)
    {
        PopupMenu popup = optionButton.GetPopup();
        popup.AboutToPopup += () =>
        {
            // Disable auto-fit behavior
            popup.WrapControls = false;

            // Force popup to a specific size
            var desiredSize = new Vector2I((int)optionButton.Size.X, 250);
            popup.Size = desiredSize;
            popup.MinSize = desiredSize;
            popup.MaxSize = desiredSize;
        };
    }

    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        PartTypeOptionButton.ItemSelected += OnPartTypeOptionButtonItemSelected;
    }

    private async void OnPartTypeOptionButtonItemSelected(long index)
    {
        foreach (var child in AttributeContainer.GetChildren())
        {
            child.QueueFree();
        }

        GD.Print("Part Type Selected: " + PartTypeOptionButton.GetItemText((int)index));
        string type = PartTypeOptionButton.GetItemText((int)index);
        DataTable partAttributes = await sqlHandler.QueryAsync(@$"
        SELECT dbo.attributes.display_name, types.type 
        FROM dbo.attributes 
        JOIN dbo.type_attribs ON attributes.[index] = type_attribs.attrib_index 
        JOIN dbo.types ON type_attribs.type_index = types.[index] 
        WHERE types.type IN ('{type}') 
        ORDER BY type_attribs.attrib_index");

        foreach (DataRow row in partAttributes.Rows)
        {
            VBoxContainer hBox = new VBoxContainer();
            hBox.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;

            Label label = new Label();
            label.Text = row["display_name"].ToString();

            LineEdit attributeLine = new LineEdit();
            attributeLine.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            attributeLine.CustomMinimumSize = new Vector2(300, 0);
            attributeLine.PlaceholderText = $"Type {row["display_name"]}";

            hBox.AddChild(label);
            hBox.AddChild(attributeLine);
            AttributeContainer.AddChild(hBox);
        }

        LoadManufacturers(type);
    }

    private void LoadPartTypes()
    {
        PartTypeOptionButton.Clear();
        DataTable partTypes = sqlHandler.Query($"SELECT [type] FROM dbo.types");
        foreach (DataRow row in partTypes.Rows)
        {
            PartTypeOptionButton.AddItem(row["type"].ToString());
        }
    }

    private void LoadManufacturers(string type)
    {
        ManufacturerOptionButton.Clear();

        DataTable manufacturers = sqlHandler.Query(@$"
        SELECT DISTINCT value
        FROM dbo.parts AS p
        LEFT JOIN part_types AS pt ON pt.part_index = p.[index]
        LEFT JOIN part_attrib_str AS ps ON ps.part_index = pt.part_index
        LEFT JOIN types AS t ON t.[index] = pt.type_index 
        WHERE ps.attrib_index = 2 AND t.type = '{type}'");

        foreach (DataRow row in manufacturers.Rows)
        {
            ManufacturerOptionButton.AddItem(row["value"].ToString());
        }
    }
}