using Godot;
using Godot.Collections;
using System;
using System.Data;
using System.Linq;

public partial class ToolAssistantWireBundleCalculator : PanelContainer
{
    public float BundleDiameter = 0.0f;
    [Export] VBoxContainer WireGroups;
    [Export] Button ResetButton;
    [Export] Button CalculateButton;
    [Export] Button AddButton;
    [Export] LineEdit BundleDiameterLine;
    [Export] Tree WireTree;
    [Export] public CirclePacker CirclePacker;
    [Export] public float scaleFactor = 50.0f;
    [Export] public Button CirclePackerResetButton;
    public SQLHandler sqlHandler;
    public CustomSignals customSignals;
    Dictionary<int, float> MultiplicationFactor = new Dictionary<int, float>
    {
        {1, 1.0f}, {12, 4.3f},
        {2, 2.0f}, {14, 4.6f},
        {3, 2.2f}, {16, 5.0f},
        {4, 2.4f}, {18, 5.3f},
        {5, 2.7f}, {20, 5.6f},
        {6, 2.9f}, {24, 6.0f},
        {7, 3.0f}, {28, 6.5f},
        {8, 3.3f}, {32, 6.9f},
        {9, 3.8f}, {36, 7.4f},
        {10, 4.0f}, {40, 7.7f},
        {45, 8.1f}, {90, 11.6f},
        {50, 8.5f}, {100, 12.2f},
        {55, 8.9f}, {125, 13.7f},
        {60, 9.3f}, {150, 15.0f},
        {65, 9.7f}, {175, 16.1f},
        {70, 10.1f}, {200, 17.2f},
        {75, 10.5f}, {225, 19.3f},
        {80, 10.9f}, {300, 21.0f}
    };

    public override void _Ready()
    {
        ConnectSignals();
        LoadWireTree();
    }

    public void LoadWireTree()
    {
        DataTable WireData = sqlHandler.Query($@"
        WITH UnifiedAttributeValues AS 
        (
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_str
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_int
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_float
            UNION ALL
            SELECT part_index, attrib_index, CAST(value AS NVARCHAR(MAX)) AS attribute_value FROM dbo.part_attrib_bit
        ),
        ChoiceSetLookups AS (
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)) AS [Value], display AS DisplayValue FROM csAttrib_str
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_int
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_float
            UNION ALL
            SELECT attrib_index, CAST(value AS VARCHAR(MAX)), display FROM csAttrib_bit
        ),
        PivotedPartsData AS
        (
        SELECT p.[index] AS PartId, p.PartNumber, t.type AS [PartType], STRING_AGG(CASE WHEN attr.display_name = 'Wire Type' THEN COALESCE(csl.DisplayValue, uav.attribute_value) END, ', ') AS [Wire Type], 
        STRING_AGG(CASE WHEN attr.display_name = 'Outside Diameter' THEN COALESCE(csl.DisplayValue, uav.attribute_value) END, ', ') AS [Outside Diameter], 
        STRING_AGG(CASE WHEN attr.display_name = 'Wire Gauge' THEN COALESCE(csl.DisplayValue, uav.attribute_value) END, ', ') AS [Wire Gauge]
        FROM [dbo].[parts] p
        JOIN dbo.part_types pt ON p.[index] = pt.part_index
        JOIN dbo.types t ON pt.type_index = t.[index]
        LEFT JOIN dbo.type_attribs ta ON t.[index] = ta.type_index
        LEFT JOIN dbo.attributes attr ON ta.attrib_index = attr.[index]
        LEFT JOIN UnifiedAttributeValues uav ON p.[index] = uav.part_index AND attr.[index] = uav.attrib_index
        LEFT JOIN ChoiceSetLookups csl ON uav.attrib_index = csl.attrib_index AND uav.attribute_value = csl.[Value]
        GROUP BY t.type, p.[index], p.PartNumber
        )
        SELECT [PartNumber], [PartType], [Wire Type],[Wire Gauge], [Outside Diameter]
        FROM PivotedPartsData
        WHERE [PartType] = 'Cable_Wire'
        ORDER BY [Wire Gauge]");

        WireTree.Clear();
        WireTree.Columns = WireData.Columns.Count;
        WireTree.ColumnTitlesVisible = true;
        
        WireTree.SetColumnTitle(0, "Part Number");
        WireTree.SetColumnTitle(1, "Wire Type");
        WireTree.SetColumnTitle(2, "Wire Gauge");
        WireTree.SetColumnTitle(3, "Outside Diameter");

        WireTree.SetColumnCustomMinimumWidth(0, 200);
        WireTree.SetColumnCustomMinimumWidth(1, 100);
        WireTree.SetColumnCustomMinimumWidth(2, 100);
        WireTree.SetColumnCustomMinimumWidth(3, 100);

        WireTree.SetColumnExpand(0, true);
        WireTree.SetColumnExpand(1, true);
        WireTree.SetColumnExpand(2, true);
        WireTree.SetColumnExpand(3, true);

        TreeItem root = WireTree.CreateItem();
        WireTree.HideRoot = true;

        foreach (DataRow row in WireData.Rows)
        {
            TreeItem item = WireTree.CreateItem(root);
            item.SetText(0, row["PartNumber"].ToString());
            item.SetText(1, row["Wire Type"].ToString());
            item.SetText(2, row["Wire Gauge"].ToString());
            item.SetText(3, row["Outside Diameter"].ToString());

            item.AddButton(4, GD.Load<Texture2D>("res://Icons/AddLargeIcon.svg"));

            item.SetTextAlignment(0, HorizontalAlignment.Left);
            item.SetTextAlignment(1, HorizontalAlignment.Center);
            item.SetTextAlignment(2, HorizontalAlignment.Center);
            item.SetTextAlignment(3, HorizontalAlignment.Center);
        }
    }

    public void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        ResetButton.Pressed += OnResetButtonPressed;
        CalculateButton.Pressed += OnCalculateButtonPressed;
        AddButton.Pressed += OnAddButtonPressed;
        WireTree.ButtonClicked += OnWireTreeButtonClicked;
        CirclePackerResetButton.Pressed += OnCirclePackerResetButtonPressed;
    }

    private void OnCirclePackerResetButtonPressed()
    {
        CirclePacker.Camera.Zoom = new Vector2(1, 1);
        CirclePacker.Camera.Position = Vector2.Zero;
    }


    private void OnWireTreeButtonClicked(TreeItem item, long column, long id, long mouseButtonIndex)
    {
        PackedScene WireGroupSegmentPacked = GD.Load<PackedScene>("res://Scenes/ToolAssistantWireGroupSegment.tscn");
        ToolAssistantWireGroupSegment wireGroupSegmentInstance = (ToolAssistantWireGroupSegment)WireGroupSegmentPacked.Instantiate();
        WireGroups.AddChild(wireGroupSegmentInstance);
        AddButton.MoveToFront();
        wireGroupSegmentInstance.WireDiameterLine.Text = item.GetText(3);
        wireGroupSegmentInstance.QuantityLine.Text = "1";
        wireGroupSegmentInstance.WireDiameter = wireGroupSegmentInstance.WireDiameterLine.Text.ToFloat();
        wireGroupSegmentInstance.Quantity = wireGroupSegmentInstance.QuantityLine.Text.ToInt();
        wireGroupSegmentInstance.ColorButton.Color = Colors.White;

    }


    private void OnAddButtonPressed()
    {
        PackedScene WireGroupSegmentPacked = GD.Load<PackedScene>("res://Scenes/ToolAssistantWireGroupSegment.tscn");
        var wireGroupSegmentInstance = WireGroupSegmentPacked.Instantiate();
        WireGroups.AddChild(wireGroupSegmentInstance);
        AddButton.MoveToFront();
    }


    private void OnCalculateButtonPressed()
    {
        CirclePacker.ClearCircles();
        foreach (var child in WireGroups.GetChildren())
        {
            GD.Print("Child: " + child.Name);
            if (child is ToolAssistantWireGroupSegment)
            {
                for (int i = 0; i < (child as ToolAssistantWireGroupSegment).Quantity; i++)
                {
                    CirclePacker.AddCircle((child as ToolAssistantWireGroupSegment).WireDiameter, (child as ToolAssistantWireGroupSegment).ColorButton.Color);
                }
            }
        }
    }


    private void OnResetButtonPressed()
    {
        BundleDiameter = 0.0f;
    }

    public void CalculateBundleDiameter()
    {
        
    }
}
