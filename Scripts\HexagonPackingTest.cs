using Godot;
using System;
using System.Runtime.Serialization;

public partial class HexagonPackingTest : Node2D
{
    [Export] public TileMapLayer tileMap;

    public void CreateHexPositions()
    {
        Vector2[] hexPositions = new Vector2[7];

        for (int i = 0; i < hexPositions.Length; i++)
        {
            if (i == 0)
            {
                hexPositions[i] = new Vector2(0, 0);
            }

            
        }
    }
    public override void _Draw()
    {
        float radius = 50.0f;
        int numCircles = 7;

        DrawCircle(Vector2.Zero, radius, Colors.White, false, 2, true);

        for (int i = 0; i < numCircles - 1; i++)
        {
            DrawSetTransform(new Vector2(0, -radius * 2));
            DrawCircle(Vector2.Zero, radius, Colors.White, false, 2, true);
        }
    }
}
