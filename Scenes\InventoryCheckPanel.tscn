[gd_scene load_steps=6 format=3 uid="uid://cr0t1b540fj8b"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_d5bdi"]
[ext_resource type="Texture2D" uid="uid://d14cwjbmu8htb" path="res://Icons/Good.svg" id="1_hkid8"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_tohpq"]
content_margin_left = 5.0
content_margin_right = 5.0
content_margin_bottom = 5.0
bg_color = Color(0.163977, 0.174082, 0.2195, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_d5bdi"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_byq5g"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.180989, 0.191843, 0.240609, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[node name="InventoryCheckPanel" type="PanelContainer"]
custom_minimum_size = Vector2(450, 0)
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 8
theme = ExtResource("1_d5bdi")
theme_override_styles/panel = SubResource("StyleBoxFlat_tohpq")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Inventory"
horizontal_alignment = 1

[node name="PanelContainer" type="PanelContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_d5bdi")

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/PanelContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="HBoxContainer" type="VBoxContainer" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 5

[node name="PanelContainer" type="PanelContainer" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_byq5g")

[node name="HBoxContainer" type="FlowContainer" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/PanelContainer"]
layout_mode = 2
size_flags_vertical = 4

[node name="TextureRect" type="TextureRect" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/PanelContainer/HBoxContainer"]
custom_minimum_size = Vector2(23.755, 0)
layout_mode = 2
texture = ExtResource("1_hkid8")
expand_mode = 1
stretch_mode = 5

[node name="Label4" type="Label" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/PanelContainer/HBoxContainer"]
layout_mode = 2
text = "In Stock"

[node name="Label" type="Label" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/PanelContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Quantity: 26"

[node name="Label2" type="Label" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/PanelContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Room: 128"

[node name="Label3" type="Label" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/PanelContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Bin: 40A"

[node name="FlowContainer" type="HBoxContainer" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="Button" type="Button" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/FlowContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
text = "Request"

[node name="Button2" type="Button" parent="VBoxContainer/PanelContainer/MarginContainer/HBoxContainer/HBoxContainer/FlowContainer"]
layout_mode = 2
text = "Inventory Assistant"
