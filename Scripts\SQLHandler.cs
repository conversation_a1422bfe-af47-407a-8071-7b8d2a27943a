using Godot;
using System;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using System.Data;
using System.Collections.Generic;

public partial class SQLHandler : Node
{
    public string ConnectionString { get; set; } = "Server=AZ08SVDW0002,51122;Database=CableDesignTool_Play;Trusted_Connection=yes";
    public string SysTestConnectionString { get; set; } = "Server=localhost\\SQLEXPRESS;Database=SysTest;Integrated Security=True;TrustServerCertificate=True;";
    public override void _Ready()
    {
        GD.Print("SQLHandler initialized");
    }

    public async Task<DataTable> QueryAsync(string query)
    {
        var dataTable = new DataTable();
        try
        {
            using (var connection = new SqlConnection(SysTestConnectionString))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        dataTable.Load(reader);
                    }
                }
            }
        }
        catch (SqlException ex) { GD.Print($"SQL error: {ex.Message}"); throw; }
        return dataTable;
    }
    public bool CheckConnectionQuery()
    {
        using (SqlConnection connection = new SqlConnection(SysTestConnectionString))
        {
            try
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand("SELECT IS_MEMBER('db_datareader')", connection))
                {
                    object result = command.ExecuteScalar();
    
                    if (result is int intValue)
                    {
                        return intValue != 0; // Convert 0 to false and non-zero to true
                    }
                    // Handle unexpected result types if necessary
                    throw new InvalidCastException("Expected an integer result.");
                }
            }
            catch (SqlException ex)
            {
                GD.Print($"SQL error: {ex.Message}");
                return false;
            }
        }
    }
    public async Task<DataTable> QueryStoredProcedureAsync(string StoredProcedure, params SqlParameter[] parameters)
    {
        var dataTable = new DataTable();
        try
        {
            using (var connection = new SqlConnection(SysTestConnectionString))
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(StoredProcedure, connection) { CommandType = CommandType.StoredProcedure })
                {
                    command.Parameters.AddRange(parameters);
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        await reader.GetColumnSchemaAsync();
                        dataTable.Load(reader);
                    }
                }
            }
        }
        catch (SqlException ex) { GD.Print($"SQL error: {ex.Message}"); throw; }
        return dataTable;
    }
    public List<DataTable> QueryStoredProcedure(string storedProcedure, params SqlParameter[] parameters)
    {
        var tables = new List<DataTable>();

        try
        {
            using (var connection = new SqlConnection(SysTestConnectionString))
            {
                connection.Open();
                using (var command = new SqlCommand(storedProcedure, connection) { CommandType = CommandType.StoredProcedure })
                {
                    command.Parameters.AddRange(parameters);
                    using (var reader = command.ExecuteReader())
                    {
                        do
                        {
                            DataTable table = new DataTable();

                            // Dynamically create columns based on the reader's schema
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                table.Columns.Add(reader.GetName(i), reader.GetFieldType(i)); // Get field type dynamically
                            }

                            // Load data into the DataTable
                            while (reader.Read())
                            {
                                var row = table.NewRow();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    // Handle potential null values
                                    row[i] = reader.IsDBNull(i) ? DBNull.Value : reader.GetValue(i);
                                }
                                table.Rows.Add(row);
                            }

                            // Add the filled DataTable to the list of tables
                            tables.Add(table);
                        } while (reader.NextResult()); // Move to the next result set
                    }
                }
            }
        }
        catch (SqlException ex)
        {
            GD.Print($"SQL error: {ex.Message}");
            throw;
        }

        return tables; // Return the list of DataTables
    }
    public List<DataTable> QueryStoredProcedureLoadEnabled(ProgressBar progressBar, Label label, string storedProcedure, params SqlParameter[] parameters)
    {
        var tables = new List<DataTable>();
        try
        {
            using (var connection = new SqlConnection(SysTestConnectionString))
            {
                connection.Open();
                using (var command = new SqlCommand(storedProcedure, connection) { CommandType = CommandType.StoredProcedure })
                {
                    command.Parameters.AddRange(parameters);
                    using (var reader = command.ExecuteReader())
                    {
                        do
                        {
                            DataTable table = new DataTable();

                            // Dynamically create columns based on the reader's schema
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                table.Columns.Add(reader.GetName(i), reader.GetFieldType(i)); // Get field type dynamically
                            }

                            // Load data into the DataTable
                            while (reader.Read())
                            {
                                var row = table.NewRow();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    // Handle potential null values
                                    row[i] = reader.IsDBNull(i) ? DBNull.Value : reader.GetValue(i);
                                }
                                table.Rows.Add(row);
                            }
                            // Add the filled DataTable to the list of tables
                            tables.Add(table);
                        } while (reader.NextResult()); // Move to the next result set
                    }
                }
            }
        }
        catch (SqlException ex)
        {
            GD.Print($"SQL error: {ex.Message}");
            throw;
        }
        GD.Print("Done loading tables");
        return tables; // Return the list of DataTables
    }
    public async Task<string> QuerySingleItem(string query)
    {
        using (SqlConnection connection = new SqlConnection(SysTestConnectionString))
        {
            try
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    return command.ExecuteScalar().ToString();
                }
            }
            catch (SqlException ex)
            {
                GD.Print($"SQL error: {ex.Message}");
                return "";
            }
        }
    }
    public DataTable Query(string query)
    {
        var dataTable = new DataTable();
        try
        {
            using (var connection = new SqlConnection(SysTestConnectionString))
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        dataTable.Load(reader);
                    }
                }
            }
        }
        catch (SqlException ex)
        {
            GD.Print($"SQL error: {ex.Message}");
            throw;
        }
        return dataTable;
    }

    public void AddToTable(string query)
    {
        using (SqlConnection connection = new SqlConnection(SysTestConnectionString))
        {
            try
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.ExecuteReader();
                }
            }
            catch (SqlException ex)
            {
                GD.Print($"SQL error: {ex.Message}");
                throw;
            }
        }
    }

    public void AddToSysTestTable(string query)
    {
        using (SqlConnection connection = new SqlConnection(SysTestConnectionString))
        {
            try
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.ExecuteReader();
                }
            }
            catch (SqlException ex)
            {
                GD.Print($"SQL error: {ex.Message}");
                throw;
            }
        }
    }

    public string SysTestQuery(string query)
    {
        try
        {
            using (var connection = new SqlConnection(SysTestConnectionString))
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    return command.ExecuteScalar().ToString();
                }
            }
        }
        catch (SqlException ex)
        {
            GD.Print($"SQL error: {ex.Message}");
            throw;
        }
    }
}
