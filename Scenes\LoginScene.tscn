[gd_scene load_steps=5 format=3 uid="uid://d1ixuhqu4kwfw"]

[ext_resource type="Script" uid="uid://dqe3fn53ndfyy" path="res://Scripts/PopupWindow.cs" id="1_7bcn3"]
[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_76d8l"]
[ext_resource type="Script" uid="uid://du45uic8y8mvq" path="res://Scripts/LoginScene.cs" id="2_7bcn3"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t4pb4"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[node name="LoginScene" type="Window"]
auto_translate_mode = 1
title = "Login Screen"
initial_position = 1
size = Vector2i(400, 400)
theme = ExtResource("1_76d8l")
script = ExtResource("1_7bcn3")

[node name="LoginScene" type="PanelContainer" parent="." node_paths=PackedStringArray("LoginButton", "UsernameInput", "PasswordInput", "ErrorLabel")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_76d8l")
theme_override_styles/panel = SubResource("StyleBoxFlat_t4pb4")
script = ExtResource("2_7bcn3")
LoginButton = NodePath("MarginContainer4/VBoxContainer/LoginButton")
UsernameInput = NodePath("MarginContainer4/VBoxContainer/HBoxContainer2/UsernameLine")
PasswordInput = NodePath("MarginContainer4/VBoxContainer/HBoxContainer3/PasswordLine")
ErrorLabel = NodePath("MarginContainer4/VBoxContainer/ErrorLabel")

[node name="MarginContainer4" type="MarginContainer" parent="LoginScene"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="LoginScene/MarginContainer4"]
layout_mode = 2
mouse_filter = 2
theme_override_constants/separation = 15

[node name="HBoxContainer" type="HBoxContainer" parent="LoginScene/MarginContainer4/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 10

[node name="Label" type="Label" parent="LoginScene/MarginContainer4/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 2
size_flags_vertical = 8
theme_override_font_sizes/font_size = 24
text = "Login"

[node name="HBoxContainer2" type="HBoxContainer" parent="LoginScene/MarginContainer4/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="LoginScene/MarginContainer4/VBoxContainer/HBoxContainer2"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "Username:"

[node name="UsernameLine" type="LineEdit" parent="LoginScene/MarginContainer4/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer3" type="HBoxContainer" parent="LoginScene/MarginContainer4/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="LoginScene/MarginContainer4/VBoxContainer/HBoxContainer3"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "Password:"

[node name="PasswordLine" type="LineEdit" parent="LoginScene/MarginContainer4/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3
secret = true

[node name="LoginButton" type="Button" parent="LoginScene/MarginContainer4/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_vertical = 10
text = "Sign In"

[node name="ErrorLabel" type="Label" parent="LoginScene/MarginContainer4/VBoxContainer"]
layout_mode = 2
