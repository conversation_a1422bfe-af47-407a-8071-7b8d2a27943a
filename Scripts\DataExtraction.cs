using Godot;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading.Tasks;

public partial class DataExtraction : Control
{
    public SQLHandler sqlHandler = new SQLHandler();
    public CustomSignals customSignals = new CustomSignals();
    public override void _Ready()
    {
        ConnectSignals();
        ExtractData();
    }

    public void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
    }

    public async void ExtractData()
    {
        DataTable TypeTable = await sqlHandler.QueryAsync($"SELECT [type] FROM dbo.types");

        foreach (DataRow row in TypeTable.Rows)
        {
            string type = row["type"].ToString();
            DataTable AttributeTable = await sqlHandler.QueryAsync($"SELECT dbo.attributes.display_name, dbo.attributes.attribute, types.type FROM dbo.attributes JOIN dbo.type_attribs ON attributes.[index] = type_attribs.attrib_index JOIN dbo.types ON type_attribs.type_index = types.[index] WHERE types.type = '{type}' ORDER BY type_attribs.attrib_index");

            DataTable PartTable = await sqlHandler.QueryAsync($"SELECT [PartNumber], [type] FROM parts JOIN part_types ON parts.[index] = part_types.part_index JOIN types ON part_types.type_index = types.[index] WHERE types.type = ('{type}') ORDER BY [PartNumber]");

            List<string> attributeList = new List<string>();
            foreach (DataRow itemRow in AttributeTable.Rows)
            {
                attributeList.Add(itemRow["attribute"].ToString());
            }

            List<string> partList = new List<string>();
            foreach (DataRow itemRow in PartTable.Rows)
            {
                partList.Add(itemRow["PartNumber"].ToString());
            }

            string attributes = string.Join(",", attributeList);
            string parts = string.Join(",", partList);

            string partsfilename = $"{type}_parts.txt";
            string filePath = $"C:/Users/<USER>/OneDrive - NGC/Documents/Connector Assistant/Database/Extraction/{partsfilename}";
            try
            {
                File.WriteAllText(filePath, parts);
            }
            catch (Exception e)
            {
                GD.Print(e.Message);
            }

            string attributesfilename = $"{type}_attributes.txt";
            filePath = $"C:/Users/<USER>/OneDrive - NGC/Documents/Connector Assistant/Database/Extraction/{attributesfilename}";
            try
            {
                File.WriteAllText(filePath, attributes);
            }
            catch (Exception e)
            {
                GD.Print(e.Message);
            }



        }



    }
}