[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://cu0vfun4f1kh4"
path="res://.godot/imported/Roboto_SemiCondensed-Light.ttf-06b8ebd80756721132f42c1014519a9b.fontdata"

[deps]

source_file="res://Fonts/Roboto_SemiCondensed-Light.ttf"
dest_files=["res://.godot/imported/Roboto_SemiCondensed-Light.ttf-06b8ebd80756721132f42c1014519a9b.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
