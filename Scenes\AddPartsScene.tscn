[gd_scene load_steps=6 format=3 uid="uid://dk1ehnl0fpqg2"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_n4fyt"]
[ext_resource type="Script" uid="uid://dl8qhu8bug677" path="res://Scripts/AddPartsPanel.cs" id="2_y0bc1"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_vay3w"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_n4fyt"]
content_margin_left = 8.0
content_margin_top = 3.0
content_margin_right = 8.0
content_margin_bottom = 3.0
bg_color = Color(0.176471, 0.188235, 0.227451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_y0bc1"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.102178, 0.109495, 0.142424, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.143281, 0.153133, 0.19252, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[node name="AddPartsPanel" type="PanelContainer" node_paths=PackedStringArray("PartTypeOptionButton", "ManufacturerOptionButton", "AttributeContainer")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_n4fyt")
theme_override_styles/panel = SubResource("StyleBoxFlat_vay3w")
script = ExtResource("2_y0bc1")
PartTypeOptionButton = NodePath("MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer2/PartTypeOptionButton")
ManufacturerOptionButton = NodePath("MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer3/ManufacturerOptionButton")
AttributeContainer = NodePath("MarginContainer/VBoxContainer/PanelContainer/MarginContainer/ScrollContainer/AttributeContainer")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/VBoxContainer"]
layout_mode = 2
text = "Part Number"

[node name="PartNumberLineEdit" type="LineEdit" parent="MarginContainer/VBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Type Part Number"

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="VBoxContainer2" type="VBoxContainer" parent="MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer2"]
layout_mode = 2
text = "Select Part Type"

[node name="PartTypeOptionButton" type="OptionButton" parent="MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer2"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
selected = 0
item_count = 1
popup/item_0/text = "Part Type"
popup/item_0/id = 0

[node name="VBoxContainer3" type="VBoxContainer" parent="MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer3"]
layout_mode = 2
text = "Select Manufacturer"

[node name="ManufacturerOptionButton" type="OptionButton" parent="MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer3"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
selected = 0
item_count = 1
popup/item_0/text = "Manufacturer"
popup/item_0/id = 0

[node name="VBoxContainer4" type="VBoxContainer" parent="MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer4"]
layout_mode = 2
text = "Cage Code"

[node name="PartTypeOptionButton" type="LineEdit" parent="MarginContainer/VBoxContainer/HBoxContainer/VBoxContainer4"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
placeholder_text = "Type Cage Code"

[node name="VBoxContainer3" type="VBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer/VBoxContainer3"]
layout_mode = 2
text = "Description"

[node name="PartTypeOptionButton" type="TextEdit" parent="MarginContainer/VBoxContainer/VBoxContainer3"]
custom_minimum_size = Vector2(200, 100)
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/normal = SubResource("StyleBoxFlat_n4fyt")
placeholder_text = "Type Description Here"

[node name="PanelContainer" type="PanelContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_y0bc1")

[node name="MarginContainer" type="MarginContainer" parent="MarginContainer/VBoxContainer/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="ScrollContainer" type="ScrollContainer" parent="MarginContainer/VBoxContainer/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="AttributeContainer" type="FlowContainer" parent="MarginContainer/VBoxContainer/PanelContainer/MarginContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 5
theme_override_constants/v_separation = 5

[node name="SubmitButton" type="Button" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8
text = "Submit"

[node name="PopupMenu" type="PopupMenu" parent="."]
