-- Main requests table
CREATE TABLE [dbo].[PartRequests] (
    [RequestID] INT IDENTITY(1,1) PRIMARY KEY,
    [RequestNumber] NVARCHAR(50) UNIQUE NOT NULL, -- Auto-generated: REQ-2024-001
    [RequestedBy] NVARCHAR(100) NOT NULL,
    [Department] NVARCHAR(100),
    [Priority] NVARCHAR(20) DEFAULT 'Normal', -- Low, Normal, High, Urgent
    [Status] NVARCHAR(20) DEFAULT 'Pending', -- Pending, Approved, Rejected, Fulfilled, Cancelled
    [RequestDate] DATETIME2 DEFAULT GETDATE(),
    [RequiredDate] DATETIME2,
    [ApprovedBy] NVARCHAR(100),
    [ApprovedDate] DATETIME2,
    [Notes] NVARCHAR(MAX),
    [TotalEstimatedCost] DECIMAL(10,2),
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [ModifiedDate] DATETIME2 DEFAULT GETDATE()
);

-- Individual request items
CREATE TABLE [dbo].[PartRequestItems] (
    [ItemID] INT IDENTITY(1,1) PRIMARY KEY,
    [RequestID] INT FOREIGN KEY REFERENCES PartRequests(RequestID),
    [PartNumber] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(500),
    [QuantityRequested] INT NOT NULL,
    [QuantityApproved] INT,
    [UnitCost] DECIMAL(10,2),
    [TotalCost] AS (QuantityApproved * UnitCost),
    [Justification] NVARCHAR(500),
    [Status] NVARCHAR(20) DEFAULT 'Pending', -- Pending, Approved, Rejected
    [Notes] NVARCHAR(MAX)
);

-- Request status history (optional but recommended)
CREATE TABLE [dbo].[PartRequestHistory] (
    [HistoryID] INT IDENTITY(1,1) PRIMARY KEY,
    [RequestID] INT FOREIGN KEY REFERENCES PartRequests(RequestID),
    [StatusFrom] NVARCHAR(20),
    [StatusTo] NVARCHAR(20),
    [ChangedBy] NVARCHAR(100),
    [ChangeDate] DATETIME2 DEFAULT GETDATE(),
    [Comments] NVARCHAR(MAX)
);