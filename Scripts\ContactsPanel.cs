using Godot;
using System;
using System.Data;

public partial class ContactsPanel : PanelContainer
{
    [Export] Tree contactTree;
    DataTable contactsTable { get; set; }
    SQLHandler sqlHandler;
    CustomSignals customSignals;

    public override void _Ready()
    {
        ConnectSignals();
    }

    public void OnTreeEntered()
    {
        ConnectSignals();
    }

    private void ConnectSignals()
    {
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        contactTree.ButtonClicked += OnContactTreeButtonClicked;
    }

    public void PopulatePanel(string PartNumber)
    {

        GD.Print("Populating Contacts Panel for PartNumber: " + PartNumber);

        if (sqlHandler == null)
        {
            GD.Print("SQLHandler is null");
        }
        DataTable contactsTable = sqlHandler.Query(@$"SELECT Insert_Contacts.partNumber AS PartNumber, Insert_Contacts.gauge AS Gauge, type AS Type, gender AS Gender, COUNT(DISTINCT pin) AS Count
        FROM parts
        JOIN part_attrib_int ON parts.[index] = part_attrib_int.part_index
        JOIN Insert_Arrgs ON part_attrib_int.value = Insert_Arrgs.[index]
        JOIN Inserts ON Inserts.[index] = Insert_Arrgs.insert_index
        JOIN Insert_Pins ON Inserts.[index] = Insert_Pins.insert_index
        JOIN Insert_StdContacts ON Insert_StdContacts.pin_index = Insert_Pins.[index]
        JOIN Insert_Contacts ON Insert_StdContacts.contact_index = Insert_Contacts.[index]
        WHERE part_attrib_int.attrib_index = 43 AND parts.PartNumber = '{PartNumber}'
        GROUP BY Insert_Contacts.partNumber, Insert_Contacts.gauge, type, gender;");


        if (contactsTable.Rows.Count == 0)
        {
            Visible = false;
            return;
        }
        else
        {
            Visible = true;
        }


        contactTree.Clear();

        contactTree.Columns = contactsTable.Columns.Count + 1;
        contactTree.ColumnTitlesVisible = true;
        TreeItem root = contactTree.CreateItem();
        contactTree.HideRoot = true;

        var buttonIcon = GD.Load<Texture2D>("res://Icons/ExportWhite.svg");

        // --- Column Setup (MODIFIED) ---

        // Set the total number of columns: data columns + 1 for the icon.
        contactTree.Columns = contactsTable.Columns.Count + 1;
        contactTree.ColumnTitlesVisible = true;

        // STEP 1: Create the dedicated, narrow icon column at index 0.
        contactTree.SetColumnTitle(0, ""); // No title for the icon column
        contactTree.SetColumnCustomMinimumWidth(0, 40); // Small width, just for the icon
        contactTree.SetColumnExpand(0, false); // Don't let it expand

        // Loop through your data columns, but start them at index 1.
        foreach (DataColumn column in contactsTable.Columns)
        {
            // SHIFTED INDEX: We add 1 to the index to make space for the icon column.
            int columnIndex = contactsTable.Columns.IndexOf(column) + 1;

            contactTree.SetColumnTitle(columnIndex, column.ColumnName);
            contactTree.SetColumnExpand(columnIndex, true);
            contactTree.SetColumnCustomMinimumWidth(columnIndex, 100);
        }

        foreach (DataRow row in contactsTable.Rows)
        {
            TreeItem item = contactTree.CreateItem(root);

            // STEP 2: Add the button to the icon column (column 0).
            item.AddButton(0, buttonIcon);

            // SHIFTED INDEX: Set text for all data columns, starting from index 1.
            item.SetText(1, row["PartNumber"].ToString());
            item.SetText(2, row["Gauge"].ToString());
            item.SetText(3, row["Type"].ToString());
            item.SetText(4, row["Gender"].ToString());
            item.SetText(5, row["Count"].ToString());

            // SHIFTED INDEX: Align the text in the data columns.
            item.SetTextAlignment(0, HorizontalAlignment.Left); // Icon column
            item.SetTextAlignment(1, HorizontalAlignment.Center);
            item.SetTextAlignment(2, HorizontalAlignment.Center);
            item.SetTextAlignment(3, HorizontalAlignment.Center);
            item.SetTextAlignment(4, HorizontalAlignment.Center);
            item.SetTextAlignment(5, HorizontalAlignment.Center);
        }
    }

    // --- Signal Handler (MODIFIED) ---
    private void OnContactTreeButtonClicked(TreeItem item, long column, long id, long mouseButtonIndex)
    {
        // STEP 3: Check if the button was clicked in our icon column (column 0).
       if (column == 0)
        {
            string partNumber = item.GetText(1);

            GD.Print($"Button clicked for PartNumber: {partNumber}");
            
            var popupWindow = new Window();
            PackedScene _itemPanelScene = GD.Load<PackedScene>("res://Scenes/ItemPanel.tscn");

            // 2. Configure the Window's properties
            popupWindow.Title = "Contact Details";
            popupWindow.InitialPosition = Window.WindowInitialPosition.CenterMainWindowScreen;
            popupWindow.Size = new Vector2I(900, 700); // Set a default size
            popupWindow.Unresizable = false; // Allow resizing if you want
            
            // IMPORTANT: Handle the close request to free the window from memory.
            // A lambda function is a clean way to do this inline.
            popupWindow.CloseRequested += () => popupWindow.QueueFree();

            // 3. Load and instance your existing ContactDetailsPanel scene
            ItemPanel panelInstance = _itemPanelScene.Instantiate<ItemPanel>();

            // 4. Add the panel as a child of the window
            popupWindow.AddChild(panelInstance);

            // 5. Add the completed window to the main scene so it becomes visible
            AddChild(popupWindow);

            // 6. Populate the panel with data and show the window
            panelInstance.PopulatePanel(partNumber);
            popupWindow.Show();
        }
    }
    
    
}
