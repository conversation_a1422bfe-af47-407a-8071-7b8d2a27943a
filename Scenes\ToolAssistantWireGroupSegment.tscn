[gd_scene load_steps=16 format=3 uid="uid://bgnuqoi4b6cbl"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_4bm6r"]
[ext_resource type="Script" uid="uid://cmjqi5k774bbd" path="res://Scripts/ToolAssistantWireGroupSegment.cs" id="2_pyx2u"]
[ext_resource type="Texture2D" uid="uid://bxfit3wxmgp0e" path="res://Icons/ExitWhite.svg" id="3_pyx2u"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_pyx2u"]
content_margin_left = 10.0
content_margin_top = 10.0
content_margin_right = 10.0
content_margin_bottom = 10.0
bg_color = Color(0.139003, 0.147991, 0.188416, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pyx2u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3dssy"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_s5wqp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nppow"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_u7bld"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_dntmt"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_crjd2"]
bg_color = Color(1, 1, 1, 0.114)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rgn44"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_iaq0h"]
bg_color = Color(1, 1, 1, 0.168)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_q783s"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_45e4d"]

[node name="WireGroupSegment" type="PanelContainer" node_paths=PackedStringArray("WireDiameterLine", "QuantityLine", "ExitButton", "ColorButton")]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 82.0
grow_horizontal = 2
theme = ExtResource("1_4bm6r")
theme_override_styles/panel = SubResource("StyleBoxFlat_pyx2u")
script = ExtResource("2_pyx2u")
WireDiameterLine = NodePath("HBoxContainer/VBoxContainer2/WireDiameterLine")
QuantityLine = NodePath("HBoxContainer/VBoxContainer3/QuantityLine")
ExitButton = NodePath("HBoxContainer/ExitButton")
ColorButton = NodePath("HBoxContainer/ColorButton")

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 2
theme_override_constants/separation = 10

[node name="ColorButton" type="ColorPickerButton" parent="HBoxContainer"]
custom_minimum_size = Vector2(25, 0)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_pyx2u")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_3dssy")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_s5wqp")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_nppow")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_u7bld")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dntmt")
theme_override_styles/hover = SubResource("StyleBoxFlat_crjd2")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_rgn44")
theme_override_styles/pressed = SubResource("StyleBoxFlat_iaq0h")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_q783s")
theme_override_styles/normal = SubResource("StyleBoxEmpty_45e4d")
flat = true
color = Color(0.340467, 0.665028, 0.823906, 1)

[node name="VBoxContainer2" type="VBoxContainer" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="HBoxContainer/VBoxContainer2"]
layout_mode = 2
text = "Wire Diameter"

[node name="WireDiameterLine" type="LineEdit" parent="HBoxContainer/VBoxContainer2"]
layout_mode = 2

[node name="VBoxContainer3" type="VBoxContainer" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="HBoxContainer/VBoxContainer3"]
layout_mode = 2
text = "Quantity"

[node name="QuantityLine" type="LineEdit" parent="HBoxContainer/VBoxContainer3"]
layout_mode = 2

[node name="ExitButton" type="Button" parent="HBoxContainer"]
layout_mode = 2
icon = ExtResource("3_pyx2u")
flat = true
