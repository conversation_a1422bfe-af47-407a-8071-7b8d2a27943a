<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 19.76 19.9">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: #d1d2d4;
        stroke-linecap: round;
        stroke-linejoin: round;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer_1">
    <g>
      <polyline class="cls-1" points="16.48 11.29 19.26 11.29 9.88 .5 .5 11.29 3.28 11.29"/>
      <path class="cls-1" d="M12.26,19.4v-4.44c0-.7-.56-1.26-1.26-1.26h-2.24c-.7,0-1.26.56-1.26,1.26v4.44"/>
      <polyline class="cls-1" points="16.48 11.29 16.48 19.4 12.26 19.4"/>
      <polyline class="cls-1" points="3.28 11.29 3.28 19.4 7.5 19.4"/>
    </g>
  </g>
</svg>