using Godot;
using System;
using System.Collections.Generic;

// This class holds the live simulation data for a circle.
public class Circle
{
    public Vector2 Position;
    public float Radius;
    public Color Color;
}

public partial class CirclePacker : Control
{
    [ExportGroup("Circle Properties")]
    // If CustomRadii is empty, random circles will be generated.
    [Export] public float[] CustomRadii { get; set; } = new float[0];
    [Export] public Color[] CustomColors { get; set; } = new Color[0];
    [Export(PropertyHint.Range, "1.0, 100.0, 0.1")] public float ScaleFactor { get; set; } = 10.0f;
    [Export] public int CircleCount { get; set; } = 100;
    [Export] public float MinRadius { get; set; } = 5.0f;
    [Export] public float MaxRadius { get; set; } = 35.0f;

    [ExportGroup("Simulation Settings")]
    [Export(PropertyHint.Range, "1,20,1")] public int SimulationIterations { get; set; } = 5;
    [Export(PropertyHint.Range, "0.1,2.0,0.01")] public float PushFactor { get; set; } = 0.5f;
    [Export(PropertyHint.Range, "0.01, 2.0, 0.001")] public float TighteningForce { get; set; } = 0.05f;

    [ExportGroup("UI Settings")]
    [Export] public HSlider AirGapSlider { get; set; }
    [Export] public Button TightenButton { get; set; }
    [Export] public Button ResetButton { get; set; }
    [Export] public Camera2D Camera { get; set; }


    public float AirGap { get; private set; } = 1.0f;

    [Export] private NodePath _airGapLabelPath;
    [Export] private NodePath _boundingRadiusLabelPath;
    private Label _airGapLabel;
    private LineEdit _boundingRadiusLabel;
    
    private bool _isTighteningEnabled = false;
    
    private List<Circle> _circles = new List<Circle>();
    private float _boundingRadius = 0f;

    public override void _Ready()
    {
        InitializeCircles();

        if (_airGapLabelPath != null)
        {
            _airGapLabel = GetNode<Label>(_airGapLabelPath);
            UpdateAirGapLabel(AirGap);
        }

        if (_boundingRadiusLabelPath != null)
        {
            _boundingRadiusLabel = GetNode<LineEdit>(_boundingRadiusLabelPath);
            UpdateBoundingRadiusLabel(_boundingRadius);
        }

        // Check if UI nodes are assigned before connecting signals
        if (AirGapSlider != null) AirGapSlider.ValueChanged += SetAirGap;
        if (TightenButton != null) TightenButton.Pressed += OnTightenButtonToggled;
        if (ResetButton != null) ResetButton.Pressed += Reset;
    }

    public override void _PhysicsProcess(double delta)
    {
        for (int i = 0; i < SimulationIterations; i++)
        {
            ResolveCollisions();

            if (_isTighteningEnabled)
            {
                ApplyTighteningForce();
            }

            EnforceContainmentAndUpdateBounds();
        }
        QueueRedraw();
    }

    public override void _Draw()
    {
        var center = GetViewportRect().Size / 2;
        DrawCircle(center, _boundingRadius, new Color(1, 1, 1, 0.35f), true, -1, true);
        
        foreach (var circle in _circles)
        {
            DrawCircle(center + circle.Position, circle.Radius, circle.Color, true, -1, true);
        }
    }
    
    // --- Public Methods for UI Signal Connections ---

    /// <summary>
    /// Resets the positions of the CURRENT circles, not the ones from the inspector.
    /// </summary>
    public void Reset()
    {
        GD.Print("Resetting positions of current circles!");
        var rng = new Random();
        foreach (var circle in _circles)
        {
            var angle = (float)(rng.NextDouble() * Math.PI * 2);
            var dist = (float)(rng.NextDouble() * 50.0);
            circle.Position = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * dist;
        }
    }

    /// <summary>
    /// Call this method from other scripts to add a circle at runtime.
    /// The input radius will be scaled by the ScaleFactor.
    /// </summary>
    /// <param name="radius">The unscaled radius of the new circle.</param>
    /// <param name="color">The color of the new circle.</param>
    public void AddCircle(float radius, Color color)
    {
        var rng = new Random();
        var angle = (float)(rng.NextDouble() * Math.PI * 2);
        var dist = (float)(rng.NextDouble() * 50.0);

        _circles.Add(new Circle
        {
            Radius = radius * ScaleFactor, // Apply scale factor here
            Position = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * dist,
            Color = color
        });
        
        GD.Print($"Added a new circle with unscaled radius {radius} (scaled to {radius * ScaleFactor})");
    }

    public void ClearCircles()
    {
        _circles.Clear();
    }

    public void SetAirGap(double value)
    {
        AirGap = (float)value;
        UpdateAirGapLabel((float)value);
    }

    public void OnTightenButtonToggled()
    {
        if (TightenButton != null)
        {
            _isTighteningEnabled = TightenButton.ButtonPressed;
            GD.Print($"Tightening enabled: {_isTighteningEnabled}");

            if (TightenButton.ButtonPressed)
            {
                TightenButton.Text = "Stop";
            }
            else
            {
                TightenButton.Text = "Play";
            }
        }
    }
    
    private void UpdateAirGapLabel(float value)
    {
        if (_airGapLabel != null)
        {
            _airGapLabel.Text = $"Air Gap: {value:0.00}";
        }
    }

    private void UpdateBoundingRadiusLabel(float value)
    {
        if (_boundingRadiusLabel != null)
        {
            // Display the unscaled radius for real-world reference
            _boundingRadiusLabel.Text = $"Bounding Radius: {value / ScaleFactor:0.000}";
        }
    }

    /// <summary>
    /// This method is now only for the INITIAL setup.
    /// </summary>
    private void InitializeCircles()
    {
        _circles.Clear();
        var rng = new Random();

        if (CustomRadii != null && CustomRadii.Length > 0)
        {
            // --- Use user-provided parallel arrays ---
            for (int i = 0; i < CustomRadii.Length; i++)
            {
                var angle = (float)(rng.NextDouble() * Math.PI * 2);
                var dist = (float)(rng.NextDouble() * 50.0);
                
                Color color = (CustomColors != null && i < CustomColors.Length) 
                    ? CustomColors[i] 
                    : Color.FromHsv((float)rng.NextDouble(), 0.7f, 0.9f);

                _circles.Add(new Circle
                {
                    Radius = CustomRadii[i] * ScaleFactor, // Apply scale factor here
                    Position = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * dist,
                    Color = color
                });
            }
        }
        else
        {
            // --- FALLBACK: Generate random circles ---
            for (int i = 0; i < CircleCount; i++)
            {
                var angle = (float)(rng.NextDouble() * Math.PI * 2);
                var dist = (float)(rng.NextDouble() * 50.0);
                _circles.Add(new Circle
                {
                    // Note: Min/Max radius are now treated as unscaled values
                    Radius = (float)(rng.NextDouble() * (MaxRadius - MinRadius) + MinRadius) * ScaleFactor, // Apply scale factor here
                    Position = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * dist,
                    Color = Color.FromHsv((float)rng.NextDouble(), 0.7f, 0.9f)
                });
            }
        }
    }

    private void ApplyTighteningForce()
    {
        if (_circles.Count < 2) return;

        // 1. Find the maximum radius in the current set to use for normalization.
        float maxRadius = 0f;
        foreach (var circle in _circles)
        {
            if (circle.Radius > maxRadius)
            {
                maxRadius = circle.Radius;
            }
        }

        if (maxRadius == 0) return; // Avoid division by zero

        foreach (var circle in _circles)
        {
            Vector2 directionToOrigin = Vector2.Zero - circle.Position;
            if (directionToOrigin.LengthSquared() > 0.001f)
            {
                // 2. Normalize the current circle's radius (value from 0 to 1).
                float normalizedRadius = circle.Radius / maxRadius;

                // 3. Create a weighted force.
                float weightedForce = TighteningForce * (1.0f + normalizedRadius);

                circle.Position += directionToOrigin.Normalized() * weightedForce;
            }
        }
    }

    private void ResolveCollisions()
    {
        for (int i = 0; i < _circles.Count; i++)
        {
            for (int j = i + 1; j < _circles.Count; j++)
            {
                var c1 = _circles[i];
                var c2 = _circles[j];

                Vector2 axis = c1.Position - c2.Position;
                float distance = axis.Length();
                float requiredSeparation = c1.Radius + c2.Radius + AirGap;

                if (distance < requiredSeparation)
                {
                    float overlap = requiredSeparation - distance;
                    Vector2 pushVector = axis.Normalized() * overlap * PushFactor;
                    c1.Position += pushVector;
                    c2.Position -= pushVector;
                }
            }
        }
    }

    private void EnforceContainmentAndUpdateBounds()
    {
        if (_circles.Count == 0)
        {
            _boundingRadius = 0;
            UpdateBoundingRadiusLabel(_boundingRadius);
            return;
        }

        // --- Center the entire cluster first using a true center of mass ---
        Vector2 weightedCenter = Vector2.Zero;
        float totalArea = 0f;
        foreach (var circle in _circles)
        {
            float area = circle.Radius * circle.Radius;
            weightedCenter += circle.Position * area;
            totalArea += area;
        }

        if (totalArea > 0)
        {
            weightedCenter /= totalArea;
        }

        if (weightedCenter.LengthSquared() > 0.001f)
        {
            for (int i = 0; i < _circles.Count; i++)
            {
                _circles[i].Position -= weightedCenter;
            }
        }

        // --- THEN, calculate the bounds ---
        float maxDist = 0f;
        foreach (var circle in _circles)
        {
            float distFromCenter = circle.Position.Length();
            maxDist = Mathf.Max(maxDist, distFromCenter + circle.Radius);
        }
        _boundingRadius = maxDist;
        
        UpdateBoundingRadiusLabel(_boundingRadius);

        // --- Safeguard loop ---
        foreach (var circle in _circles)
        {
            float distFromCenter = circle.Position.Length();
            if (distFromCenter + circle.Radius > _boundingRadius)
            {
                Vector2 directionToCenter = circle.Position.Normalized();
                circle.Position = directionToCenter * (_boundingRadius - circle.Radius);
            }
        }
    }
}
