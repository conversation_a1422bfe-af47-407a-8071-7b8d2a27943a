[gd_resource type="Theme" load_steps=66 format=3 uid="uid://c2xpmfb8cwrgn"]

[ext_resource type="FontFile" uid="uid://dg6qs6ursn3qb" path="res://Fonts/Roboto-Regular.ttf" id="1_ouao7"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kv65r"]
content_margin_left = 14.0
content_margin_top = 14.0
content_margin_right = 14.0
content_margin_bottom = 14.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_711s5"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.261, 0.280914, 0.305804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k6l6x"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.181922, 0.198184, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_01xn8"]
content_margin_left = 9.0
content_margin_top = 6.0
content_margin_right = 9.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3
border_color = Color(0.212878, 0.226466, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mqmd2"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.177348, 0.195643, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h5xys"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.125333, 0.140079, 0.158509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kq03c"]
content_margin_left = 24.0
content_margin_top = 6.0
content_margin_right = 0.0
content_margin_bottom = 6.0
bg_color = Color(0.109333, 0.122196, 0.138274, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_hll2p"]
content_margin_left = 12.0
content_margin_top = 7.2
content_margin_right = 12.0
content_margin_bottom = 7.2
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dcfsl"]
content_margin_left = 12.0
content_margin_top = 7.2
content_margin_right = 12.0
content_margin_bottom = 7.2
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uiad3"]
content_margin_left = 9.0
content_margin_top = 6.0
content_margin_right = 9.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1sxxe"]
content_margin_left = 12.0
content_margin_top = 9.0
content_margin_right = 12.0
content_margin_bottom = 9.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_sh018"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7b6lo"]
content_margin_left = 12.0
content_margin_top = 9.0
content_margin_right = 12.0
content_margin_bottom = 9.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.29293, 0.31528, 0.343215, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_vswa4"]
content_margin_left = 12.0
content_margin_top = 9.0
content_margin_right = 12.0
content_margin_bottom = 9.0
bg_color = Color(0.29293, 0.31528, 0.343215, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.335504, 0.361103, 0.393098, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gqd2v"]
content_margin_left = 12.0
content_margin_top = 9.0
content_margin_right = 12.0
content_margin_bottom = 9.0
bg_color = Color(0.202687, 0.220805, 0.243451, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.250356, 0.269458, 0.293333, 1)
border_blend = true
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dxwid"]
content_margin_left = 9.0
content_margin_top = 3.0
content_margin_right = 9.0
content_margin_bottom = 3.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6u0gn"]
content_margin_left = 14.0
content_margin_top = 14.0
content_margin_right = 14.0
content_margin_bottom = 14.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_x2a5j"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.186495, 0.200724, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qakf4"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.218426, 0.235091, 0.255921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_je4t5"]
content_margin_left = 12.0
content_margin_top = 6.0
content_margin_right = 12.0
content_margin_bottom = 6.0
bg_color = Color(0.121333, 0.135608, 0.153451, 1)
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bx0ey"]
content_margin_left = 12.0
content_margin_top = 6.0
content_margin_right = 12.0
content_margin_bottom = 6.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1ni2p"]
content_margin_left = 12.0
content_margin_top = 12.0
content_margin_right = 12.0
content_margin_bottom = 12.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_msxit"]
content_margin_left = 0.0
content_margin_top = 7.2
content_margin_right = 0.0
content_margin_bottom = 7.2
bg_color = Color(0.181922, 0.198184, 0.218509, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_i82na"]
content_margin_left = 9.0
content_margin_top = 6.0
content_margin_right = 9.0
content_margin_bottom = 6.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_egrk0"]
content_margin_left = 9.0
content_margin_top = 6.0
content_margin_right = 9.0
content_margin_bottom = 6.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_dkrsl"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_44k6j"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_swfvm"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3
border_color = Color(1, 1, 1, 0.07)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ctoo2"]
content_margin_left = 12.0
content_margin_top = 9.0
content_margin_right = 12.0
content_margin_bottom = 9.0
bg_color = Color(0, 0, 0, 0.3)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_rux83"]
content_margin_left = 9.0
content_margin_top = 6.0
content_margin_right = 9.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_left = 1.5
expand_margin_top = 1.5
expand_margin_right = 1.5
expand_margin_bottom = 1.5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kxbic"]
content_margin_left = 9.0
content_margin_top = 6.0
content_margin_right = 9.0
content_margin_bottom = 6.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
draw_center = false
border_color = Color(1, 1, 1, 0)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_left = 1.5
expand_margin_top = 1.5
expand_margin_right = 1.5
expand_margin_bottom = 1.5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5vyo7"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.231938, 0.259226, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2rk15"]
content_margin_left = 6.0
content_margin_top = 3.0
content_margin_right = 6.0
content_margin_bottom = 3.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_r1jc0"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.256496, 0.272869, 0.293333, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6xhs0"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.463615, 0.4877, 0.517804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_v6yi0"]
content_margin_left = 0.0
content_margin_top = 6.75
content_margin_right = 0.0
content_margin_bottom = 6.75
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_re7yf"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(1, 1, 1, 0.04)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_58bqw"]
content_margin_left = 12.0
content_margin_top = 12.0
content_margin_right = 12.0
content_margin_bottom = 12.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_jrqur"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.111333, 0.124432, 0.140804, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8rqeh"]
content_margin_left = 12.0
content_margin_top = 6.0
content_margin_right = 12.0
content_margin_bottom = 6.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_k2084"]
content_margin_left = 12.0
content_margin_top = 4.5
content_margin_right = 12.0
content_margin_bottom = 4.5
bg_color = Color(0.0853331, 0.0953728, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qygfl"]
content_margin_left = 12.0
content_margin_top = 4.5
content_margin_right = 12.0
content_margin_bottom = 4.5
bg_color = Color(0.101333, 0.113255, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_26erj"]
content_margin_left = 12.0
content_margin_top = 4.5
content_margin_right = 12.0
content_margin_bottom = 4.5
bg_color = Color(0, 0, 0, 0.1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxLine" id="StyleBoxLine_2xsss"]
color = Color(0.161157, 0.175563, 0.193568, 1)
grow_begin = -9.0
grow_end = -9.0
thickness = 2

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_n4x4x"]
content_margin_left = 14.0
content_margin_top = 14.0
content_margin_right = 14.0
content_margin_bottom = 14.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1c2dg"]
content_margin_left = 14.0
content_margin_top = 14.0
content_margin_right = 14.0
content_margin_bottom = 14.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)
shadow_color = Color(0, 0, 0, 0.3)
shadow_size = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4h2fv"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.0875919, 0.0966276, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_top = 3.0
expand_margin_bottom = 3.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1flmk"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.223452, 0.243426, 0.268392, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4
expand_margin_top = 3.0
expand_margin_bottom = 3.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7qa65"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="PlaceholderTexture2D" id="PlaceholderTexture2D_m5ykw"]
size = Vector2(0, 0)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_lmcqk"]
content_margin_left = 21.0
content_margin_top = 12.0
content_margin_right = 21.0
content_margin_bottom = 9.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_width_top = 3
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f028s"]
content_margin_left = 21.0
content_margin_top = 12.0
content_margin_right = 21.0
content_margin_bottom = 9.0
bg_color = Color(0.113333, 0.126667, 0.143333, 1)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_kjdif"]
content_margin_left = 21.0
content_margin_top = 12.0
content_margin_right = 21.0
content_margin_bottom = 9.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
border_width_top = 3
border_color = Color(0.25, 0.356509, 0.5, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8kufp"]
content_margin_left = 21.0
content_margin_top = 12.0
content_margin_right = 21.0
content_margin_bottom = 9.0
bg_color = Color(1, 1, 1, 0)
border_color = Color(0.337255, 0.619608, 1, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oss8c"]
content_margin_left = 9.0
content_margin_top = 9.0
content_margin_right = 9.0
content_margin_bottom = 9.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_46qep"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.0933331, 0.104314, 0.118039, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ytoxk"]
content_margin_left = 21.0
content_margin_top = 12.0
content_margin_right = 21.0
content_margin_bottom = 9.0
bg_color = Color(0.119333, 0.133373, 0.150921, 1)
border_width_top = 3
border_color = Color(0.25, 0.356509, 0.5, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_m5r8g"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.104015, 0.114745, 0.128157, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_s8xc4"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.207782, 0.223636, 0.243451, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4263u"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(1, 1, 1, 0.04)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1sjld"]
content_margin_left = 0.0
content_margin_top = 0.0
content_margin_right = 0.0
content_margin_bottom = 0.0
bg_color = Color(0.250356, 0.269458, 0.293333, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gsit0"]
content_margin_left = 12.0
content_margin_top = 12.0
content_margin_right = 12.0
content_margin_bottom = 12.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0aj1m"]
content_margin_left = 6.0
content_margin_top = 6.0
content_margin_right = 6.0
content_margin_bottom = 6.0
bg_color = Color(0.0853331, 0.0953728, 0.107921, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_c5wou"]
content_margin_left = 6.75
content_margin_top = 0.0
content_margin_right = 6.75
content_margin_bottom = 0.0
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[resource]
default_font = ExtResource("1_ouao7")
default_font_size = 16
AcceptDialog/styles/panel = SubResource("StyleBoxFlat_kv65r")
AnimationBezierTrackEdit/colors/focus_color = Color(1, 1, 1, 0)
AnimationBezierTrackEdit/colors/h_line_color = Color(1, 1, 1, 0.12)
AnimationBezierTrackEdit/colors/track_focus_color = Color(1, 1, 1, 0.1)
AnimationBezierTrackEdit/colors/v_line_color = Color(1, 1, 1, 0)
AnimationTimelineEdit/colors/font_primary_color = Color(1, 1, 1, 0.7)
AnimationTimelineEdit/colors/font_secondary_color = Color(1, 1, 1, 0.4)
AnimationTimelineEdit/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTimelineEdit/colors/v_line_primary_color = Color(1, 1, 1, 0.4)
AnimationTimelineEdit/colors/v_line_secondary_color = Color(1, 1, 1, 0.08)
AnimationTimelineEdit/constants/text_primary_margin = 4
AnimationTimelineEdit/constants/text_secondary_margin = 3
AnimationTimelineEdit/constants/v_line_primary_margin = 6
AnimationTimelineEdit/constants/v_line_primary_width = 3
AnimationTimelineEdit/constants/v_line_secondary_margin = 9
AnimationTimelineEdit/constants/v_line_secondary_width = 2
AnimationTimelineEdit/styles/time_available = SubResource("StyleBoxFlat_711s5")
AnimationTimelineEdit/styles/time_unavailable = SubResource("StyleBoxFlat_k6l6x")
AnimationTrackEdit/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTrackEdit/constants/h_separation = 9
AnimationTrackEdit/styles/focus = SubResource("StyleBoxFlat_01xn8")
AnimationTrackEdit/styles/hover = SubResource("StyleBoxFlat_mqmd2")
AnimationTrackEdit/styles/odd = SubResource("StyleBoxFlat_h5xys")
AnimationTrackEditGroup/colors/bg_color = Color(0.125333, 0.140079, 0.158509, 1)
AnimationTrackEditGroup/colors/h_line_color = Color(1, 1, 1, 0)
AnimationTrackEditGroup/colors/v_line_color = Color(1, 1, 1, 0)
AnimationTrackEditGroup/constants/h_separation = 12
AnimationTrackEditGroup/styles/header = SubResource("StyleBoxFlat_kq03c")
BottomPanelButton/styles/hover = SubResource("StyleBoxFlat_hll2p")
BottomPanelButton/styles/hover_pressed = SubResource("StyleBoxFlat_dcfsl")
BottomPanelButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
BottomPanelButton/styles/pressed = SubResource("StyleBoxFlat_hll2p")
Button/colors/font_color = Color(1, 1, 1, 0.7)
Button/colors/font_disabled_color = Color(1, 1, 1, 0.3)
Button/colors/font_focus_color = Color(1, 1, 1, 1)
Button/colors/font_hover_color = Color(1, 1, 1, 1)
Button/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
Button/colors/font_pressed_color = Color(1, 1, 1, 1)
Button/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
Button/colors/icon_normal_color = Color(1, 1, 1, 0.7)
Button/constants/outline_size = 0
Button/styles/disabled = SubResource("StyleBoxFlat_1sxxe")
Button/styles/disabled_mirrored = SubResource("StyleBoxFlat_1sxxe")
Button/styles/focus = SubResource("StyleBoxFlat_sh018")
Button/styles/hover = SubResource("StyleBoxFlat_7b6lo")
Button/styles/hover_mirrored = SubResource("StyleBoxFlat_7b6lo")
Button/styles/hover_pressed = SubResource("StyleBoxFlat_vswa4")
Button/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_vswa4")
Button/styles/normal = SubResource("StyleBoxFlat_gqd2v")
Button/styles/normal_mirrored = SubResource("StyleBoxFlat_gqd2v")
Button/styles/pressed = SubResource("StyleBoxFlat_vswa4")
Button/styles/pressed_mirrored = SubResource("StyleBoxFlat_vswa4")
CheckBox/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckBox/colors/font_pressed_color = Color(1, 1, 1, 0.7)
CheckBox/styles/normal = SubResource("StyleBoxFlat_dxwid")
CheckBox/styles/normal_mirrored = SubResource("StyleBoxFlat_dxwid")
CheckButton/colors/font_focus_color = Color(1, 1, 1, 0.7)
CheckButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
CheckButton/colors/font_pressed_color = Color(1, 1, 1, 0.7)
Editor/colors/background = Color(0.109333, 0.122196, 0.138274, 1)
Editor/colors/box_selection_fill_color = Color(1, 1, 1, 0.12)
Editor/colors/box_selection_stroke_color = Color(1, 1, 1, 0.4)
Editor/colors/dark_color_2 = Color(0, 0, 0, 0.3)
Editor/colors/dark_color_3 = Color(0.109333, 0.122196, 0.138274, 1)
Editor/colors/forward_plus_color = Color(0.54902, 0.752941, 0.392157, 1)
Editor/colors/gl_compatibility_color = Color(0.447059, 0.698039, 0.890196, 1)
Editor/colors/mobile_color = Color(0.862745, 0.482353, 0.584314, 1)
Editor/colors/prop_subsection = Color(1, 1, 1, 0)
Editor/colors/property_color_w = Color(1, 1, 1, 0.8)
Editor/colors/property_color_x = Color(0.882353, 0.384314, 0.466667, 1)
Editor/colors/property_color_y = Color(0.764706, 0.937255, 0.396078, 1)
Editor/colors/property_color_z = Color(0.415686, 0.670588, 0.964706, 1)
Editor/colors/warning_color = Color(0.831373, 0.780392, 0.623529, 1)
Editor/constants/top_bar_separation = 6
Editor/constants/window_border_margin = 6
EditorAbout/styles/panel = SubResource("StyleBoxFlat_6u0gn")
EditorAudioBus/styles/focus = SubResource("StyleBoxFlat_x2a5j")
EditorAudioBus/styles/master = SubResource("StyleBoxFlat_qakf4")
EditorAudioBus/styles/normal = SubResource("StyleBoxFlat_x2a5j")
EditorHelpBitContent/styles/normal = SubResource("StyleBoxFlat_je4t5")
EditorHelpBitTitle/styles/normal = SubResource("StyleBoxFlat_bx0ey")
EditorInspector/constants/v_separation = 5
EditorInspector/styles/panel = SubResource("StyleBoxFlat_1ni2p")
EditorInspectorCategory/styles/bg = SubResource("StyleBoxFlat_msxit")
EditorInspectorSection/constants/h_separation = 6
EditorLogFilterButton/styles/hover = SubResource("StyleBoxFlat_i82na")
EditorLogFilterButton/styles/normal = SubResource("StyleBoxFlat_sh018")
EditorLogFilterButton/styles/pressed = SubResource("StyleBoxFlat_egrk0")
EditorProperty/colors/property_color = Color(1, 1, 1, 0.6)
EditorProperty/colors/warning_color = Color(0.831373, 0.780392, 0.623529, 1)
EditorProperty/styles/bg = SubResource("StyleBoxFlat_sh018")
EditorProperty/styles/bg_selected = SubResource("StyleBoxFlat_sh018")
EditorProperty/styles/child_bg = SubResource("StyleBoxFlat_dkrsl")
EditorSettingsDialog/styles/panel = SubResource("StyleBoxFlat_6u0gn")
EditorSpinSlider/styles/label_bg = SubResource("StyleBoxFlat_44k6j")
EditorStyles/colors/movie_writer_icon_hover = Color(1, 1, 1, 0.8)
EditorStyles/colors/movie_writer_icon_hover_pressed = Color(1, 1, 1, 0.8)
EditorStyles/colors/movie_writer_icon_normal = Color(1, 1, 1, 0.7)
EditorStyles/colors/movie_writer_icon_pressed = Color(1, 1, 1, 0.941176)
EditorStyles/styles/FocusViewport = SubResource("StyleBoxFlat_swfvm")
EditorStyles/styles/Information3dViewport = SubResource("StyleBoxFlat_ctoo2")
EditorStyles/styles/LaunchPadMovieMode = SubResource("StyleBoxFlat_rux83")
EditorStyles/styles/LaunchPadNormal = SubResource("StyleBoxFlat_kxbic")
EditorStyles/styles/MovieWriterButtonPressed = SubResource("StyleBoxFlat_5vyo7")
EditorValidationPanel/styles/panel = SubResource("StyleBoxFlat_2rk15")
FlatButton/colors/font_color = Color(1, 1, 1, 0.7)
FlatButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
FlatButton/colors/font_focus_color = Color(1, 1, 1, 1)
FlatButton/colors/font_hover_color = Color(1, 1, 1, 1)
FlatButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
FlatButton/colors/font_pressed_color = Color(1, 1, 1, 1)
FlatButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
FlatButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
FlatButton/styles/disabled = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/hover = SubResource("StyleBoxFlat_egrk0")
FlatButton/styles/hover_mirrored = SubResource("StyleBoxFlat_egrk0")
FlatButton/styles/hover_pressed = SubResource("StyleBoxFlat_i82na")
FlatButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_i82na")
FlatButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatButton/styles/pressed = SubResource("StyleBoxFlat_i82na")
FlatButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_i82na")
FlatMenuButton/colors/font_color = Color(1, 1, 1, 0.7)
FlatMenuButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
FlatMenuButton/colors/font_focus_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_hover_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/font_pressed_color = Color(1, 1, 1, 1)
FlatMenuButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
FlatMenuButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
FlatMenuButton/styles/disabled = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/focus = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/hover = SubResource("StyleBoxFlat_egrk0")
FlatMenuButton/styles/hover_mirrored = SubResource("StyleBoxFlat_egrk0")
FlatMenuButton/styles/hover_pressed = SubResource("StyleBoxFlat_i82na")
FlatMenuButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_i82na")
FlatMenuButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
FlatMenuButton/styles/pressed = SubResource("StyleBoxFlat_i82na")
FlatMenuButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_i82na")
GraphStateMachine/colors/focus_color = Color(1, 1, 1, 0)
HBoxContainer/constants/separation = 3
HScrollBar/styles/grabber = SubResource("StyleBoxFlat_r1jc0")
HScrollBar/styles/grabber_highlight = SubResource("StyleBoxFlat_6xhs0")
HScrollBar/styles/grabber_pressed = SubResource("StyleBoxFlat_6xhs0")
HScrollBar/styles/scroll = SubResource("StyleBoxFlat_v6yi0")
HScrollBar/styles/scroll_focus = SubResource("StyleBoxFlat_v6yi0")
HSplitContainer/constants/autohide = 1
HSplitContainer/constants/minimum_grab_thickness = 9
HSplitContainer/constants/separation = 3
InspectorActionButton/constants/h_separation = 12
InspectorActionButton/styles/disabled = SubResource("StyleBoxFlat_1sxxe")
InspectorActionButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_1sxxe")
InspectorActionButton/styles/hover = SubResource("StyleBoxFlat_7b6lo")
InspectorActionButton/styles/hover_mirrored = SubResource("StyleBoxFlat_7b6lo")
InspectorActionButton/styles/normal = SubResource("StyleBoxFlat_gqd2v")
InspectorActionButton/styles/normal_mirrored = SubResource("StyleBoxFlat_gqd2v")
InspectorActionButton/styles/pressed = SubResource("StyleBoxFlat_vswa4")
InspectorActionButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_vswa4")
ItemList/colors/guide_color = Color(1, 1, 1, 0)
ItemList/constants/v_separation = 9
ItemList/styles/cursor = SubResource("StyleBoxFlat_re7yf")
ItemList/styles/cursor_unfocused = SubResource("StyleBoxFlat_re7yf")
ItemList/styles/focus = SubResource("StyleBoxFlat_sh018")
ItemList/styles/hovered = SubResource("StyleBoxFlat_egrk0")
ItemList/styles/hovered_selected = SubResource("StyleBoxFlat_egrk0")
ItemList/styles/hovered_selected_focus = SubResource("StyleBoxFlat_egrk0")
ItemList/styles/panel = SubResource("StyleBoxFlat_58bqw")
ItemList/styles/selected = SubResource("StyleBoxFlat_egrk0")
ItemList/styles/selected_focus = SubResource("StyleBoxFlat_egrk0")
ItemListSecondary/styles/panel = SubResource("StyleBoxFlat_jrqur")
Label/colors/font_color = Color(1, 1, 1, 0.7)
Label/styles/normal = SubResource("StyleBoxFlat_8rqeh")
LineEdit/colors/font_placeholder_color = Color(1, 1, 1, 0.4)
LineEdit/styles/focus = SubResource("StyleBoxFlat_k2084")
LineEdit/styles/normal = SubResource("StyleBoxFlat_qygfl")
LineEdit/styles/read_only = SubResource("StyleBoxFlat_26erj")
MainMenuBar/styles/hover = SubResource("StyleBoxFlat_egrk0")
MainMenuBar/styles/hover_pressed = SubResource("StyleBoxFlat_i82na")
MainMenuBar/styles/normal = SubResource("StyleBoxFlat_uiad3")
MainMenuBar/styles/pressed = SubResource("StyleBoxFlat_i82na")
MainScreenButton/styles/hover = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/hover_mirrored = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/hover_pressed = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/pressed = SubResource("StyleBoxFlat_uiad3")
MainScreenButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/disabled = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/focus = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/hover = SubResource("StyleBoxFlat_egrk0")
MenuButton/styles/hover_mirrored = SubResource("StyleBoxFlat_egrk0")
MenuButton/styles/hover_pressed = SubResource("StyleBoxFlat_egrk0")
MenuButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_egrk0")
MenuButton/styles/normal = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/normal_mirrored = SubResource("StyleBoxFlat_uiad3")
MenuButton/styles/pressed = SubResource("StyleBoxFlat_i82na")
MenuButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_i82na")
OptionButton/colors/font_color = Color(1, 1, 1, 0.7)
OptionButton/colors/font_disabled_color = Color(1, 1, 1, 0.3)
OptionButton/colors/font_focus_color = Color(1, 1, 1, 1)
OptionButton/colors/font_hover_color = Color(1, 1, 1, 1)
OptionButton/colors/font_hover_pressed_color = Color(1, 1, 1, 1)
OptionButton/colors/font_pressed_color = Color(1, 1, 1, 1)
OptionButton/colors/icon_disabled_color = Color(1, 1, 1, 0.3)
OptionButton/colors/icon_normal_color = Color(1, 1, 1, 0.7)
OptionButton/constants/arrow_margin = 14
OptionButton/styles/disabled = SubResource("StyleBoxFlat_1sxxe")
OptionButton/styles/disabled_mirrored = SubResource("StyleBoxFlat_1sxxe")
OptionButton/styles/focus = SubResource("StyleBoxFlat_sh018")
OptionButton/styles/hover = SubResource("StyleBoxFlat_7b6lo")
OptionButton/styles/hover_mirrored = SubResource("StyleBoxFlat_7b6lo")
OptionButton/styles/hover_pressed = SubResource("StyleBoxFlat_vswa4")
OptionButton/styles/hover_pressed_mirrored = SubResource("StyleBoxFlat_vswa4")
OptionButton/styles/normal = SubResource("StyleBoxFlat_gqd2v")
OptionButton/styles/normal_mirrored = SubResource("StyleBoxFlat_gqd2v")
OptionButton/styles/pressed = SubResource("StyleBoxFlat_vswa4")
OptionButton/styles/pressed_mirrored = SubResource("StyleBoxFlat_vswa4")
PanelContainer/styles/panel = SubResource("StyleBoxFlat_uiad3")
PopupDialog/styles/panel = SubResource("StyleBoxFlat_kv65r")
PopupMenu/constants/item_start_padding = 14
PopupMenu/constants/v_separation = 10
PopupMenu/styles/hover = SubResource("StyleBoxFlat_egrk0")
PopupMenu/styles/labeled_separator_left = SubResource("StyleBoxLine_2xsss")
PopupMenu/styles/labeled_separator_right = SubResource("StyleBoxLine_2xsss")
PopupMenu/styles/panel = SubResource("StyleBoxFlat_n4x4x")
PopupMenu/styles/separator = SubResource("StyleBoxLine_2xsss")
PopupPanel/styles/panel = SubResource("StyleBoxFlat_1c2dg")
ProgressBar/styles/background = SubResource("StyleBoxFlat_4h2fv")
ProgressBar/styles/fill = SubResource("StyleBoxFlat_1flmk")
ProjectSettingsEditor/styles/panel = SubResource("StyleBoxFlat_6u0gn")
RichTextLabel/styles/normal = SubResource("StyleBoxFlat_7qa65")
ScrollContainer/styles/focus = SubResource("StyleBoxFlat_sh018")
ScrollContainer/styles/panel = SubResource("StyleBoxFlat_sh018")
SplitContainer/constants/minimum_grab_thickness = 9
SplitContainer/constants/separation = 4
SplitContainer/icons/h_grabber = SubResource("PlaceholderTexture2D_m5ykw")
SplitContainer/icons/v_grabber = SubResource("PlaceholderTexture2D_m5ykw")
TabBar/styles/tab_focus = SubResource("StyleBoxFlat_lmcqk")
TabBar/styles/tab_hovered = SubResource("StyleBoxFlat_f028s")
TabBar/styles/tab_selected = SubResource("StyleBoxFlat_kjdif")
TabBar/styles/tab_unselected = SubResource("StyleBoxFlat_8kufp")
TabContainer/styles/panel = SubResource("StyleBoxFlat_oss8c")
TabContainer/styles/tab_focus = SubResource("StyleBoxFlat_lmcqk")
TabContainer/styles/tab_hovered = SubResource("StyleBoxFlat_f028s")
TabContainer/styles/tab_selected = SubResource("StyleBoxFlat_kjdif")
TabContainer/styles/tab_unselected = SubResource("StyleBoxFlat_8kufp")
TabContainer/styles/tabbar_background = SubResource("StyleBoxFlat_46qep")
TabContainerOdd/styles/tab_selected = SubResource("StyleBoxFlat_ytoxk")
TextEdit/styles/focus = SubResource("StyleBoxFlat_k2084")
TextEdit/styles/normal = SubResource("StyleBoxFlat_qygfl")
TextEdit/styles/read_only = SubResource("StyleBoxFlat_26erj")
TooltipPanel/styles/panel = SubResource("StyleBoxFlat_m5r8g")
Tree/colors/drop_position_color = Color(1, 1, 1, 0.4)
Tree/colors/font_color = Color(1, 1, 1, 0.7)
Tree/colors/guide_color = Color(1, 1, 1, 0)
Tree/colors/parent_hl_line_color = Color(1, 1, 1, 0.1)
Tree/constants/children_hl_line_width = 0
Tree/constants/draw_guides = 0
Tree/constants/draw_relationship_lines = 1
Tree/constants/inner_item_margin_left = 6
Tree/constants/inner_item_margin_right = 6
Tree/constants/parent_hl_line_width = 2
Tree/constants/relationship_line_width = 0
Tree/constants/v_separation = 15
Tree/font_sizes/title_button_font_size = 14
Tree/styles/button_hover = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/button_pressed = SubResource("StyleBoxFlat_i82na")
Tree/styles/cursor = SubResource("StyleBoxFlat_4263u")
Tree/styles/cursor_unfocused = SubResource("StyleBoxFlat_4263u")
Tree/styles/custom_button_hover = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/custom_button_pressed = SubResource("StyleBoxFlat_i82na")
Tree/styles/focus = SubResource("StyleBoxFlat_sh018")
Tree/styles/hover = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/hovered = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/hovered_dimmed = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/hovered_selected = SubResource("StyleBoxFlat_1sjld")
Tree/styles/hovered_selected_focus = SubResource("StyleBoxFlat_1sjld")
Tree/styles/panel = SubResource("StyleBoxFlat_gsit0")
Tree/styles/selected = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/selected_focus = SubResource("StyleBoxFlat_s8xc4")
Tree/styles/title_button_hover = SubResource("StyleBoxFlat_0aj1m")
Tree/styles/title_button_normal = SubResource("StyleBoxFlat_0aj1m")
Tree/styles/title_button_pressed = SubResource("StyleBoxFlat_0aj1m")
TreeSecondary/styles/panel = SubResource("StyleBoxFlat_jrqur")
VBoxContainer/constants/separation = 3
VScrollBar/styles/grabber = SubResource("StyleBoxFlat_r1jc0")
VScrollBar/styles/grabber_highlight = SubResource("StyleBoxFlat_6xhs0")
VScrollBar/styles/grabber_pressed = SubResource("StyleBoxFlat_6xhs0")
VScrollBar/styles/scroll = SubResource("StyleBoxFlat_c5wou")
VScrollBar/styles/scroll_focus = SubResource("StyleBoxFlat_c5wou")
VSplitContainer/constants/autohide = 1
VSplitContainer/constants/minimum_grab_thickness = 9
VSplitContainer/constants/separation = 3
