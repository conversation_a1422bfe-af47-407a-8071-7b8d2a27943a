[gd_scene load_steps=4 format=3 uid="uid://d2xp2unhpvxno"]

[ext_resource type="Script" uid="uid://oknnmhfpesxl" path="res://Scripts/DataContainer.cs" id="1_f0n6d"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_f0n6d"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6idld"]

[node name="DataContainer" type="PanelContainer" node_paths=PackedStringArray("DataRowContainer", "DataContainerPanel", "checkBox", "label")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxEmpty_f0n6d")
script = ExtResource("1_f0n6d")
DataRowContainer = NodePath("HBoxContainer")
DataContainerPanel = NodePath("HBoxContainer/DataContainerPanel")
checkBox = NodePath("HBoxContainer/CheckBox")
label = NodePath("HBoxContainer/Label")

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="CheckBox" type="CheckBox" parent="HBoxContainer"]
layout_mode = 2
flat = true

[node name="Label" type="Label" parent="HBoxContainer"]
layout_mode = 2
text = "Default"

[node name="DataContainerPanel" type="PanelContainer" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxEmpty_6idld")
