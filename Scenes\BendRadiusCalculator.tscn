[gd_scene load_steps=6 format=3 uid="uid://buy3iiff6m2rr"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_34d8v"]
[ext_resource type="Script" uid="uid://bx07r5aqjj01g" path="res://Scripts/MainController.cs" id="1_fobm0"]
[ext_resource type="Script" uid="uid://7d82rk4bmfrc" path="res://Scripts/VisualizationDrawer.cs" id="2_34d8v"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_yy6cy"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_34d8v"]
bg_color = Color(0.0843835, 0.0908839, 0.120145, 1)

[node name="Main" type="PanelContainer" node_paths=PackedStringArray("WireTypeDropdown", "AngleDropdown", "AngleSlider", "AngleValueLabel", "QuantitySpinBox", "BendRadiusLabel", "ArcLengthLabel", "TotalLengthLabel", "VisualizationAreaNode")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -502.0
offset_bottom = -210.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_34d8v")
script = ExtResource("1_fobm0")
WireTypeDropdown = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer3/WireTypeDropdown")
AngleDropdown = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer4/AngleDropdown")
AngleSlider = NodePath("PanelContainer/VBoxContainer/MarginContainer/AngleSlider")
AngleValueLabel = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/AngleValueLabel")
QuantitySpinBox = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer2/QuantitySpinBox")
BendRadiusLabel = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/BendRadiusLabel")
ArcLengthLabel = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/ArcLengthLabel")
TotalLengthLabel = NodePath("PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/TotalLengthLabel")
VisualizationAreaNode = NodePath("PanelContainer/VBoxContainer/HBoxContainer/PanelContainer/VisualizationArea")

[node name="PanelContainer" type="PanelContainer" parent="."]
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_yy6cy")

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer"]
layout_mode = 2

[node name="HBoxContainer" type="HSplitContainer" parent="PanelContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="MarginContainer" type="MarginContainer" parent="PanelContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.0
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="UILayout" type="VBoxContainer" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 10

[node name="HBoxContainer" type="HBoxContainer" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="Label" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer"]
layout_mode = 2
text = "Inches"

[node name="CheckButton" type="CheckButton" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer"]
layout_mode = 2
flat = true

[node name="Label2" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer"]
layout_mode = 2
text = "Millimeters"

[node name="Label" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2
text = "Select largest gauge wire"

[node name="HBoxContainer3" type="HBoxContainer" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="Label" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer3"]
layout_mode = 2
text = "Wire Type: "

[node name="WireTypeDropdown" type="OptionButton" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer4" type="HBoxContainer" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="Label" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer4"]
layout_mode = 2
text = "Angle:"

[node name="AngleDropdown" type="OptionButton" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer4"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer2" type="HBoxContainer" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="Label" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer2"]
layout_mode = 2
text = "Wire Count:"

[node name="QuantitySpinBox" type="SpinBox" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3

[node name="AngleValueLabel" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="BendRadiusLabel" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="ArcLengthLabel" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
layout_mode = 2

[node name="TotalLengthLabel" type="Label" parent="PanelContainer/VBoxContainer/HBoxContainer/MarginContainer/UILayout"]
visible = false
layout_mode = 2

[node name="PanelContainer" type="PanelContainer" parent="PanelContainer/VBoxContainer/HBoxContainer"]
clip_contents = true
custom_minimum_size = Vector2(400, 600)
layout_mode = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_34d8v")

[node name="VisualizationArea" type="Control" parent="PanelContainer/VBoxContainer/HBoxContainer/PanelContainer"]
layout_mode = 2
script = ExtResource("2_34d8v")
VisualArcCenterlineRadiusPx = 150.0
VisualStraightLenPx = 200.0
VisualWireThicknessPx = 15.0
DrawingOrigin = Vector2(150, 175)
WireColor = Color(0.945098, 0.352941, 0.160784, 1)
DimensionColor = Color(0.359069, 0.359069, 0.359069, 1)
TextPadding = 50.0

[node name="MarginContainer" type="MarginContainer" parent="PanelContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="AngleSlider" type="HSlider" parent="PanelContainer/VBoxContainer/MarginContainer"]
layout_mode = 2
value = 17.0
