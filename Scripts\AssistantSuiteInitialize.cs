using Godot;
using System;
using System.Threading.Tasks;

public partial class AssistantSuiteInitialize : PanelContainer
{
    CustomSignals customSignals;
    SQLHandler sqlHandler;
    ConnectorAssistantGlobalData globalData;
    [Export]
    public ProgressBar progressBar;
    [Export]
    public Label label;
    [Export]
    public RichTextLabel richText;
    [Export]
    public Light2D light;

    public override void _Ready()
    {
        ConnectSignals();
        TweenLight();
        TweenProgressBar();
    }
    public void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        globalData = GetNode<ConnectorAssistantGlobalData>("/root/ConnectorAssistantGlobalData");
    }
    public void TweenLight()
    {
        Tween tween = GetTree().CreateTween(); ;
        tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(light, "energy", 0.75f, 3.0f);
    }

    public async Task TweenProgressBar()
    {
        Tween tween = GetTree().CreateTween();
        tween.SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(progressBar, "value", 100.0f, 3.0f);
        await ToSignal(tween, "finished");
        OnDataLoadComplete();
    }

    public void OnDataLoadComplete()
    {
        GetTree().ChangeSceneToFile("res://Scenes/Main.tscn");
    }

}
