[gd_scene load_steps=5 format=3 uid="uid://crfqd783ewyhm"]

[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_htaeo"]
[ext_resource type="Script" uid="uid://dqe3fn53ndfyy" path="res://Scripts/PopupWindow.cs" id="2_t4pb4"]
[ext_resource type="Script" uid="uid://dw2yyivwn3kum" path="res://Scripts/ProfileScreen.cs" id="3_n50w1"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t4pb4"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[node name="ProfileScene" type="Window"]
title = "Profile"
initial_position = 1
size = Vector2i(400, 400)
theme = ExtResource("1_htaeo")
script = ExtResource("2_t4pb4")

[node name="ProfileScreen" type="PanelContainer" parent="." node_paths=PackedStringArray("UsernameLine", "AccountTypeLine", "RequestElevatedAccessButton")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_htaeo")
theme_override_styles/panel = SubResource("StyleBoxFlat_t4pb4")
script = ExtResource("3_n50w1")
UsernameLine = NodePath("MarginContainer/VBoxContainer/HBoxContainer2/UsernameLine")
AccountTypeLine = NodePath("MarginContainer/VBoxContainer/HBoxContainer3/AccountTypeLine")
RequestElevatedAccessButton = NodePath("MarginContainer/VBoxContainer/RequestElevatedAccess")

[node name="MarginContainer" type="MarginContainer" parent="ProfileScreen"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="ProfileScreen/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 15

[node name="HBoxContainer" type="HBoxContainer" parent="ProfileScreen/MarginContainer/VBoxContainer"]
visible = false
layout_mode = 2

[node name="Label" type="Label" parent="ProfileScreen/MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 6
text = "Profile"

[node name="HBoxContainer2" type="HBoxContainer" parent="ProfileScreen/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 4

[node name="Label" type="Label" parent="ProfileScreen/MarginContainer/VBoxContainer/HBoxContainer2"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "Username:"

[node name="UsernameLine" type="LineEdit" parent="ProfileScreen/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
editable = false

[node name="HBoxContainer3" type="HBoxContainer" parent="ProfileScreen/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="ProfileScreen/MarginContainer/VBoxContainer/HBoxContainer3"]
custom_minimum_size = Vector2(120, 0)
layout_mode = 2
text = "Account Type:"

[node name="AccountTypeLine" type="LineEdit" parent="ProfileScreen/MarginContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3
editable = false

[node name="RequestElevatedAccess" type="Button" parent="ProfileScreen/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 10
text = "Request Elevated Access"
