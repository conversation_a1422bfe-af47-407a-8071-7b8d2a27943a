[gd_scene load_steps=6 format=3 uid="uid://chl62atkxyjm3"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_4evg5"]
[ext_resource type="PackedScene" uid="uid://c64qqrmhahd1q" path="res://Scenes/ToolAssistantWireBundleCalculator.tscn" id="2_wnonw"]
[ext_resource type="PackedScene" uid="uid://buy3iiff6m2rr" path="res://Scenes/BendRadiusCalculator.tscn" id="3_4evg5"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5rjmq"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_leol6"]
bg_color = Color(0.133333, 0.14902, 0.168627, 1)
draw_center = false
border_width_left = 5
border_width_top = 5
border_width_right = 5
border_width_bottom = 5
border_color = Color(0.945098, 0.352941, 0.160784, 1)
border_blend = true

[node name="ToolAssistantMain" type="PanelContainer"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme = ExtResource("1_4evg5")
theme_override_styles/panel = SubResource("StyleBoxFlat_5rjmq")

[node name="FlowContainer" type="FlowContainer" parent="."]
layout_mode = 2

[node name="PanelContainer2" type="PanelContainer" parent="FlowContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="VBoxContainer" type="VBoxContainer" parent="FlowContainer/PanelContainer2"]
layout_mode = 2

[node name="PanelContainer" type="PanelContainer" parent="FlowContainer/PanelContainer2/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="FlowContainer/PanelContainer2/VBoxContainer/PanelContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Bundle Diameter Calculator"
horizontal_alignment = 1

[node name="WireBundleCalculator" parent="FlowContainer/PanelContainer2/VBoxContainer" instance=ExtResource("2_wnonw")]
layout_mode = 2
size_flags_vertical = 3

[node name="PanelContainer3" type="PanelContainer" parent="FlowContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="VBoxContainer" type="VBoxContainer" parent="FlowContainer/PanelContainer3"]
layout_mode = 2

[node name="PanelContainer" type="PanelContainer" parent="FlowContainer/PanelContainer3/VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="FlowContainer/PanelContainer3/VBoxContainer/PanelContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Bend Radius Calculator"
horizontal_alignment = 1

[node name="Main" parent="FlowContainer/PanelContainer3/VBoxContainer" instance=ExtResource("3_4evg5")]
layout_mode = 2
size_flags_vertical = 3

[node name="PanelContainer" type="PanelContainer" parent="FlowContainer"]
visible = false
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_leol6")

[node name="Button" type="Button" parent="FlowContainer/PanelContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
text = "Add"
