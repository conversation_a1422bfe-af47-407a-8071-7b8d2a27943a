using Godot;
using System;
using Global;
using System.Data;

public partial class HelpScene : PanelContainer
{
    [ExportGroup("Bug Form")]
    [Export] LineEdit BugID;
    [Export] FlowContainer BugCategoryContainer;
    [Export] OptionButton BugPrioritySelection;
    [Export] TextEdit BugDescription;
    [Export] CheckBox BugEmailCheckbox;
    [Export] Button BugSubmitButton;
    [Export] Label BugErrorLabel;

    [ExportGroup("Request Form")]
    [Export] LineEdit RequestID;
    [Export] FlowContainer RequestCategoryContainer;
    [Export] OptionButton RequestAssistantSelection;
    [Export] TextEdit RequestDescription;
    [Export] CheckBox RequestEmailCheckbox;
    [Export] Button RequestSubmitButton;
    [Export] Label RequestErrorLabel;

    private CustomSignals customSignals;
    private SQLHandler sqlHandler;
    private ConnectorAssistantGlobalData globalData;

    public override void _Ready()
    {
        ConnectSignals();
        GetBugID();
    }
    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
        globalData = GetNode<ConnectorAssistantGlobalData>("/root/ConnectorAssistantGlobalData");

        foreach (var child in RequestCategoryContainer.GetChildren())
        {
            if (child is Button)
            {
                (child as Button).Pressed += OnRequestCategoryPressed;
            }
        }

        foreach (var child in BugCategoryContainer.GetChildren())
        {
            if (child is Button)
            {
                (child as Button).Pressed += OnBugCategoryPressed;
            }
        }

        RequestSubmitButton.Pressed += OnRequestSubmitButtonPressed;
        BugSubmitButton.Pressed += OnBugSubmitButtonPressed;
    }

    private void OnBugSubmitButtonPressed()
    {
        BuildBugQuery();
        GetBugID();
    }


    private void OnRequestSubmitButtonPressed()
    {
        BuildRequestQuery();
        GetRequestID();
    }


    private void OnBugCategoryPressed()
    {
        foreach (var child in BugCategoryContainer.GetChildren())
        {
            if (child is Button)
            {
                if ((child as Button).HasFocus())
                {
                    continue;
                }
                (child as Button).SetPressedNoSignal(false);
            }
        }
    }


    private void OnRequestCategoryPressed()
    {
        foreach (var child in RequestCategoryContainer.GetChildren())
        {
            if (child is Button)
            {
                if ((child as Button).HasFocus())
                {
                    continue;
                }
                (child as Button).SetPressedNoSignal(false);
            }
        }
    }

    private void BuildBugQuery()
    {
        var ID = BugID.Text;
        var Priority = BugPrioritySelection.GetItemText(BugPrioritySelection.Selected);
        var Description = BugDescription.Text;
        var Reporter = globalData.UserName;
        var Category = "";

        foreach (var child in BugCategoryContainer.GetChildren())
        {
            if (child is Button)
            {
                if ((child as Button).IsPressed())
                {
                    Category = (child as Button).Text;
                }
            }
        }

        if (Category == "")
        {
            UpdateError(0, BugErrorLabel);
            return;
        }

        if (Priority == "")
        {
            UpdateError(1, BugErrorLabel);
            return;
        }

        if (Description == "")
        {
            UpdateError(2, BugErrorLabel);
            return;
        }

        if (BugEmailCheckbox.IsPressed())
        {
            string body = @$"
            Connector Assistant Bug Report%0A
            {ID}%0A
            Category: {Category}%0A
            Priority: {Priority}%0A
            Reporter: {Reporter}%0A
            Description: {Description}";
            GlobalFunctions.Email("<EMAIL>", "Connector Assistant Bug Report", body);
        }

        try
        {
            sqlHandler.AddToSysTestTable(@$"
            INSERT INTO dbo.BugReports (Category, Priority, Description, Status, DateCreated, Reporter)
            VALUES ('{Category}', '{Priority}', '{Description}', 'Open', CURRENT_TIMESTAMP, '{Reporter}');");

            UpdateError(3, BugErrorLabel);
        }
        catch
        {
            UpdateError(4, BugErrorLabel);
        }

    }

    private void GetBugID()
    {
        string ID = sqlHandler.SysTestQuery("SELECT MAX(ID) FROM dbo.BugReports;");

        GD.Print(ID);
        BugID.Text = "ID: " + (ID.ToInt() + 1).ToString();
        GD.Print(BugID.Text);
    }

    private void GetRequestID()
    {
        string ID = sqlHandler.SysTestQuery("SELECT MAX(ID) FROM dbo.RequestReports;");

        GD.Print(ID);
        RequestID.Text = "ID: " + (ID.ToInt() + 1).ToString();
        GD.Print(RequestID.Text);
    }

    private void UpdateError(int errorCode, Label ErrorLabel)
    {
        ErrorLabel.Visible = true;
        switch (errorCode)
        {
            case 0:
                ErrorLabel.Text = "Please select a category.";
                break;
            case 1:
                ErrorLabel.Text = "Please select a priority.";
                break;
            case 2:
                ErrorLabel.Text = "Please enter a description.";
                break;
            case 3:
                ErrorLabel.Text = "Report submitted successfully.";
                break;
            case 4:
                ErrorLabel.Text = "Database error. Could not submit Bug Report.";
                break;
            case 5:
                ErrorLabel.Visible = false;
                break;
        }
    }

    private void BuildRequestQuery()
    {
        var ID = RequestID.Text;
        var Assistant = RequestAssistantSelection.GetItemText(RequestAssistantSelection.Selected);
        var Description = RequestDescription.Text;
        var Reporter = globalData.UserName;
        var Category = "";

        foreach (var child in RequestCategoryContainer.GetChildren())
        {
            if (child is Button)
            {
                if ((child as Button).IsPressed())
                {
                    Category = (child as Button).Text;
                }
            }
        }

        if (Category == "")
        {
            UpdateError(0, RequestErrorLabel);
            return;
        }
        if (Description == "")
        {
            UpdateError(2, RequestErrorLabel);
            return;
        }

        if (RequestEmailCheckbox.IsPressed())
        {
            string body = @$"
            Connector Assistant Request%0A
            {ID}%0A
            Assistant: {Assistant}%0A
            Category: {Category}%0A
            Description: {Description}%0A
            Reporter: {Reporter}";
            GlobalFunctions.Email("<EMAIL>", "Connector Assistant Request", body);
        }

        try
        {
            sqlHandler.AddToSysTestTable(@$"
            INSERT INTO dbo.RequestReports (Assistant, Category, Description, Status, DateCreated, Reporter)
            VALUES ('{Assistant}', '{Category}', '{Description}', 'Open', CURRENT_TIMESTAMP, '{Reporter}');");
            
            UpdateError(3, RequestErrorLabel);
        }
        catch
        {
            UpdateError(4, RequestErrorLabel);
        }
    }
}
