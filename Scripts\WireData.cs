// WireData.cs
using Godot;

public partial class WireData : GodotObject
{
    public float MinBendRadiusMultiplier { get; set; }
    public float DiameterMm { get; set; }
    public string DisplayName { get; set; }

    public WireData(string displayName, float diameterMm, float minBendRadiusMultiplier)
    {
        DisplayName = displayName;
        DiameterMm = diameterMm;
        MinBendRadiusMultiplier = minBendRadiusMultiplier;
    }
}
