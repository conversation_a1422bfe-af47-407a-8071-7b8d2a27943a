using Godot;
using System;

public partial class DataContainer : PanelContainer
{
    [Export] public HBoxContainer DataRowContainer { get; set; }
    [Export] public PanelContainer DataContainerPanel { get; set; }
    [Export] public CheckBox checkBox { get; set; }
    [Export] public Label label { get; set; }
    public CustomSignals customSignals { get; set; }
    public string attribute = "";
    public string value = "";
    public string queryString = "";

    public override void _Ready()
    {
        ConnectSignals();
    }

    public void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
    }
    public string GetData()
    {
        queryString = "";
        attribute = label.Text;

        var ValueVariant = DataContainerPanel.GetChild(0);
        if (ValueVariant is Label)
        {
            value = (ValueVariant as Label).Text;
            queryString = $"[{attribute}] = '{value}'";
        }
        else if (ValueVariant is LineEdit)
        {
            value = (ValueVariant as LineEdit).Text;
            queryString = $"[{attribute}] LIKE '%{value}%'";
        }
        else if (ValueVariant is SpinBox)
        {
            value = (ValueVariant as SpinBox).Value.ToString();
            queryString = $"[{attribute}] > {value}";
        }
        else if (ValueVariant is OptionButton)
        {
            value = (ValueVariant as OptionButton).GetItemText((ValueVariant as OptionButton).Selected);
            if (value.IsValidInt())
            {
                queryString = $"[{attribute}] = '{value}'";
            }
            else
            {
                queryString = $"[{attribute}] = '{value}'";
            }
        }
        else if (ValueVariant is CheckBox)
        {
            value = (ValueVariant as CheckBox).ButtonPressed ? "Approved" : "Non-Approved";
            queryString = $"[{attribute}] = '{value}'";
        }
        return queryString;
    }
}
