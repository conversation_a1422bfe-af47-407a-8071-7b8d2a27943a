[gd_scene load_steps=6 format=3 uid="uid://bdivx4tvrpo5h"]

[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_uypgd"]
[ext_resource type="Script" uid="uid://dlvabx3giecx3" path="res://Scripts/ContactsPanel.cs" id="2_ogq5r"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_uypgd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ogq5r"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ngmgf"]

[node name="ContactsPanel" type="PanelContainer" node_paths=PackedStringArray("contactTree")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2
theme = ExtResource("1_uypgd")
script = ExtResource("2_ogq5r")
contactTree = NodePath("Tree")

[node name="Tree" type="Tree" parent="."]
custom_minimum_size = Vector2(200, 100)
layout_mode = 2
theme_override_styles/title_button_hover = SubResource("StyleBoxEmpty_uypgd")
theme_override_styles/title_button_pressed = SubResource("StyleBoxEmpty_ogq5r")
theme_override_styles/title_button_normal = SubResource("StyleBoxEmpty_ngmgf")
select_mode = 1
