[gd_scene load_steps=12 format=3 uid="uid://d1evncqy4gq76"]

[ext_resource type="Theme" uid="uid://bpnl2xcc4rnm2" path="res://Themes/ConnectorAssistantTheme.tres" id="1_sfcpv"]
[ext_resource type="Script" uid="uid://b6bfp4xrw78k" path="res://Scripts/ConnectorAssistantMain.cs" id="3_05mef"]
[ext_resource type="Texture2D" uid="uid://cmeh2k15j2ngc" path="res://Icons/Search.svg" id="4_d726q"]
[ext_resource type="Texture2D" uid="uid://8v7r1nw8yhyc" path="res://Icons/FilterWhite_2.svg" id="4_nl2en"]
[ext_resource type="Script" uid="uid://clwhk1tx1kwrq" path="res://Scripts/ItemTreePanel.cs" id="6_nl2en"]
[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="7_gncu3"]
[ext_resource type="PackedScene" uid="uid://d1kn6a5tc6nyf" path="res://Scenes/ItemPanel.tscn" id="7_nl2en"]
[ext_resource type="Texture2D" uid="uid://d14cwjbmu8htb" path="res://Icons/Good.svg" id="7_pt6ic"]
[ext_resource type="Script" uid="uid://b4p24k5u43aaw" path="res://Scripts/ConnectorAssistantSearchFilter.cs" id="8_lokgf"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mlw1e"]
bg_color = Color(0.113725, 0.121569, 0.156863, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_nl2en"]
bg_color = Color(0.18, 0.465, 0.75, 0.282353)

[node name="ConnectorAssistantMain" type="PanelContainer" node_paths=PackedStringArray("ItemTree", "DatabaseStatusIcon", "DatabaseStatusLabel", "DatabaseErrorLabel", "DatabaseProgressBar", "SearchBar", "SearchButton", "FilterButton", "HelpButton", "SearchFilter", "itemPanel", "SplitContainer")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_sfcpv")
theme_override_styles/panel = SubResource("StyleBoxFlat_mlw1e")
script = ExtResource("3_05mef")
ItemTree = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchTree/ItemTree")
DatabaseStatusIcon = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer/DatabaseStatusIcon")
DatabaseStatusLabel = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer/DatabaseStatusLabel")
DatabaseErrorLabel = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer/VBoxContainer/DatabaseErrorLabel")
DatabaseProgressBar = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer/VBoxContainer/DatabaseProgressBar")
SearchBar = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer/SearchBar")
SearchButton = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer/SearchButton")
FilterButton = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer/FilterButton")
HelpButton = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer/HelpButton")
SearchFilter = NodePath("SearchFilter")
itemPanel = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer/ItemPanel")
SplitContainer = NodePath("VBoxContainer/ConnectorAssistantSearch/HSplitContainer")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="ConnectorAssistantSearch" type="PanelContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="HSplitContainer" type="HSplitContainer" parent="VBoxContainer/ConnectorAssistantSearch"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 0
theme_override_styles/split_bar_background = SubResource("StyleBoxFlat_nl2en")
dragger_visibility = 1

[node name="VBoxContainer" type="VBoxContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="SearchPanel" type="PanelContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer"]
layout_mode = 2

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer"]
layout_mode = 2
size_flags_vertical = 4

[node name="SearchBar" type="LineEdit" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer"]
custom_minimum_size = Vector2(300, 0)
layout_mode = 2
placeholder_text = "Search Part Number"
expand_to_text_length = true
right_icon = ExtResource("4_d726q")

[node name="SearchButton" type="Button" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer"]
layout_mode = 2
text = "Search"

[node name="FilterButton" type="Button" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer"]
layout_mode = 2
icon = ExtResource("4_nl2en")

[node name="HelpButton" type="Button" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchPanel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 10
text = "Add Parts"

[node name="SearchTree" type="PanelContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ItemTree" type="Tree" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/SearchTree"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
select_mode = 1
script = ExtResource("6_nl2en")

[node name="StatusBar" type="PanelContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 8

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer"]
layout_mode = 2

[node name="DatabaseStatusIcon" type="TextureRect" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer"]
layout_mode = 2
texture = ExtResource("7_pt6ic")
stretch_mode = 5

[node name="DatabaseStatusLabel" type="Label" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer"]
layout_mode = 2
text = "Connected to Database"

[node name="VBoxContainer" type="VBoxContainer" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 6

[node name="DatabaseErrorLabel" type="LineEdit" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "Database Status"
editable = false
expand_to_text_length = true

[node name="DatabaseProgressBar" type="ProgressBar" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer/VBoxContainer/StatusBar/MarginContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 4
show_percentage = false

[node name="ItemPanel" parent="VBoxContainer/ConnectorAssistantSearch/HSplitContainer" instance=ExtResource("7_nl2en")]
visible = false
layout_mode = 2
mouse_filter = 1

[node name="SearchFilter" type="Window" parent="." node_paths=PackedStringArray("TypeTree", "AttributeTree", "ValueTree", "connectorAssistantMain", "SubmitButton", "ResetButton", "ValueContainer", "itemPanel")]
title = "Filter"
initial_position = 2
size = Vector2i(1275, 450)
visible = false
min_size = Vector2i(800, 450)
max_size = Vector2i(1920, 1080)
theme = ExtResource("7_gncu3")
script = ExtResource("8_lokgf")
TypeTree = NodePath("PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer/VBoxContainer/TypeTree")
AttributeTree = NodePath("PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer2/VBoxContainer2/AttributeTree")
ValueTree = NodePath("PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3/ParametersContainer/ValueTree")
connectorAssistantMain = NodePath("..")
SubmitButton = NodePath("PanelContainer/MarginContainer/VBoxContainer/MarginContainer5/HBoxContainer2/SubmitButton")
ResetButton = NodePath("PanelContainer/MarginContainer/VBoxContainer/MarginContainer5/HBoxContainer2/Reset")
ValueContainer = NodePath("PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3/ParametersContainer/ScrollContainer/ValueContainer")
itemPanel = NodePath("../VBoxContainer/ConnectorAssistantSearch/HSplitContainer/ItemPanel")

[node name="PanelContainer" type="PanelContainer" parent="SearchFilter"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_mlw1e")

[node name="MarginContainer" type="MarginContainer" parent="SearchFilter/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 15
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 15
theme_override_constants/margin_bottom = 15

[node name="VBoxContainer" type="VBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer"]
layout_mode = 2

[node name="HBoxContainer3" type="HBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="MarginContainer" type="MarginContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3

[node name="VBoxContainer" type="VBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0
text = "Select Part Type"
horizontal_alignment = 1

[node name="TypeTree" type="Tree" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
hide_root = true
select_mode = 1

[node name="VSeparator" type="VSeparator" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2

[node name="MarginContainer2" type="MarginContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3

[node name="VBoxContainer2" type="VBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer2"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer2/VBoxContainer2"]
layout_mode = 2
size_flags_vertical = 0
text = "Select Attributes"
horizontal_alignment = 1

[node name="AttributeTree" type="Tree" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer2/VBoxContainer2"]
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
hide_root = true
select_mode = 1

[node name="VSeparator2" type="VSeparator" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2

[node name="MarginContainer3" type="MarginContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ParametersContainer" type="VBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3/ParametersContainer"]
layout_mode = 2
size_flags_vertical = 0
text = "Refine Search"
horizontal_alignment = 1

[node name="ValueTree" type="Tree" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3/ParametersContainer"]
visible = false
custom_minimum_size = Vector2(500, 200)
layout_mode = 2
size_flags_vertical = 3
select_mode = 1

[node name="ScrollContainer" type="ScrollContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3/ParametersContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ValueContainer" type="VBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/HBoxContainer3/MarginContainer3/ParametersContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 0
theme_override_constants/separation = 5

[node name="MarginContainer4" type="MarginContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer"]
visible = false
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer" type="HBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/MarginContainer4"]
layout_mode = 2
size_flags_horizontal = 4

[node name="Label" type="Label" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/MarginContainer4/HBoxContainer"]
layout_mode = 2
text = "Sort By"

[node name="OptionButton" type="OptionButton" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/MarginContainer4/HBoxContainer"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
size_flags_horizontal = 3

[node name="MarginContainer5" type="MarginContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HBoxContainer2" type="HBoxContainer" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/MarginContainer5"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8

[node name="Reset" type="Button" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/MarginContainer5/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 10
text = "Reset"

[node name="SubmitButton" type="Button" parent="SearchFilter/PanelContainer/MarginContainer/VBoxContainer/MarginContainer5/HBoxContainer2"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 10
text = "Submit"
