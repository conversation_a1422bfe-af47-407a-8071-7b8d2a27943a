// PinNode.cs (Corrected)
using Godot;

public partial class PinNode : Node2D
{
    private string _pinText;
    private float _radius;
    private bool _isSpecial;
    private Label _label;

    // The _Ready() method is no longer needed for this script, so we can remove it.
    // public override void _Ready()
    // {
    // }

    /// <summary>
    /// A public method to set up the pin's data from the parent.
    /// </summary>
    public void Initialize(string pinText, float radius, bool isSpecial)
    {
        // --- THIS IS THE FIX ---
        // Get the Label node reference here, at the beginning of Initialize,
        // instead of in _Ready().
        _label = GetNode<Label>("Label");

        // It's good practice to add a check to make sure it was found.
        if (_label == null)
        {
            GD.PrintErr("PinNode scene is missing its required Label child!");
            return;
        }

        // Now that _label is guaranteed to have a value, the rest of the code will work.
        _pinText = pinText;
        _radius = radius;
        _isSpecial = isSpecial;

        _label.Text = _pinText;
        _label.LabelSettings = new LabelSettings()
        {
            FontSize = 12,
            FontColor = new Color(1, 1, 1, 0.75f)
        };
        _label.Position = new Vector2(6, -20);

        // Tell this node it needs to draw itself
        QueueRedraw();
    }

    public override void _Draw()
    {
        // This part remains the same.
        if (_isSpecial)
        {
            DrawCircle(Vector2.Zero, _radius, new Color(1, 1, 1, 1));
        }
        else
        {
            // Corrected to draw an outline for non-special pins
            DrawCircle(Vector2.Zero, _radius, new Color(1, 1, 1, 1), width: 2.0f);
        }
    }
}