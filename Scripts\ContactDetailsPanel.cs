using Godot;
using System;
using System.Collections.Generic;
using System.Data;

public partial class ContactDetailsPanel : PanelContainer
{
    //-------------------------------------------------------------------------
    #region Helper Classes for Data Organization
    //-------------------------------------------------------------------------

    /// <summary>
    /// A simple class to hold information about a single accessory tool.
    /// </summary>
    public class AccessoryInfo
    {
        public string PartNumber { get; set; }
        public string Type { get; set; }
    }

    /// <summary>
    /// A class to hold the grouped data for a single crimper and its list of accessories.
    /// This makes it easy to pass all relevant info to the UI row scene.
    /// </summary>
    public class CrimperUIData
    {
        public string PartNumber { get; set; }
        public string Type { get; set; }
        public List<AccessoryInfo> Accessories { get; set; } = new List<AccessoryInfo>();
    }

    #endregion

    //-------------------------------------------------------------------------
    #region Exports - Your UI Node Connections
    //-------------------------------------------------------------------------

    [Export] public Label PartNumberLine { get; set; }
    [Export] public LineEdit TypeLine { get; set; }
    [Export] public LineEdit WireRangeLine { get; set; }
    [Export] public LineEdit MatingEndLine { get; set; }
    [Export] public LineEdit WireBarrelLine { get; set; }
    [Export] public LineEdit InsertionToolLine { get; set; }
    [Export] public LineEdit RemovalToolLine { get; set; }
    [Export] public VBoxContainer AssemblyContainer { get; set; }
    [Export] public ColorRect ColorCode1 { get; set; }
    [Export] public ColorRect ColorCode2 { get; set; }
    [Export] public ColorRect ColorCode3 { get; set; }

    #endregion

    //-------------------------------------------------------------------------
    #region Scene Preload
    //-------------------------------------------------------------------------

    // IMPORTANT: Make sure this path points to your unified ContactAssemblyRow scene!
    private PackedScene _contactAssemblyRowScene = GD.Load<PackedScene>("res://Scenes/ContactAssemblyRow.tscn");

    #endregion

    //-------------------------------------------------------------------------
    #region Private Members
    //-------------------------------------------------------------------------

    private CustomSignals customSignals;
    private SQLHandler sqlHandler;

    #endregion

    //-------------------------------------------------------------------------
    #region Godot Lifecycle Methods
    //-------------------------------------------------------------------------

    public override void _Ready()
    {
        ConnectSignals();
        // Using a known-good contact part number for testing.
        // You will replace this with a dynamic value later.
        PopulatePanel("M39029/44-293");
    }

    #endregion

    //-------------------------------------------------------------------------
    #region Private Methods
    //-------------------------------------------------------------------------

    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
    }

    /// <summary>
    /// Queries the database and populates the entire panel with contact details.
    /// </summary>
    public void PopulatePanel(string partNumber)
    {
        PartNumberLine.Text = partNumber;

        DataTable itemTable = sqlHandler.Query(@$"
            DECLARE @ContactPN NVARCHAR(100) = '{partNumber}';
            SELECT DISTINCT
                c.PartNumberCurrent, c.ContactType, c.MatingEndAWG AS MatingEnd,
                c.WireBarrelAWG AS WireBarrel, c.WireRange, c.ColorCode1,
                c.ColorCode2, c.ColorCode3, ins_tool.PartNumber AS InsertionTool,
                ins_type.TypeName AS InsertionToolType, rem_tool.PartNumber AS RemovalTool,
                rem_type.TypeName AS RemovalToolType, crimper.PartNumber AS Crimper,
                crimper_type.TypeName AS CrimperType, accessory.PartNumber AS PositionerOrDie,
                accessory_type.TypeName AS AccessoryType
            FROM Contacts AS c
            LEFT JOIN Tools AS ins_tool ON c.InsertionToolID = ins_tool.ToolID
            LEFT JOIN ToolTypes AS ins_type ON ins_tool.ToolTypeID = ins_type.ToolTypeID
            LEFT JOIN Tools AS rem_tool ON c.RemovalToolID = rem_tool.ToolID
            LEFT JOIN ToolTypes AS rem_type ON rem_tool.ToolTypeID = rem_type.ToolTypeID
            LEFT JOIN CrimperCompatibility AS cc ON c.ContactID = cc.ContactID
            LEFT JOIN Tools AS crimper ON cc.CrimperToolID = crimper.ToolID
            LEFT JOIN ToolTypes AS crimper_type ON crimper.ToolTypeID = crimper_type.ToolTypeID
            LEFT JOIN Tools AS accessory ON cc.AccessoryToolID = accessory.ToolID
            LEFT JOIN ToolTypes AS accessory_type ON accessory.ToolTypeID = accessory_type.ToolTypeID
            WHERE c.PartNumberCurrent = @ContactPN;");

        // --- Phase 1: Populate static contact details from the first row ---
        if (itemTable.Rows.Count > 0)
        {
            DataRow firstRow = itemTable.Rows[0];
            
            string typeResult = firstRow["ContactType"]?.ToString();
            if (typeResult == "P")
            {
                TypeLine.Text = "Contact Type: Pin";
            }
            else if (typeResult == "S")
            {
                TypeLine.Text = "Contact Type: Socket";
            }
            else
            {
                TypeLine.Text = "Contact Type: Unknown";
            }

            WireRangeLine.Text = "Wire Range: " + firstRow["WireRange"]?.ToString();
            MatingEndLine.Text = "Mating End: " + firstRow["MatingEnd"]?.ToString();
            WireBarrelLine.Text = "Wire Barrel: " + firstRow["WireBarrel"]?.ToString();
            InsertionToolLine.Text = firstRow["InsertionTool"]?.ToString();
            RemovalToolLine.Text = firstRow["RemovalTool"]?.ToString();

            ColorCode1.Color = ColorFromHex(firstRow["ColorCode1"]?.ToString());
            ColorCode2.Color = ColorFromHex(firstRow["ColorCode2"]?.ToString());
            ColorCode3.Color = ColorFromHex(firstRow["ColorCode3"]?.ToString());
        }
        else
        {
            GD.PrintErr("No data found for part number: " + partNumber);
            return; // Exit early if no data
        }

        // --- Phase 2: Process and group all crimper data from the result table ---
            var crimperGroups = new Dictionary<string, CrimperUIData>();
        foreach (DataRow row in itemTable.Rows)
        {
            if (row["Crimper"] == DBNull.Value) continue;

            string crimperPN = row["Crimper"].ToString();
            if (!crimperGroups.ContainsKey(crimperPN))
            {
                crimperGroups[crimperPN] = new CrimperUIData
                {
                    PartNumber = crimperPN,
                    Type = row["CrimperType"].ToString()
                };
            }

            crimperGroups[crimperPN].Accessories.Add(new AccessoryInfo
            {
                PartNumber = row["PositionerOrDie"].ToString(),
                Type = row["AccessoryType"].ToString()
            });
        }
        
        // --- Phase 3: Build the UI from the clean, grouped data ---
        foreach (var child in AssemblyContainer.GetChildren())
        {
            child.QueueFree();
        }

        foreach (var crimperData in crimperGroups.Values)
        {
            // Always instantiate the same scene, no more if/else logic here.
            var newRow = _contactAssemblyRowScene.Instantiate<ContactAssemblyRow>();
            newRow.Populate(crimperData); // The row's own script will handle the display logic.
            AssemblyContainer.AddChild(newRow);
        }
    }

    /// <summary>
    /// Helper function to convert a hex string (e.g., #FF00FF) to a Godot Color.
    /// </summary>
    private Color ColorFromHex(string hex)
    {
        if (string.IsNullOrEmpty(hex))
        {
            return new Color(0, 0, 0, 0); // Return transparent if no color
        }
        return new Color(hex);
    }

    #endregion
}