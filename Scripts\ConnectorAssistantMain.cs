using Godot;
using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.IO;
using System.Net;
using System.ComponentModel;

public partial class ConnectorAssistantMain : PanelContainer
{
    [Export] public Tree ItemTree;
    [Export] public TextureRect DatabaseStatusIcon { get; set; }
    [Export] public Label DatabaseStatusLabel { get; set; }
    [Export] public LineEdit DatabaseErrorLabel { get; set; }
    [Export] public ProgressBar DatabaseProgressBar { get; set; }
    [Export] public LineEdit SearchBar { get; set; }
    [Export] public Button SearchButton { get; set; }
    [Export] public Button FilterButton { get; set; }
    [Export] public Button HelpButton { get; set; }
    [Export] public Window SearchFilter { get; set; }
    [Export] public ItemPanel itemPanel { get; set; }
    [Export] public HSplitContainer SplitContainer { get; set; }
    private MainController mainController;
    private CustomSignals customSignals;
    private SQLHandler sqlHandler;
    public ConnectorAssistantGlobalData globalData;
    private DataTable allData;

    public override void _Ready()
    {
        // Setup
        ConnectSignals();
        CheckConnection();
        // await LoadAllData();
        // SetupTreeColumns();
    }
    public void ConnectSignals()
    {
        try
        {
            customSignals = GetNode<CustomSignals>("/root/CustomSignals");
            sqlHandler = GetNode<SQLHandler>("/root/SQLHandler");
            globalData = GetNode<ConnectorAssistantGlobalData>("/root/ConnectorAssistantGlobalData");
        }
        catch (Exception e)
        {
            GD.PrintErr("Error connecting signals: " + e.Message);
        }
        FilterButton.Pressed += OnFilterButtonPressed;
        SearchFilter.CloseRequested += OnSearchFilterCloseRequested;
        SearchBar.TextChanged += OnSearchBarTextChanged;
        ItemTree.ItemSelected += OnItemSelected;
        DatabaseProgressBar.ValueChanged += OnDatabaseProgressBarValueChanged;
        SplitContainer.GetDragAreaControl().MouseEntered += OnSplitContainerMouseEntered;
        SplitContainer.GetDragAreaControl().MouseExited += OnSplitContainerMouseExited;
    }

    private void OnSplitContainerMouseExited()
    {
        SplitContainer.RemoveThemeStyleboxOverride("split_bar_background");
        StyleBoxFlat style = new StyleBoxFlat();
        style.BgColor = new Color(0.18f, 0.465f, 0.75f, 0.282f);
        SplitContainer.AddThemeStyleboxOverride("split_bar_background", style);
    }

    private void OnSplitContainerMouseEntered()
    {
        SplitContainer.RemoveThemeStyleboxOverride("split_bar_background");
        StyleBoxFlat style = new StyleBoxFlat();
        style.BgColor = new Color(0.18f, 0.465f, 0.75f, 0.9f);
        SplitContainer.AddThemeStyleboxOverride("split_bar_background", style);
    }


    private void OnDatabaseProgressBarValueChanged(double value)
    {
        DatabaseProgressBar.Value = value;
    }


    private void OnItemSelected()
    {
        itemPanel.Visible = true;
        itemPanel.PopulatePanel(ItemTree.GetSelected().GetText(0));
        itemPanel.SizeFlagsHorizontal = SizeFlags.ExpandFill;
    }


    private void OnSearchBarTextChanged(string newText)
    {
        
            if (newText == "")
            {
                SearchButton.Disabled = true;
            }
            else
            {
                SearchButton.Disabled = false;
            }

            if (ItemTree.GetSelected() != null)
            {
                ItemTree.DeselectAll();
            }

            if (newText == "")
            {
                foreach (TreeItem item in ItemTree.GetRoot().GetChildren())
                {
                    item.Visible = true;
                }
            }
            else
            {
                foreach (TreeItem item in ItemTree.GetRoot().GetChildren())
                {
                    if (!item.GetText(0).Contains(newText.ToUpper().Substring(2, newText.Length - 2)))
                    {
                        item.Visible = false;
                    }
                    else
                    {
                        item.Visible = true;
                    }
                }
            }
    }


    public void CheckConnection()
    {
        bool result = sqlHandler.CheckConnectionQuery();
        if (result)
        {
            DatabaseStatusIcon.Texture = GD.Load<Texture2D>("res://Icons/Good.svg");
            DatabaseStatusLabel.Text = "Connected to Database";
        }
        else
        {
            DatabaseStatusIcon.Texture = GD.Load<Texture2D>("res://Icons/Caution.svg");
            DatabaseStatusLabel.Text = "Not Connected to Database";
        }
    }
    public Tree SetupTreeColumns(List<string> attributes)
    {
        ItemTree.Clear();
        ItemTree.Columns = attributes.Count;
        ItemTree.ColumnTitlesVisible = true;
        foreach (string attribute in attributes)
        {
            ItemTree.SetColumnTitle(attributes.IndexOf(attribute), attribute);
            ItemTree.SetColumnExpand(attributes.IndexOf(attribute), true);
            ItemTree.SetColumnCustomMinimumWidth(attributes.IndexOf(attribute), 200);
        }
        return ItemTree;
    }
    public async Task LoadAllData()
    {
        allData = await sqlHandler.QueryAsync($"SELECT [PartNumber], [type] FROM parts JOIN part_types ON parts.[index] = part_types.part_index JOIN types ON part_types.type_index = types.[index] ORDER BY [type]");

        foreach (DataRow row in allData.Rows)
        {
            DatabaseProgressBar.Value += 1;
            DatabaseErrorLabel.Text = $"Loading Data: {row["type"]}";
        }
        DatabaseErrorLabel.Text = $"Loaded {allData.Rows.Count} parts";
    }
    public async Task LoadItemTree(string query)
    {
        ItemTree.MouseFilter = MouseFilterEnum.Ignore;
        DataTable ItemData = await sqlHandler.QueryAsync(query);
        int tablerowsize = ItemData.Rows.Count;
        int tablecolsize = ItemData.Columns.Count;
        DatabaseProgressBar.Value = 0;
        DatabaseProgressBar.MaxValue = tablerowsize * tablecolsize;

        ItemTree.Clear();
        TreeItem root = ItemTree.CreateItem();
        ItemTree.HideRoot = true;
        ItemTree.Columns = ItemData.Columns.Count;
        ItemTree.ColumnTitlesVisible = true;
        foreach (DataColumn column in ItemData.Columns)
        {
            ItemTree.SetColumnTitle(ItemData.Columns.IndexOf(column), column.ColumnName);
            ItemTree.SetColumnExpand(ItemData.Columns.IndexOf(column), true);
            ItemTree.SetColumnCustomMinimumWidth(ItemData.Columns.IndexOf(column), 200);
        }

        for (int i = 0; i < tablerowsize; i++)
        {
            DataRow row = ItemData.Rows[i];
            TreeItem item = ItemTree.CreateItem(root);
            foreach (DataColumn column in ItemData.Columns)
            {
                DatabaseStatusIcon.Texture = GD.Load<Texture2D>("res://Icons/Caution.svg");
                DatabaseStatusLabel.Text = $"Loading Data, Please Wait";
                DatabaseProgressBar.Value += 1;
                item.SetText(ItemData.Columns.IndexOf(column), row[column].ToString());
                if (column.ToString() == "Description")
                {
                    item.SetTextAlignment(ItemData.Columns.IndexOf(column), HorizontalAlignment.Left);
                }
                else if (column.ToString() == "PartNumber")
                {
                    item.SetTextAlignment(ItemData.Columns.IndexOf(column), HorizontalAlignment.Left);
                }
                else
                {
                    item.SetTextAlignment(ItemData.Columns.IndexOf(column), HorizontalAlignment.Center);
                }
            }
            // ItemTree.ScrollToItem(item, true);
            if (i % 100 == 0)
            {
                await ToSignal(GetTree().CreateTimer(0.0001), "timeout");
            }


        }

        DatabaseProgressBar.Value = DatabaseProgressBar.MaxValue;
        DatabaseErrorLabel.Text = $"Loaded {tablerowsize} parts";
        DatabaseStatusLabel.Text = "Ready";
        DatabaseStatusIcon.Texture = GD.Load<Texture2D>("res://Icons/Good.svg");
        ItemTree.MouseFilter = MouseFilterEnum.Stop;
    }
    private void OnFilterButtonPressed()
    {
        SearchFilter.Show();
    }
    private void OnSearchFilterCloseRequested()
    {
        SearchFilter.Hide();
    }
}