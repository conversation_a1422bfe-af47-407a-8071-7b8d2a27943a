[gd_scene load_steps=12 format=3 uid="uid://cn5ih51qdlvpp"]

[ext_resource type="Theme" uid="uid://dll8ohandi7wd" path="res://Themes/AssistantThemeSuite.tres" id="1_f3wmf"]
[ext_resource type="Texture2D" uid="uid://bmo8nldjgfrin" path="res://Icons/AssistantSuiteLogox512.png" id="2_0u3ja"]
[ext_resource type="Script" uid="uid://bc5n8fpkk3fvc" path="res://Scripts/AssistantSuiteInitialize.cs" id="2_x157q"]
[ext_resource type="FontFile" uid="uid://dlvu30o86he7u" path="res://Fonts/Roboto-Light.ttf" id="3_x157q"]
[ext_resource type="FontFile" uid="uid://bxifp2f51tlu3" path="res://Fonts/Roboto-Bold.ttf" id="4_htnvl"]
[ext_resource type="Texture2D" uid="uid://x0a0pqx5kxyw" path="res://Icons/E2DLogo.png" id="5_dmeb3"]
[ext_resource type="Texture2D" uid="uid://0jx633fa6f6m" path="res://Images/E2D BootSplash.png" id="7_x157q"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7h5s3"]
bg_color = Color(0.117647, 0.12549, 0.137255, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_0u3ja"]
content_margin_left = 15.0
content_margin_top = 15.0
content_margin_right = 15.0
content_margin_bottom = 15.0
bg_color = Color(0.0759, 0.0821517, 0.11, 0.164706)

[sub_resource type="Gradient" id="Gradient_8wonm"]
interpolation_mode = 2
offsets = PackedFloat32Array(0.169145, 1)
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_tpng5"]
gradient = SubResource("Gradient_8wonm")
width = 2500
height = 2500
use_hdr = true
fill = 1
fill_from = Vector2(0.498576, 0.501424)
fill_to = Vector2(0.814815, 0.139601)

[node name="AssistantSuiteInitialize" type="PanelContainer" node_paths=PackedStringArray("progressBar", "label", "richText", "light")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("1_f3wmf")
theme_override_styles/panel = SubResource("StyleBoxFlat_7h5s3")
script = ExtResource("2_x157q")
progressBar = NodePath("TextureRect/ProgressBar")
label = NodePath("MarginContainer/VBoxContainer/HBoxContainer2/LoadingLabel")
richText = NodePath("MarginContainer/VBoxContainer/RichTextLabel")
light = NodePath("LoadingLight")

[node name="MarginContainer" type="MarginContainer" parent="."]
visible = false
layout_mode = 2
theme_override_constants/margin_left = 50
theme_override_constants/margin_top = 50
theme_override_constants/margin_right = 50

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2
size_flags_vertical = 4
theme_override_constants/separation = 50

[node name="TextureRect2" type="TextureRect" parent="MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 300)
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("2_0u3ja")
expand_mode = 1
stretch_mode = 5

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4

[node name="Label" type="Label" parent="MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("3_x157q")
theme_override_font_sizes/font_size = 64
text = "ASSISTANT"

[node name="Label2" type="Label" parent="MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_fonts/font = ExtResource("4_htnvl")
theme_override_font_sizes/font_size = 64
text = "SUITE"

[node name="HBoxContainer2" type="HBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 10

[node name="LoadingLabel" type="Label" parent="MarginContainer/VBoxContainer/HBoxContainer2"]
visible = false
layout_mode = 2
text = "LOADING DATA"
horizontal_alignment = 2

[node name="RichTextLabel" type="RichTextLabel" parent="MarginContainer/VBoxContainer"]
custom_minimum_size = Vector2(800, 200)
layout_mode = 2
size_flags_horizontal = 4
theme_override_styles/normal = SubResource("StyleBoxFlat_0u3ja")
scroll_following = true

[node name="TextureRect" type="TextureRect" parent="MarginContainer/VBoxContainer"]
modulate = Color(1, 1, 1, 0.0509804)
custom_minimum_size = Vector2(0, 64)
layout_mode = 2
size_flags_vertical = 10
texture = ExtResource("5_dmeb3")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 2
texture = ExtResource("7_x157q")
expand_mode = 1
stretch_mode = 5

[node name="ProgressBar" type="ProgressBar" parent="TextureRect"]
custom_minimum_size = Vector2(1000, 0)
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.9
anchor_right = 0.5
anchor_bottom = 0.907
offset_left = -500.0
offset_top = -6.10352e-05
offset_right = 500.0
offset_bottom = 0.439941
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 2
size_flags_vertical = 4
step = 0.1
show_percentage = false

[node name="LoadingLight" type="PointLight2D" parent="."]
position = Vector2(959, -417)
scale = Vector2(2.1816, 1)
energy = 0.0
texture = SubResource("GradientTexture2D_tpng5")
