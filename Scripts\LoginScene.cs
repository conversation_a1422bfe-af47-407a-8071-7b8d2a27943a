using Godot;
using System;
using System.IO;
using System.DirectoryServices;

public partial class LoginScene : PanelContainer
{
    [Export]
    public Button LoginButton { get; set; }
    [Export]
    public LineEdit UsernameInput { get; set; }
    [Export]
    public LineEdit PasswordInput { get; set; }
    [Export]
    public Label ErrorLabel { get; set; }

    private CustomSignals customSignals;
    private ConnectorAssistantGlobalData globalData;

    public override void _Ready()
    {
        ConnectSignals();
        ErrorLabel.Hide();
        UsernameInput.Text = globalData.UserName;
    }

    private void ConnectSignals()
    {
        customSignals = GetNode<CustomSignals>("/root/CustomSignals");
        globalData = GetNode<ConnectorAssistantGlobalData>("/root/ConnectorAssistantGlobalData");
        LoginButton.Pressed += OnLoginButtonPressed;
        UsernameInput.TextSubmitted += OnUsernameInputSubmitted;
        PasswordInput.TextSubmitted += OnPasswordInputSubmitted;
    }

    private void OnPasswordInputSubmitted(string newText)
    {
        OnLoginButtonPressed();
    }


    private void OnUsernameInputSubmitted(string newText)
    {
        OnLoginButtonPressed();
    }


    private void OnLoginButtonPressed()
    {
        GD.Print("Login button pressed");
        // Implement login logic here
        string username = UsernameInput.Text;
        string password = PasswordInput.Text;

        try
        {
            DirectoryEntry entry = new DirectoryEntry("LDAP://134.223.80.93", username, password);
            ErrorLabel.Text = "Login successful.";
            GD.Print("Login successful.");
            GetParent().QueueFree();
            customSignals.EmitSignal("LoginSuccessful");
        }
        catch
        {
            GD.Print("Invalid credentials");
            ErrorLabel.Text = "Invalid username or password.";
        }
    }
}
